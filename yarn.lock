# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: d3ad7b89d973df059c4e8e6d7c972cbeb1bb2f18f002a3bd04ae0707da214cb06cc06929b65aa2313b9347463df2914772298bae8b1d7973f246bb3f2ab3e8f0
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/code-frame@npm:7.24.7"
  dependencies:
    "@babel/highlight": ^7.24.7
    picocolors: ^1.0.0
  checksum: 830e62cd38775fdf84d612544251ce773d544a8e63df667728cc9e0126eeef14c6ebda79be0f0bc307e8318316b7f58c27ce86702e0a1f5c321d842eb38ffda4
  languageName: node
  linkType: hard

"@babel/code-frame@npm:~7.10.4":
  version: 7.10.4
  resolution: "@babel/code-frame@npm:7.10.4"
  dependencies:
    "@babel/highlight": ^7.10.4
  checksum: feb4543c8a509fe30f0f6e8d7aa84f82b41148b963b826cd330e34986f649a85cb63b2f13dd4effdf434ac555d16f14940b8ea5f4433297c2f5ff85486ded019
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.20.5, @babel/compat-data@npm:^7.22.6, @babel/compat-data@npm:^7.25.2":
  version: 7.25.2
  resolution: "@babel/compat-data@npm:7.25.2"
  checksum: b61bc9da7cfe249f19d08da00f4f0c20550cd9ad5bffcde787c2bf61a8a6fa5b66d92bbd89031f3a6e5495a799a2a2499f2947b6cc7964be41979377473ab132
  languageName: node
  linkType: hard

"@babel/core@npm:^7.11.6, @babel/core@npm:^7.12.3, @babel/core@npm:^7.13.16, @babel/core@npm:^7.14.0, @babel/core@npm:^7.20.0, @babel/core@npm:^7.21.3, @babel/core@npm:^7.23.9":
  version: 7.25.2
  resolution: "@babel/core@npm:7.25.2"
  dependencies:
    "@ampproject/remapping": ^2.2.0
    "@babel/code-frame": ^7.24.7
    "@babel/generator": ^7.25.0
    "@babel/helper-compilation-targets": ^7.25.2
    "@babel/helper-module-transforms": ^7.25.2
    "@babel/helpers": ^7.25.0
    "@babel/parser": ^7.25.0
    "@babel/template": ^7.25.0
    "@babel/traverse": ^7.25.2
    "@babel/types": ^7.25.2
    convert-source-map: ^2.0.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.2.3
    semver: ^6.3.1
  checksum: 9a1ef604a7eb62195f70f9370cec45472a08114e3934e3eaaedee8fd754edf0730e62347c7b4b5e67d743ce57b5bb8cf3b92459482ca94d06e06246ef021390a
  languageName: node
  linkType: hard

"@babel/eslint-parser@npm:^7.18.2, @babel/eslint-parser@npm:^7.20.0":
  version: 7.25.1
  resolution: "@babel/eslint-parser@npm:7.25.1"
  dependencies:
    "@nicolo-ribaudo/eslint-scope-5-internals": 5.1.1-v1
    eslint-visitor-keys: ^2.1.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.11.0
    eslint: ^7.5.0 || ^8.0.0 || ^9.0.0
  checksum: 73207b7e84a58bd6560d29f11cf5c6f9d64a01b9299d4d0a145423a028ea4c402be2fd09228647fdbec14b65a07d4138e751468fd33d9a9363c9698582fa80b5
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.20.0, @babel/generator@npm:^7.25.0, @babel/generator@npm:^7.7.2":
  version: 7.25.0
  resolution: "@babel/generator@npm:7.25.0"
  dependencies:
    "@babel/types": ^7.25.0
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
    jsesc: ^2.5.1
  checksum: bf25649dde4068bff8e387319bf820f2cb3b1af7b8c0cfba0bd90880656427c8bad96cd5cb6db7058d20cffe93149ee59da16567018ceaa21ecaefbf780a785c
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/helper-annotate-as-pure@npm:7.24.7"
  dependencies:
    "@babel/types": ^7.24.7
  checksum: 6178566099a6a0657db7a7fa601a54fb4731ca0b8614fbdccfd8e523c210c13963649bc8fdfd53ce7dd14d05e3dda2fb22dea5b30113c488b9eb1a906d60212e
  languageName: node
  linkType: hard

"@babel/helper-builder-binary-assignment-operator-visitor@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/helper-builder-binary-assignment-operator-visitor@npm:7.24.7"
  dependencies:
    "@babel/traverse": ^7.24.7
    "@babel/types": ^7.24.7
  checksum: 71a6158a9fdebffb82fdc400d5555ba8f2e370cea81a0d578155877bdc4db7d5252b75c43b2fdf3f72b3f68348891f99bd35ae315542daad1b7ace8322b1abcb
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.20.7, @babel/helper-compilation-targets@npm:^7.22.6, @babel/helper-compilation-targets@npm:^7.24.7, @babel/helper-compilation-targets@npm:^7.24.8, @babel/helper-compilation-targets@npm:^7.25.2":
  version: 7.25.2
  resolution: "@babel/helper-compilation-targets@npm:7.25.2"
  dependencies:
    "@babel/compat-data": ^7.25.2
    "@babel/helper-validator-option": ^7.24.8
    browserslist: ^4.23.1
    lru-cache: ^5.1.1
    semver: ^6.3.1
  checksum: aed33c5496cb9db4b5e2d44e26bf8bc474074cc7f7bb5ebe1d4a20fdeb362cb3ba9e1596ca18c7484bcd6e5c3a155ab975e420d520c0ae60df81f9de04d0fd16
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.18.6, @babel/helper-create-class-features-plugin@npm:^7.24.7, @babel/helper-create-class-features-plugin@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/helper-create-class-features-plugin@npm:7.25.0"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.24.7
    "@babel/helper-member-expression-to-functions": ^7.24.8
    "@babel/helper-optimise-call-expression": ^7.24.7
    "@babel/helper-replace-supers": ^7.25.0
    "@babel/helper-skip-transparent-expression-wrappers": ^7.24.7
    "@babel/traverse": ^7.25.0
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: e986c1187e16837b71f12920bd77e672b4bc19ac6dfe30b9d9d515a311c5cc5a085a8e337ac8597b1cb7bd0efdbfcc66f69bf652786c9a022070f9b782deec0d
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.18.6, @babel/helper-create-regexp-features-plugin@npm:^7.24.7, @babel/helper-create-regexp-features-plugin@npm:^7.25.0":
  version: 7.25.2
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.25.2"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.24.7
    regexpu-core: ^5.3.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: df55fdc6a1f3090dd37d91347df52d9322d52affa239543808dc142f8fe35e6787e67d8612337668198fac85826fafa9e6772e6c28b7d249ec94e6fafae5da6e
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.6.1, @babel/helper-define-polyfill-provider@npm:^0.6.2":
  version: 0.6.2
  resolution: "@babel/helper-define-polyfill-provider@npm:0.6.2"
  dependencies:
    "@babel/helper-compilation-targets": ^7.22.6
    "@babel/helper-plugin-utils": ^7.22.5
    debug: ^4.1.1
    lodash.debounce: ^4.0.8
    resolve: ^1.14.2
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 2bba965ea9a4887ddf9c11d51d740ab473bd7597b787d042c325f6a45912dfe908c2d6bb1d837bf82f7e9fa51e6ad5150563c58131d2bb85515e63d971414a9c
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.18.9":
  version: 7.24.7
  resolution: "@babel/helper-environment-visitor@npm:7.24.7"
  dependencies:
    "@babel/types": ^7.24.7
  checksum: 079d86e65701b29ebc10baf6ed548d17c19b808a07aa6885cc141b690a78581b180ee92b580d755361dc3b16adf975b2d2058b8ce6c86675fcaf43cf22f2f7c6
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.24.8":
  version: 7.24.8
  resolution: "@babel/helper-member-expression-to-functions@npm:7.24.8"
  dependencies:
    "@babel/traverse": ^7.24.8
    "@babel/types": ^7.24.8
  checksum: bf923d05d81b06857f4ca4fe9c528c9c447a58db5ea39595bb559eae2fce01a8266173db0fd6a2ec129d7bbbb9bb22f4e90008252f7c66b422c76630a878a4bc
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/helper-module-imports@npm:7.24.7"
  dependencies:
    "@babel/traverse": ^7.24.7
    "@babel/types": ^7.24.7
  checksum: 8ac15d96d262b8940bc469052a048e06430bba1296369be695fabdf6799f201dd0b00151762b56012a218464e706bc033f27c07f6cec20c6f8f5fd6543c67054
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.24.7, @babel/helper-module-transforms@npm:^7.24.8, @babel/helper-module-transforms@npm:^7.25.0, @babel/helper-module-transforms@npm:^7.25.2":
  version: 7.25.2
  resolution: "@babel/helper-module-transforms@npm:7.25.2"
  dependencies:
    "@babel/helper-module-imports": ^7.24.7
    "@babel/helper-simple-access": ^7.24.7
    "@babel/helper-validator-identifier": ^7.24.7
    "@babel/traverse": ^7.25.2
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 282d4e3308df6746289e46e9c39a0870819630af5f84d632559171e4fae6045684d771a65f62df3d569e88ccf81dc2def78b8338a449ae3a94bb421aa14fc367
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/helper-optimise-call-expression@npm:7.24.7"
  dependencies:
    "@babel/types": ^7.24.7
  checksum: 280654eaf90e92bf383d7eed49019573fb35a98c9e992668f701ad099957246721044be2068cf6840cb2299e0ad393705a1981c88c23a1048096a8d59e5f79a3
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.18.6, @babel/helper-plugin-utils@npm:^7.20.2, @babel/helper-plugin-utils@npm:^7.22.5, @babel/helper-plugin-utils@npm:^7.24.7, @babel/helper-plugin-utils@npm:^7.24.8, @babel/helper-plugin-utils@npm:^7.8.0, @babel/helper-plugin-utils@npm:^7.8.3":
  version: 7.24.8
  resolution: "@babel/helper-plugin-utils@npm:7.24.8"
  checksum: 73b1a83ba8bcee21dc94de2eb7323207391715e4369fd55844bb15cf13e3df6f3d13a40786d990e6370bf0f571d94fc31f70dec96c1d1002058258c35ca3767a
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.18.9, @babel/helper-remap-async-to-generator@npm:^7.24.7, @babel/helper-remap-async-to-generator@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/helper-remap-async-to-generator@npm:7.25.0"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.24.7
    "@babel/helper-wrap-function": ^7.25.0
    "@babel/traverse": ^7.25.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 47f3065e43fe9d6128ddb4291ffb9cf031935379265fd13de972b5f241943121f7583efb69cd2e1ecf39e3d0f76f047547d56c3fcc2c853b326fad5465da0bd7
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.24.7, @babel/helper-replace-supers@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/helper-replace-supers@npm:7.25.0"
  dependencies:
    "@babel/helper-member-expression-to-functions": ^7.24.8
    "@babel/helper-optimise-call-expression": ^7.24.7
    "@babel/traverse": ^7.25.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: f669fc2487c22d40b808f94b9c3ee41129484d5ef0ba689bdd70f216ff91e10b6b021d2f8cd37e7bdd700235a2a6ae6622526344f064528190383bf661ac65f8
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/helper-simple-access@npm:7.24.7"
  dependencies:
    "@babel/traverse": ^7.24.7
    "@babel/types": ^7.24.7
  checksum: ddbf55f9dea1900213f2a1a8500fabfd21c5a20f44dcfa957e4b0d8638c730f88751c77f678644f754f1a1dc73f4eb8b766c300deb45a9daad000e4247957819
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.20.0, @babel/helper-skip-transparent-expression-wrappers@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.24.7"
  dependencies:
    "@babel/traverse": ^7.24.7
    "@babel/types": ^7.24.7
  checksum: 11b28fe534ce2b1a67c4d8e51a7b5711a2a0a0cae802f74614eee54cca58c744d9a62f6f60103c41759e81c537d270bfd665bf368a6bea214c6052f2094f8407
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.24.8":
  version: 7.24.8
  resolution: "@babel/helper-string-parser@npm:7.24.8"
  checksum: 39b03c5119216883878655b149148dc4d2e284791e969b19467a9411fccaa33f7a713add98f4db5ed519535f70ad273cdadfd2eb54d47ebbdeac5083351328ce
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/helper-validator-identifier@npm:7.24.7"
  checksum: 6799ab117cefc0ecd35cd0b40ead320c621a298ecac88686a14cffceaac89d80cdb3c178f969861bf5fa5e4f766648f9161ea0752ecfe080d8e89e3147270257
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.24.7, @babel/helper-validator-option@npm:^7.24.8":
  version: 7.24.8
  resolution: "@babel/helper-validator-option@npm:7.24.8"
  checksum: a52442dfa74be6719c0608fee3225bd0493c4057459f3014681ea1a4643cd38b68ff477fe867c4b356da7330d085f247f0724d300582fa4ab9a02efaf34d107c
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/helper-wrap-function@npm:7.25.0"
  dependencies:
    "@babel/template": ^7.25.0
    "@babel/traverse": ^7.25.0
    "@babel/types": ^7.25.0
  checksum: 0095b4741704066d1687f9bbd5370bb88c733919e4275e49615f70c180208148ff5f24ab58d186ce92f8f5d28eab034ec6617e9264590cc4744c75302857629c
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/helpers@npm:7.25.0"
  dependencies:
    "@babel/template": ^7.25.0
    "@babel/types": ^7.25.0
  checksum: 739e3704ff41a30f5eaac469b553f4d3ab02be6ced083f5925851532dfbd9efc5c347728e77b754ed0b262a4e5e384e60932a62c192d338db7e4b7f3adf9f4a7
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.10.4, @babel/highlight@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/highlight@npm:7.24.7"
  dependencies:
    "@babel/helper-validator-identifier": ^7.24.7
    chalk: ^2.4.2
    js-tokens: ^4.0.0
    picocolors: ^1.0.0
  checksum: 5cd3a89f143671c4ac129960024ba678b669e6fc673ce078030f5175002d1d3d52bc10b22c5b916a6faf644b5028e9a4bd2bb264d053d9b05b6a98690f1d46f1
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.13.16, @babel/parser@npm:^7.14.7, @babel/parser@npm:^7.20.0, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.23.9, @babel/parser@npm:^7.25.0, @babel/parser@npm:^7.25.3":
  version: 7.25.3
  resolution: "@babel/parser@npm:7.25.3"
  dependencies:
    "@babel/types": ^7.25.2
  bin:
    parser: ./bin/babel-parser.js
  checksum: b55aba64214fa1d66ccd0d29f476d2e55a48586920d280f88c546f81cbbececc0e01c9d05a78d6bf206e8438b9c426caa344942c1a581eecc4d365beaab8a20e
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:^7.25.3":
  version: 7.25.3
  resolution: "@babel/plugin-bugfix-firefox-class-in-computed-class-key@npm:7.25.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/traverse": ^7.25.3
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: d3dba60f360defe70eb43e35a1b17ea9dd4a99e734249e15be3d5c288019644f96f88d7ff51990118fda0845b4ad50f6d869e0382232b1d8b054d113d4eea7e2
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/plugin-bugfix-safari-class-field-initializer-scope@npm:7.25.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: fd56d1e6435f2c008ca9050ea906ff7eedcbec43f532f2bf2e7e905d8bf75bf5e4295ea9593f060394e2c8e45737266ccbf718050bad2dd7be4e7613c60d1b5b
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@npm:7.25.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 13ed301b108d85867d64226bbc4032b07dd1a23aab68e9e32452c4fe3930f2198bb65bdae9c262c4104bd5e45647bc1830d25d43d356ee9a137edd8d5fab8350
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/helper-skip-transparent-expression-wrappers": ^7.24.7
    "@babel/plugin-transform-optional-chaining": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.13.0
  checksum: 07b92878ac58a98ea1fdf6a8b4ec3413ba4fa66924e28b694d63ec5b84463123fbf4d7153b56cf3cedfef4a3482c082fe3243c04f8fb2c041b32b0e29b4a9e21
  languageName: node
  linkType: hard

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@npm:7.25.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/traverse": ^7.25.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: c8d08b8d6cc71451ad2a50cf7db72ab5b41c1e5e2e4d56cf6837a25a61270abd682c6b8881ab025f11a552d2024b3780519bb051459ebb71c27aed13d9917663
  languageName: node
  linkType: hard

"@babel/plugin-proposal-async-generator-functions@npm:^7.0.0":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-async-generator-functions@npm:7.20.7"
  dependencies:
    "@babel/helper-environment-visitor": ^7.18.9
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-remap-async-to-generator": ^7.18.9
    "@babel/plugin-syntax-async-generators": ^7.8.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 111109ee118c9e69982f08d5e119eab04190b36a0f40e22e873802d941956eee66d2aa5a15f5321e51e3f9aa70a91136451b987fe15185ef8cc547ac88937723
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-properties@npm:^7.13.0, @babel/plugin-proposal-class-properties@npm:^7.18.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-class-properties@npm:7.18.6"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 49a78a2773ec0db56e915d9797e44fd079ab8a9b2e1716e0df07c92532f2c65d76aeda9543883916b8e0ff13606afeffa67c5b93d05b607bc87653ad18a91422
  languageName: node
  linkType: hard

"@babel/plugin-proposal-export-default-from@npm:^7.0.0":
  version: 7.24.7
  resolution: "@babel/plugin-proposal-export-default-from@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-export-default-from": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 527cd85a73f80b8612ed8817982e08d616c4a159579116e7ae2a95ac0fbc601785ac2fe94185b56e10983be3defef383d33ba77313fed681bc6127538e95460c
  languageName: node
  linkType: hard

"@babel/plugin-proposal-logical-assignment-operators@npm:^7.18.0":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-logical-assignment-operators@npm:7.20.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cdd7b8136cc4db3f47714d5266f9e7b592a2ac5a94a5878787ce08890e97c8ab1ca8e94b27bfeba7b0f2b1549a026d9fc414ca2196de603df36fb32633bbdc19
  languageName: node
  linkType: hard

"@babel/plugin-proposal-nullish-coalescing-operator@npm:^7.13.8, @babel/plugin-proposal-nullish-coalescing-operator@npm:^7.18.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-nullish-coalescing-operator@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 949c9ddcdecdaec766ee610ef98f965f928ccc0361dd87cf9f88cf4896a6ccd62fce063d4494778e50da99dea63d270a1be574a62d6ab81cbe9d85884bf55a7d
  languageName: node
  linkType: hard

"@babel/plugin-proposal-numeric-separator@npm:^7.0.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-numeric-separator@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f370ea584c55bf4040e1f78c80b4eeb1ce2e6aaa74f87d1a48266493c33931d0b6222d8cee3a082383d6bb648ab8d6b7147a06f974d3296ef3bc39c7851683ec
  languageName: node
  linkType: hard

"@babel/plugin-proposal-object-rest-spread@npm:^7.20.0":
  version: 7.20.7
  resolution: "@babel/plugin-proposal-object-rest-spread@npm:7.20.7"
  dependencies:
    "@babel/compat-data": ^7.20.5
    "@babel/helper-compilation-targets": ^7.20.7
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-transform-parameters": ^7.20.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1329db17009964bc644484c660eab717cb3ca63ac0ab0f67c651a028d1bc2ead51dc4064caea283e46994f1b7221670a35cbc0b4beb6273f55e915494b5aa0b2
  languageName: node
  linkType: hard

"@babel/plugin-proposal-optional-catch-binding@npm:^7.0.0":
  version: 7.18.6
  resolution: "@babel/plugin-proposal-optional-catch-binding@npm:7.18.6"
  dependencies:
    "@babel/helper-plugin-utils": ^7.18.6
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7b5b39fb5d8d6d14faad6cb68ece5eeb2fd550fb66b5af7d7582402f974f5bc3684641f7c192a5a57e0f59acfae4aada6786be1eba030881ddc590666eff4d1e
  languageName: node
  linkType: hard

"@babel/plugin-proposal-optional-chaining@npm:^7.13.12, @babel/plugin-proposal-optional-chaining@npm:^7.20.0":
  version: 7.21.0
  resolution: "@babel/plugin-proposal-optional-chaining@npm:7.21.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.20.2
    "@babel/helper-skip-transparent-expression-wrappers": ^7.20.0
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 11c5449e01b18bb8881e8e005a577fa7be2fe5688e2382c8822d51f8f7005342a301a46af7b273b1f5645f9a7b894c428eee8526342038a275ef6ba4c8d8d746
  languageName: node
  linkType: hard

"@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2":
  version: 7.21.0-placeholder-for-preset-env.2
  resolution: "@babel/plugin-proposal-private-property-in-object@npm:7.21.0-placeholder-for-preset-env.2"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d97745d098b835d55033ff3a7fb2b895b9c5295b08a5759e4f20df325aa385a3e0bc9bd5ad8f2ec554a44d4e6525acfc257b8c5848a1345cb40f26a30e277e91
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7ed1c1d9b9e5b64ef028ea5e755c0be2d4e5e4e3d6cf7df757b9a8c4cfa4193d268176d0f1f7fbecdda6fe722885c7fda681f480f3741d8a2d26854736f05367
  languageName: node
  linkType: hard

"@babel/plugin-syntax-bigint@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-bigint@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3a10849d83e47aec50f367a9e56a6b22d662ddce643334b087f9828f4c3dd73bdc5909aaeabe123fed78515767f9ca43498a0e621c438d1cd2802d7fae3c9648
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13, @babel/plugin-syntax-class-properties@npm:^7.8.3":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": ^7.12.13
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 24f34b196d6342f28d4bad303612d7ff566ab0a013ce89e775d98d6f832969462e7235f3e7eaf17678a533d4be0ba45d3ae34ab4e5a9dcbda5d98d49e5efa2fc
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3e80814b5b6d4fe17826093918680a351c2d34398a914ce6e55d8083d72a9bdde4fbaf6a2dcea0e23a03de26dc2917ae3efd603d27099e2b98380345703bf948
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.8.0, @babel/plugin-syntax-dynamic-import@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ce307af83cf433d4ec42932329fad25fa73138ab39c7436882ea28742e1c0066626d224e0ad2988724c82644e41601cef607b36194f695cb78a1fcdc959637bd
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-default-from@npm:^7.0.0, @babel/plugin-syntax-export-default-from@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-syntax-export-default-from@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5572825e7c2a9d60285c2ef1d3f7ff77965393ed1f1b44b84af981b96cb5f938d630c7bdadf69fe5ebea04bd05934541d2df3fec06d2127c81d69466d1d54649
  languageName: node
  linkType: hard

"@babel/plugin-syntax-export-namespace-from@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-export-namespace-from@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85740478be5b0de185228e7814451d74ab8ce0a26fcca7613955262a26e99e8e15e9da58f60c754b84515d4c679b590dbd3f2148f0f58025f4ae706f1c5a5d4a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-flow@npm:^7.12.1, @babel/plugin-syntax-flow@npm:^7.18.0, @babel/plugin-syntax-flow@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-syntax-flow@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 43b78b5fcdedb2a6d80c3d02a1a564fbfde86b73b442d616a8f318f673caa6ce0151513af5a00fcae42a512f144e70ef259d368b9537ee35d40336a6c895a7d4
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-assertions@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-syntax-import-assertions@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c4d67be4eb1d4637e361477dbe01f5b392b037d17c1f861cfa0faa120030e137aab90a9237931b8040fd31d1e5d159e11866fa1165f78beef7a3be876a391a17
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 590dbb5d1a15264f74670b427b8d18527672c3d6c91d7bae7e65f80fd810edbc83d90e68065088644cbad3f2457ed265a54a9956fb789fcb9a5b521822b3a275
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4, @babel/plugin-syntax-import-meta@npm:^7.8.3":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 166ac1125d10b9c0c430e4156249a13858c0366d38844883d75d27389621ebe651115cb2ceb6dc011534d5055719fa1727b59f39e1ab3ca97820eef3dcab5b9b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bf5aea1f3188c9a507e16efe030efb996853ca3cadd6512c51db7233cc58f3ac89ff8c6bdfb01d30843b161cfe7d321e1bf28da82f7ab8d7e6bc5464666f354a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.24.7, @babel/plugin-syntax-jsx@npm:^7.7.2":
  version: 7.24.7
  resolution: "@babel/plugin-syntax-jsx@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7a5ca629d8ca1e1ee78705a78e58c12920d07ed8006d7e7232b31296a384ff5e41d7b649bde5561196041037bbb9f9715be1d1c20975df87ca204f34ad15b965
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4, @babel/plugin-syntax-logical-assignment-operators@npm:^7.8.3":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: aff33577037e34e515911255cdbb1fd39efee33658aa00b8a5fd3a4b903585112d037cce1cc9e4632f0487dc554486106b79ccd5ea63a2e00df4363f6d4ff886
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.0.0, @babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 87aca4918916020d1fedba54c0e232de408df2644a425d153be368313fdde40d96088feed6c4e5ab72aac89be5d07fef2ddf329a15109c5eb65df006bf2580d1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4, @babel/plugin-syntax-numeric-separator@npm:^7.8.3":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 01ec5547bd0497f76cc903ff4d6b02abc8c05f301c88d2622b6d834e33a5651aa7c7a3d80d8d57656a4588f7276eba357f6b7e006482f5b564b7a6488de493a1
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 910d90e72bc90ea1ce698e89c1027fed8845212d5ab588e35ef91f13b93143845f94e2539d831dc8d8ededc14ec02f04f7bd6a8179edd43a326c784e7ed7f0b9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.0.0, @babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: eef94d53a1453361553c1f98b68d17782861a04a392840341bc91780838dd4e695209c783631cf0de14c635758beafb6a3a65399846ffa4386bff90639347f30
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b317174783e6e96029b743ccff2a67d63d38756876e7e5d0ba53a322e38d9ca452c13354a57de1ad476b4c066dbae699e0ca157441da611117a47af88985ecda
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5, @babel/plugin-syntax-top-level-await@npm:^7.8.3":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bbd1a56b095be7820029b209677b194db9b1d26691fe999856462e66b25b281f031f3dfd91b1619e9dcf95bebe336211833b854d0fb8780d618e35667c2d0d7e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.24.7, @babel/plugin-syntax-typescript@npm:^7.7.2":
  version: 7.24.7
  resolution: "@babel/plugin-syntax-typescript@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 56fe84f3044ecbf038977281648db6b63bd1301f2fff6595820dc10ee276c1d1586919d48d52a8d497ecae32c958be38f42c1c8d174dc58aad856c516dc5b35a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-unicode-sets-regex@npm:^7.18.6":
  version: 7.18.6
  resolution: "@babel/plugin-syntax-unicode-sets-regex@npm:7.18.6"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.18.6
    "@babel/helper-plugin-utils": ^7.18.6
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: a651d700fe63ff0ddfd7186f4ebc24447ca734f114433139e3c027bc94a900d013cf1ef2e2db8430425ba542e39ae160c3b05f06b59fd4656273a3df97679e9c
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.0.0, @babel/plugin-transform-arrow-functions@npm:^7.0.0-0, @babel/plugin-transform-arrow-functions@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 707c209b5331c7dc79bd326128c6a6640dbd62a78da1653c844db20c4f36bf7b68454f1bc4d2d051b3fde9136fa291f276ec03a071bb00ee653069ff82f91010
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-generator-functions@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/plugin-transform-async-generator-functions@npm:7.25.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/helper-remap-async-to-generator": ^7.25.0
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/traverse": ^7.25.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cce2bab70ad871ac11751bede006bd4861888f4c63bc9954be38620b14cc6890a4cbc633c1062b89c5fe288ce74b9d1974cc0d43c04baeeb2b13231a236fba85
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.20.0, @babel/plugin-transform-async-to-generator@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.24.7"
  dependencies:
    "@babel/helper-module-imports": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/helper-remap-async-to-generator": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 13704fb3b83effc868db2b71bfb2c77b895c56cb891954fc362e95e200afd523313b0e7cf04ce02f45b05e76017c5b5fa8070c92613727a35131bb542c253a36
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 249cdcbff4e778b177245f9652b014ea4f3cd245d83297f10a7bf6d97790074089aa62bcde8c08eb299c5e68f2faed346b587d3ebac44d625ba9a83a4ee27028
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.0.0, @babel/plugin-transform-block-scoping@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/plugin-transform-block-scoping@npm:7.25.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b1a8f932f69ad2a47ae3e02b4cedd2a876bfc2ac9cf72a503fd706cdc87272646fe9eed81e068c0fc639647033de29f7fa0c21cddd1da0026f83dbaac97316a8
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-properties@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-class-properties@npm:7.24.7"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1348d7ce74da38ba52ea85b3b4289a6a86913748569ef92ef0cff30702a9eb849e5eaf59f1c6f3517059aa68115fb3067e389735dccacca39add4e2b0c67e291
  languageName: node
  linkType: hard

"@babel/plugin-transform-class-static-block@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-class-static-block@npm:7.24.7"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-class-static-block": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.12.0
  checksum: 324049263504f18416f1c3e24033baebfafd05480fdd885c8ebe6f2b415b0fc8e0b98d719360f9e30743cc78ac387fabc0b3c6606d2b54135756ffb92963b382
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.0.0, @babel/plugin-transform-classes@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/plugin-transform-classes@npm:7.25.0"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.24.7
    "@babel/helper-compilation-targets": ^7.24.8
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/helper-replace-supers": ^7.25.0
    "@babel/traverse": ^7.25.0
    globals: ^11.1.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ff97f168e6a18fa4e7bb439f1a170dc83c470973091c22c74674769350ab572be5af017cdb64fbd261fe99d068a4ee88f1b7fa7f5ab524d84c2f2833b116e577
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.0.0, @babel/plugin-transform-computed-properties@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-computed-properties@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/template": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0cf8c1b1e4ea57dec8d4612460d84fd4cdbf71a7499bb61ee34632cf89018a59eee818ffca88a8d99ee7057c20a4257044d7d463fda6daef9bf1db9fa81563cb
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.20.0, @babel/plugin-transform-destructuring@npm:^7.24.8":
  version: 7.24.8
  resolution: "@babel/plugin-transform-destructuring@npm:7.24.8"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0b4bd3d608979a1e5bd97d9d42acd5ad405c7fffa61efac4c7afd8e86ea6c2d91ab2d94b6a98d63919571363fe76e0b03c4ff161f0f60241b895842596e4a999
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.24.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 67b10fc6abb1f61f0e765288eb4c6d63d1d0f9fc0660e69f6f2170c56fa16bc74e49857afc644beda112b41771cd90cf52df0940d11e97e52617c77c7dcff171
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d1da2ff85ecb56a63f4ccfd9dc9ae69400d85f0dadf44ecddd9e71c6e5c7a9178e74e3a9637555f415a2bb14551e563f09f98534ab54f53d25e8439fdde6ba2d
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/plugin-transform-duplicate-named-capturing-groups-regex@npm:7.25.0"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.25.0
    "@babel/helper-plugin-utils": ^7.24.8
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 608d6b0e77341189508880fd1a9f605a38d0803dd6f678ea3920ab181b17b377f6d5221ae8cf0104c7a044d30d4ddb0366bd064447695671d78457a656bb264f
  languageName: node
  linkType: hard

"@babel/plugin-transform-dynamic-import@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-dynamic-import@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 776509ff62ab40c12be814a342fc56a5cc09b91fb63032b2633414b635875fd7da03734657be0f6db2891fe6e3033b75d5ddb6f2baabd1a02e4443754a785002
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.24.7"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 23c84a23eb56589fdd35a3540f9a1190615be069110a2270865223c03aee3ba4e0fc68fe14850800cf36f0712b26e4964d3026235261f58f0405a29fe8dac9b1
  languageName: node
  linkType: hard

"@babel/plugin-transform-export-namespace-from@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-export-namespace-from@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3bd3a10038f10ae0dea1ee42137f3edcf7036b5e9e570a0d1cbd0865f03658990c6c2d84fa2475f87a754e7dc5b46766c16f7ce5c9b32c3040150b6a21233a80
  languageName: node
  linkType: hard

"@babel/plugin-transform-flow-strip-types@npm:^7.20.0, @babel/plugin-transform-flow-strip-types@npm:^7.24.7":
  version: 7.25.2
  resolution: "@babel/plugin-transform-flow-strip-types@npm:7.25.2"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/plugin-syntax-flow": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9f7b96cbd374077eaf04b59e468976d2e89ec353807d7ac28f129f686945447df92aeb5b60acf906f3ec0f9ebef5d9f88735c7aa39af97033a6ab96c79c9a909
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-for-of@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/helper-skip-transparent-expression-wrappers": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a53b42dc93ab4b7d1ebd3c695b52be22b3d592f6a3dbdb3dc2fea2c8e0a7e1508fe919864c455cde552aec44ce7518625fccbb70c7063373ca228d884f4f49ea
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.0.0, @babel/plugin-transform-function-name@npm:^7.25.1":
  version: 7.25.1
  resolution: "@babel/plugin-transform-function-name@npm:7.25.1"
  dependencies:
    "@babel/helper-compilation-targets": ^7.24.8
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/traverse": ^7.25.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 743f3ea03bbc5a90944849d5a880b6bd9243dddbde581a46952da76e53a0b74c1e2424133fe8129d7a152c1f8c872bcd27e0b6728d7caadabd1afa7bb892e1e0
  languageName: node
  linkType: hard

"@babel/plugin-transform-json-strings@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-json-strings@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-json-strings": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 88874d0b7a1ddea66c097fc0abb68801ffae194468aa44b828dde9a0e20ac5d8647943793de86092eabaa2911c96f67a6b373793d4bb9c932ef81b2711c06c2e
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.0.0, @babel/plugin-transform-literals@npm:^7.25.2":
  version: 7.25.2
  resolution: "@babel/plugin-transform-literals@npm:7.25.2"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 70c9bb40e377a306bd8f500899fb72127e527517914466e95dc6bb53fa7a0f51479db244a54a771b5780fc1eab488fedd706669bf11097b81a23c81ab7423eb1
  languageName: node
  linkType: hard

"@babel/plugin-transform-logical-assignment-operators@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-logical-assignment-operators@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3367ce0be243704dc6fce23e86a592c4380f01998ee5dd9f94c54b1ef7b971ac6f8a002901eb51599ac6cbdc0d067af8d1a720224fca1c40fde8bb8aab804aac
  languageName: node
  linkType: hard

"@babel/plugin-transform-member-expression-literals@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-member-expression-literals@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2720c57aa3bf70576146ba7d6ea03227f4611852122d76d237924f7b008dafc952e6ae61a19e5024f26c665f44384bbd378466f01b6bd1305b3564a3b7fb1a5d
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-modules-amd@npm:7.24.7"
  dependencies:
    "@babel/helper-module-transforms": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f1dd0fb2f46c0f8f21076b8c7ccd5b33a85ce6dcb31518ea4c648d9a5bb2474cd4bd87c9b1b752e68591e24b022e334ba0d07631fef2b6b4d8a4b85cf3d581f5
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.0.0, @babel/plugin-transform-modules-commonjs@npm:^7.13.8, @babel/plugin-transform-modules-commonjs@npm:^7.24.7, @babel/plugin-transform-modules-commonjs@npm:^7.24.8":
  version: 7.24.8
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.24.8"
  dependencies:
    "@babel/helper-module-transforms": ^7.24.8
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/helper-simple-access": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a4cf95b1639c33382064b44558f73ee5fac023f2a94d16e549d2bb55ceebd5cbc10fcddd505d08cd5bc97f5a64af9fd155512358b7dcf7b1a0082e8945cf21c5
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.25.0":
  version: 7.25.0
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.25.0"
  dependencies:
    "@babel/helper-module-transforms": ^7.25.0
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/helper-validator-identifier": ^7.24.7
    "@babel/traverse": ^7.25.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fe673bec08564e491847324bb80a1e6edfb229f5c37e58a094d51e95306e7b098e1d130fc43e992d22debd93b9beac74441ffc3f6ea5d78f6b2535896efa0728
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-modules-umd@npm:7.24.7"
  dependencies:
    "@babel/helper-module-transforms": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9ff1c464892efe042952ba778468bda6131b196a2729615bdcc3f24cdc94014f016a4616ee5643c5845bade6ba698f386833e61056d7201314b13a7fd69fac88
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.0.0, @babel/plugin-transform-named-capturing-groups-regex@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.24.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: f1c6c7b5d60a86b6d7e4dd098798e1d393d55e993a0b57a73b53640c7a94985b601a96bdacee063f809a9a700bcea3a2ff18e98fa561554484ac56b761d774bd
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-new-target@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3cb94cd1076b270f768f91fdcf9dd2f6d487f8dbfff3df7ca8d07b915900b86d02769a35ba1407d16fe49499012c8f055e1741299e2c880798b953d942a8fa1b
  languageName: node
  linkType: hard

"@babel/plugin-transform-nullish-coalescing-operator@npm:^7.0.0-0, @babel/plugin-transform-nullish-coalescing-operator@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-nullish-coalescing-operator@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4a9221356401d87762afbc37a9e8e764afc2daf09c421117537820f8cfbed6876888372ad3a7bcfae2d45c95f026651f050ab4020b777be31d3ffb00908dbdd3
  languageName: node
  linkType: hard

"@babel/plugin-transform-numeric-separator@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-numeric-separator@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 561b5f1d08b2c3f92ce849f092751558b5e6cfeb7eb55c79e7375c34dd9c3066dce5e630bb439affef6adcf202b6cbcaaa23870070276fa5bb429c8f5b8c7514
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-rest-spread@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-object-rest-spread@npm:7.24.7"
  dependencies:
    "@babel/helper-compilation-targets": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-transform-parameters": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 169d257b9800c13e1feb4c37fb05dae84f702e58b342bb76e19e82e6692b7b5337c9923ee89e3916a97c0dd04a3375bdeca14f5e126f110bbacbeb46d1886ca2
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-object-super@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/helper-replace-supers": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f71e607a830ee50a22fa1a2686524d3339440cf9dea63032f6efbd865cfe4e35000e1e3f3492459e5c986f7c0c07dc36938bf3ce61fc9ba5f8ab732d0b64ab37
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-catch-binding@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-optional-catch-binding@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7229f3a5a4facaab40f4fdfc7faabc157dc38a67d66bed7936599f4bc509e0bff636f847ac2aa45294881fce9cf8a0a460b85d2a465b7b977de9739fce9b18f6
  languageName: node
  linkType: hard

"@babel/plugin-transform-optional-chaining@npm:^7.0.0-0, @babel/plugin-transform-optional-chaining@npm:^7.24.7, @babel/plugin-transform-optional-chaining@npm:^7.24.8":
  version: 7.24.8
  resolution: "@babel/plugin-transform-optional-chaining@npm:7.24.8"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/helper-skip-transparent-expression-wrappers": ^7.24.7
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 45e55e3a2fffb89002d3f89aef59c141610f23b60eee41e047380bffc40290b59f64fc649aa7ec5281f73d41b2065410d788acc6afaad2a9f44cad6e8af04442
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.0.0, @babel/plugin-transform-parameters@npm:^7.20.7, @babel/plugin-transform-parameters@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-parameters@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ab534b03ac2eff94bc79342b8f39a4584666f5305a6c63c1964afda0b1b004e6b861e49d1683548030defe248e3590d3ff6338ee0552cb90c064f7e1479968c3
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-methods@npm:^7.22.5, @babel/plugin-transform-private-methods@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-private-methods@npm:7.24.7"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c151548e34909be2adcceb224d8fdd70bafa393bc1559a600906f3f647317575bf40db670470934a360e90ee8084ef36dffa34ec25d387d414afd841e74cf3fe
  languageName: node
  linkType: hard

"@babel/plugin-transform-private-property-in-object@npm:^7.22.11, @babel/plugin-transform-private-property-in-object@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-private-property-in-object@npm:7.24.7"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.24.7
    "@babel/helper-create-class-features-plugin": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8cee9473095305cc787bb653fd681719b49363281feabf677db8a552e8e41c94441408055d7e5fd5c7d41b315e634fa70b145ad0c7c54456216049df4ed57350
  languageName: node
  linkType: hard

"@babel/plugin-transform-property-literals@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-property-literals@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9aeefc3aab6c6bf9d1fae1cf3a2d38c7d886fd3c6c81b7c608c477f5758aee2e7abf52f32724310fe861da61af934ee2508b78a5b5f234b9740c9134e1c14437
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-display-name@npm:^7.0.0":
  version: 7.24.7
  resolution: "@babel/plugin-transform-react-display-name@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a05bf83bf5e7b31f7a3b56da1bf8e2eeec76ef52ae44435ceff66363a1717fcda45b7b4b931a2c115982175f481fc3f2d0fab23f0a43c44e6d983afc396858f0
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-self@npm:^7.0.0":
  version: 7.24.7
  resolution: "@babel/plugin-transform-react-jsx-self@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2d72c33664e614031b8a03fc2d4cfd185e99efb1d681cbde4b0b4ab379864b31d83ee923509892f6d94b2c5893c309f0217d33bcda3e470ed42297f958138381
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx-source@npm:^7.0.0":
  version: 7.24.7
  resolution: "@babel/plugin-transform-react-jsx-source@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c9afcb2259dd124a2de76f8a578589c18bd2f24dbcf78fe02b53c5cbc20c493c4618369604720e4e699b52be10ba0751b97140e1ef8bc8f0de0a935280e9d5b7
  languageName: node
  linkType: hard

"@babel/plugin-transform-react-jsx@npm:^7.0.0":
  version: 7.25.2
  resolution: "@babel/plugin-transform-react-jsx@npm:7.25.2"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.24.7
    "@babel/helper-module-imports": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/plugin-syntax-jsx": ^7.24.7
    "@babel/types": ^7.25.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 44fbde046385916de19a88d77fed9121c6cc6e25b9cdc38a43d8e514a9b18cf391ed3de25e7d6a8996d3fe4c298e395edf856ee20efffaab3b70f8ce225fffa4
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-regenerator@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    regenerator-transform: ^0.15.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 20c6c3fb6fc9f407829087316653388d311e8c1816b007609bb09aeef254092a7157adace8b3aaa8f34be752503717cb85c88a5fe482180a9b11bcbd676063be
  languageName: node
  linkType: hard

"@babel/plugin-transform-reserved-words@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-reserved-words@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3d5876954d5914d7270819479504f30c4bf5452a65c677f44e2dab2db50b3c9d4b47793c45dfad7abf4f377035dd79e4b3f554ae350df9f422201d370ce9f8dd
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.0.0":
  version: 7.24.7
  resolution: "@babel/plugin-transform-runtime@npm:7.24.7"
  dependencies:
    "@babel/helper-module-imports": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
    babel-plugin-polyfill-corejs2: ^0.4.10
    babel-plugin-polyfill-corejs3: ^0.10.1
    babel-plugin-polyfill-regenerator: ^0.6.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 98bcbbdc833d5c451189a6325f88820fe92973e119c59ce74bf28681cf4687c8280decb55b6c47f22e98c3973ae3a13521c4f51855a2b8577b230ecb1b4ca5b4
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.0.0, @babel/plugin-transform-shorthand-properties@npm:^7.0.0-0, @babel/plugin-transform-shorthand-properties@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7b524245814607188212b8eb86d8c850e5974203328455a30881b4a92c364b93353fae14bc2af5b614ef16300b75b8c1d3b8f3a08355985b4794a7feb240adc3
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.0.0, @babel/plugin-transform-spread@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-spread@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/helper-skip-transparent-expression-wrappers": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4c4254c8b9cceb1a8f975fa9b92257ddb08380a35c0a3721b8f4b9e13a3d82e403af2e0fba577b9f2452dd8f06bc3dea71cc53b1e2c6af595af5db52a13429d6
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.0.0, @babel/plugin-transform-sticky-regex@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 118fc7a7ebf7c20411b670c8a030535fdfe4a88bc5643bb625a584dbc4c8a468da46430a20e6bf78914246962b0f18f1b9d6a62561a7762c4f34a038a5a77179
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.0.0-0, @babel/plugin-transform-template-literals@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-template-literals@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ad44e5826f5a98c1575832dbdbd033adfe683cdff195e178528ead62507564bf02f479b282976cfd3caebad8b06d5fd7349c1cdb880dec3c56daea4f1f179619
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.24.8":
  version: 7.24.8
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.24.8"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.8
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8663a8e7347cedf181001d99c88cf794b6598c3d82f324098510fe8fb8bd22113995526a77aa35a3cc5d70ffd0617a59dd0d10311a9bf0e1a3a7d3e59b900c00
  languageName: node
  linkType: hard

"@babel/plugin-transform-typescript@npm:^7.24.7, @babel/plugin-transform-typescript@npm:^7.5.0":
  version: 7.25.2
  resolution: "@babel/plugin-transform-typescript@npm:7.25.2"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.24.7
    "@babel/helper-create-class-features-plugin": ^7.25.0
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/helper-skip-transparent-expression-wrappers": ^7.24.7
    "@babel/plugin-syntax-typescript": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b0267128d93560a4350919f7230a3b497e20fb8611d9f04bb3560d6b38877305ccad4c40903160263361c6930a84dbcb5b21b8ea923531bda51f67bffdc2dd0b
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-escapes@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-unicode-escapes@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4af0a193e1ddea6ff82b2b15cc2501b872728050bd625740b813c8062fec917d32d530ff6b41de56c15e7296becdf3336a58db81f5ca8e7c445c1306c52f3e01
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-property-regex@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-unicode-property-regex@npm:7.24.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: aae13350c50973f5802ca7906d022a6a0cc0e3aebac9122d0450bbd51e78252d4c2032ad69385e2759fcbdd3aac5d571bd7e26258907f51f8e1a51b53be626c2
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.0.0, @babel/plugin-transform-unicode-regex@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.24.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1cb4e70678906e431da0a05ac3f8350025fee290304ad7482d9cfaa1ca67b2e898654de537c9268efbdad5b80d3ebadf42b4a88ea84609bd8a4cce7b11b48afd
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-sets-regex@npm:^7.24.7":
  version: 7.24.7
  resolution: "@babel/plugin-transform-unicode-sets-regex@npm:7.24.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.24.7
    "@babel/helper-plugin-utils": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 08a2844914f33dacd2ce1ab021ce8c1cc35dc6568521a746d8bf29c21571ee5be78787b454231c4bb3526cbbe280f1893223c82726cec5df2be5dae0a3b51837
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.20.0":
  version: 7.25.3
  resolution: "@babel/preset-env@npm:7.25.3"
  dependencies:
    "@babel/compat-data": ^7.25.2
    "@babel/helper-compilation-targets": ^7.25.2
    "@babel/helper-plugin-utils": ^7.24.8
    "@babel/helper-validator-option": ^7.24.8
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key": ^7.25.3
    "@babel/plugin-bugfix-safari-class-field-initializer-scope": ^7.25.0
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": ^7.25.0
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": ^7.24.7
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": ^7.25.0
    "@babel/plugin-proposal-private-property-in-object": 7.21.0-placeholder-for-preset-env.2
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-class-properties": ^7.12.13
    "@babel/plugin-syntax-class-static-block": ^7.14.5
    "@babel/plugin-syntax-dynamic-import": ^7.8.3
    "@babel/plugin-syntax-export-namespace-from": ^7.8.3
    "@babel/plugin-syntax-import-assertions": ^7.24.7
    "@babel/plugin-syntax-import-attributes": ^7.24.7
    "@babel/plugin-syntax-import-meta": ^7.10.4
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.10.4
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.10.4
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-private-property-in-object": ^7.14.5
    "@babel/plugin-syntax-top-level-await": ^7.14.5
    "@babel/plugin-syntax-unicode-sets-regex": ^7.18.6
    "@babel/plugin-transform-arrow-functions": ^7.24.7
    "@babel/plugin-transform-async-generator-functions": ^7.25.0
    "@babel/plugin-transform-async-to-generator": ^7.24.7
    "@babel/plugin-transform-block-scoped-functions": ^7.24.7
    "@babel/plugin-transform-block-scoping": ^7.25.0
    "@babel/plugin-transform-class-properties": ^7.24.7
    "@babel/plugin-transform-class-static-block": ^7.24.7
    "@babel/plugin-transform-classes": ^7.25.0
    "@babel/plugin-transform-computed-properties": ^7.24.7
    "@babel/plugin-transform-destructuring": ^7.24.8
    "@babel/plugin-transform-dotall-regex": ^7.24.7
    "@babel/plugin-transform-duplicate-keys": ^7.24.7
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex": ^7.25.0
    "@babel/plugin-transform-dynamic-import": ^7.24.7
    "@babel/plugin-transform-exponentiation-operator": ^7.24.7
    "@babel/plugin-transform-export-namespace-from": ^7.24.7
    "@babel/plugin-transform-for-of": ^7.24.7
    "@babel/plugin-transform-function-name": ^7.25.1
    "@babel/plugin-transform-json-strings": ^7.24.7
    "@babel/plugin-transform-literals": ^7.25.2
    "@babel/plugin-transform-logical-assignment-operators": ^7.24.7
    "@babel/plugin-transform-member-expression-literals": ^7.24.7
    "@babel/plugin-transform-modules-amd": ^7.24.7
    "@babel/plugin-transform-modules-commonjs": ^7.24.8
    "@babel/plugin-transform-modules-systemjs": ^7.25.0
    "@babel/plugin-transform-modules-umd": ^7.24.7
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.24.7
    "@babel/plugin-transform-new-target": ^7.24.7
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.24.7
    "@babel/plugin-transform-numeric-separator": ^7.24.7
    "@babel/plugin-transform-object-rest-spread": ^7.24.7
    "@babel/plugin-transform-object-super": ^7.24.7
    "@babel/plugin-transform-optional-catch-binding": ^7.24.7
    "@babel/plugin-transform-optional-chaining": ^7.24.8
    "@babel/plugin-transform-parameters": ^7.24.7
    "@babel/plugin-transform-private-methods": ^7.24.7
    "@babel/plugin-transform-private-property-in-object": ^7.24.7
    "@babel/plugin-transform-property-literals": ^7.24.7
    "@babel/plugin-transform-regenerator": ^7.24.7
    "@babel/plugin-transform-reserved-words": ^7.24.7
    "@babel/plugin-transform-shorthand-properties": ^7.24.7
    "@babel/plugin-transform-spread": ^7.24.7
    "@babel/plugin-transform-sticky-regex": ^7.24.7
    "@babel/plugin-transform-template-literals": ^7.24.7
    "@babel/plugin-transform-typeof-symbol": ^7.24.8
    "@babel/plugin-transform-unicode-escapes": ^7.24.7
    "@babel/plugin-transform-unicode-property-regex": ^7.24.7
    "@babel/plugin-transform-unicode-regex": ^7.24.7
    "@babel/plugin-transform-unicode-sets-regex": ^7.24.7
    "@babel/preset-modules": 0.1.6-no-external-plugins
    babel-plugin-polyfill-corejs2: ^0.4.10
    babel-plugin-polyfill-corejs3: ^0.10.4
    babel-plugin-polyfill-regenerator: ^0.6.1
    core-js-compat: ^3.37.1
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9735a44e557f7ef4ade87f59c0d69e4af3383432a23ae7a3cba33e3741bd7812f2d6403a0d94ebfda5f4bd9fdc6250a52c4a156407029f590fde511a792e64e2
  languageName: node
  linkType: hard

"@babel/preset-flow@npm:^7.13.13":
  version: 7.24.7
  resolution: "@babel/preset-flow@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/helper-validator-option": ^7.24.7
    "@babel/plugin-transform-flow-strip-types": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4caca02a6e0a477eb22994d686a1fbf65b5ab0240ae77530696434dba7efff4c5dcbf9186a774168dd4c492423141a22af3f2874c356aa22429f3c83eaf34419
  languageName: node
  linkType: hard

"@babel/preset-modules@npm:0.1.6-no-external-plugins":
  version: 0.1.6-no-external-plugins
  resolution: "@babel/preset-modules@npm:0.1.6-no-external-plugins"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@babel/types": ^7.4.4
    esutils: ^2.0.2
  peerDependencies:
    "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0
  checksum: 4855e799bc50f2449fb5210f78ea9e8fd46cf4f242243f1e2ed838e2bd702e25e73e822e7f8447722a5f4baa5e67a8f7a0e403f3e7ce04540ff743a9c411c375
  languageName: node
  linkType: hard

"@babel/preset-typescript@npm:^7.13.0, @babel/preset-typescript@npm:^7.16.7":
  version: 7.24.7
  resolution: "@babel/preset-typescript@npm:7.24.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.24.7
    "@babel/helper-validator-option": ^7.24.7
    "@babel/plugin-syntax-jsx": ^7.24.7
    "@babel/plugin-transform-modules-commonjs": ^7.24.7
    "@babel/plugin-transform-typescript": ^7.24.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 12929b24757f3bd6548103475f86478eda4c872bc7cefd920b29591eee8f4a4f350561d888e133d632d0c9402b8615fdcec9138e5127a6567dcb22f804ff207f
  languageName: node
  linkType: hard

"@babel/register@npm:^7.13.16":
  version: 7.24.6
  resolution: "@babel/register@npm:7.24.6"
  dependencies:
    clone-deep: ^4.0.1
    find-cache-dir: ^2.0.0
    make-dir: ^2.1.0
    pirates: ^4.0.6
    source-map-support: ^0.5.16
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 446316c80969df89ad3515576937ddf746cd4927810f226101a8d7f476b399c14c26847e77637e09355399c645fbf413d6e53ac6987b8cf240de7932a9372cb5
  languageName: node
  linkType: hard

"@babel/regjsgen@npm:^0.8.0":
  version: 0.8.0
  resolution: "@babel/regjsgen@npm:0.8.0"
  checksum: 89c338fee774770e5a487382170711014d49a68eb281e74f2b5eac88f38300a4ad545516a7786a8dd5702e9cf009c94c2f582d200f077ac5decd74c56b973730
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.0.0, @babel/runtime@npm:^7.18.6, @babel/runtime@npm:^7.20.0, @babel/runtime@npm:^7.8.4":
  version: 7.25.0
  resolution: "@babel/runtime@npm:7.25.0"
  dependencies:
    regenerator-runtime: ^0.14.0
  checksum: 4a2a374a58eb01aaa65c5762606e90b3a1f448e0c637d42278b6cc0b42a9f5399b5f381ba9f237ee087da2860d14dd2d1de7bddcbe18be6a3cafba97e44bed64
  languageName: node
  linkType: hard

"@babel/template@npm:^7.0.0, @babel/template@npm:^7.24.7, @babel/template@npm:^7.25.0, @babel/template@npm:^7.3.3":
  version: 7.25.0
  resolution: "@babel/template@npm:7.25.0"
  dependencies:
    "@babel/code-frame": ^7.24.7
    "@babel/parser": ^7.25.0
    "@babel/types": ^7.25.0
  checksum: 3f2db568718756d0daf2a16927b78f00c425046b654cd30b450006f2e84bdccaf0cbe6dc04994aa1f5f6a4398da2f11f3640a4d3ee31722e43539c4c919c817b
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.20.0, @babel/traverse@npm:^7.24.7, @babel/traverse@npm:^7.24.8, @babel/traverse@npm:^7.25.0, @babel/traverse@npm:^7.25.1, @babel/traverse@npm:^7.25.2, @babel/traverse@npm:^7.25.3":
  version: 7.25.3
  resolution: "@babel/traverse@npm:7.25.3"
  dependencies:
    "@babel/code-frame": ^7.24.7
    "@babel/generator": ^7.25.0
    "@babel/parser": ^7.25.3
    "@babel/template": ^7.25.0
    "@babel/types": ^7.25.2
    debug: ^4.3.1
    globals: ^11.1.0
  checksum: 5661308b1357816f1d4e2813a5dd82c6053617acc08c5c95db051b8b6577d07c4446bc861c9a5e8bf294953ac8266ae13d7d9d856b6b889fc0d34c1f51abbd8c
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.21.3, @babel/types@npm:^7.24.7, @babel/types@npm:^7.24.8, @babel/types@npm:^7.25.0, @babel/types@npm:^7.25.2, @babel/types@npm:^7.3.3, @babel/types@npm:^7.4.4":
  version: 7.25.2
  resolution: "@babel/types@npm:7.25.2"
  dependencies:
    "@babel/helper-string-parser": ^7.24.8
    "@babel/helper-validator-identifier": ^7.24.7
    to-fast-properties: ^2.0.0
  checksum: f73f66ba903c6f7e38f519a33d53a67d49c07e208e59ea65250362691dc546c6da7ab90ec66ee79651ef697329872f6f97eb19a6dfcacc026fd05e76a563c5d2
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^0.2.3":
  version: 0.2.3
  resolution: "@bcoe/v8-coverage@npm:0.2.3"
  checksum: 850f9305536d0f2bd13e9e0881cb5f02e4f93fad1189f7b2d4bebf694e3206924eadee1068130d43c11b750efcc9405f88a8e42ef098b6d75239c0f047de1a27
  languageName: node
  linkType: hard

"@egjs/hammerjs@npm:^2.0.17":
  version: 2.0.17
  resolution: "@egjs/hammerjs@npm:2.0.17"
  dependencies:
    "@types/hammerjs": ^2.0.36
  checksum: 8945137cec5837edd70af3f2e0ea621543eb0aa3b667e6269ec6485350f4d120c2434b37c7c30b1cf42a65275dd61c1f24626749c616696d3956ac0c008c4766
  languageName: node
  linkType: hard

"@emotion/hash@npm:^0.9.1":
  version: 0.9.2
  resolution: "@emotion/hash@npm:0.9.2"
  checksum: 379bde2830ccb0328c2617ec009642321c0e009a46aa383dfbe75b679c6aea977ca698c832d225a893901f29d7b3eef0e38cf341f560f6b2b56f1ff23c172387
  languageName: node
  linkType: hard

"@emotion/is-prop-valid@npm:^0.8.2":
  version: 0.8.8
  resolution: "@emotion/is-prop-valid@npm:0.8.8"
  dependencies:
    "@emotion/memoize": 0.7.4
  checksum: bb7ec6d48c572c540e24e47cc94fc2f8dec2d6a342ae97bc9c8b6388d9b8d283862672172a1bb62d335c02662afe6291e10c71e9b8642664a8b43416cdceffac
  languageName: node
  linkType: hard

"@emotion/memoize@npm:0.7.4":
  version: 0.7.4
  resolution: "@emotion/memoize@npm:0.7.4"
  checksum: 4e3920d4ec95995657a37beb43d3f4b7d89fed6caa2b173a4c04d10482d089d5c3ea50bbc96618d918b020f26ed6e9c4026bbd45433566576c1f7b056c3271dc
  languageName: node
  linkType: hard

"@eslint-community/eslint-utils@npm:^4.2.0, @eslint-community/eslint-utils@npm:^4.4.0":
  version: 4.4.0
  resolution: "@eslint-community/eslint-utils@npm:4.4.0"
  dependencies:
    eslint-visitor-keys: ^3.3.0
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
  checksum: cdfe3ae42b4f572cbfb46d20edafe6f36fc5fb52bf2d90875c58aefe226892b9677fef60820e2832caf864a326fe4fc225714c46e8389ccca04d5f9288aabd22
  languageName: node
  linkType: hard

"@eslint-community/regexpp@npm:^4.10.0, @eslint-community/regexpp@npm:^4.11.0, @eslint-community/regexpp@npm:^4.4.0, @eslint-community/regexpp@npm:^4.5.1":
  version: 4.11.0
  resolution: "@eslint-community/regexpp@npm:4.11.0"
  checksum: 97d2fe46690b69417a551bd19a3dc53b6d9590d2295c43cc4c4e44e64131af541e2f4a44d5c12e87de990403654d3dae9d33600081f3a2f0386b368abc9111ec
  languageName: node
  linkType: hard

"@eslint/compat@npm:^1.1.0":
  version: 1.1.1
  resolution: "@eslint/compat@npm:1.1.1"
  checksum: c9146b139e52ee4f79e25b97f22d2936c50b876cef8e9c5789600f12d8fabae689d75571a8429e5aae0d5e8067b0628fd87b7e849cee391b485db9557b40b6a4
  languageName: node
  linkType: hard

"@eslint/config-array@npm:^0.17.1":
  version: 0.17.1
  resolution: "@eslint/config-array@npm:0.17.1"
  dependencies:
    "@eslint/object-schema": ^2.1.4
    debug: ^4.3.1
    minimatch: ^3.1.2
  checksum: b678a7af5b0be8f1b29deaf751c77c365cf0b24bead3add677edbc7c7793dfb3eb423e33395787ff86fdbd85117a571f2f338d612a23210d9771aedf765d5482
  languageName: node
  linkType: hard

"@eslint/eslintrc@npm:^3.1.0":
  version: 3.1.0
  resolution: "@eslint/eslintrc@npm:3.1.0"
  dependencies:
    ajv: ^6.12.4
    debug: ^4.3.2
    espree: ^10.0.1
    globals: ^14.0.0
    ignore: ^5.2.0
    import-fresh: ^3.2.1
    js-yaml: ^4.1.0
    minimatch: ^3.1.2
    strip-json-comments: ^3.1.1
  checksum: b0a9bbd98c8b9e0f4d975b042ff9b874dde722b20834ea2ff46551c3de740d4f10f56c449b790ef34d7f82147cbddfc22b004a43cc885dbc2664bb134766b5e4
  languageName: node
  linkType: hard

"@eslint/js@npm:9.8.0, @eslint/js@npm:^9.5.0":
  version: 9.8.0
  resolution: "@eslint/js@npm:9.8.0"
  checksum: 8b6e809127edea3bf5b5b3c01ae75290afe5044f950642445a50cb4b86c153ad1512fc417d3cb7b79eb7cd0ce06acd858946b6497ea912750aa54de609064f63
  languageName: node
  linkType: hard

"@eslint/object-schema@npm:^2.1.4":
  version: 2.1.4
  resolution: "@eslint/object-schema@npm:2.1.4"
  checksum: 5a03094115bcdab7991dbbc5d17a9713f394cebb4b44d3eaf990d7487b9b8e1877b817997334ab40be52e299a0384595c6f6ba91b389901e5e1d21efda779271
  languageName: node
  linkType: hard

"@expo/config-plugins@npm:^7.8.4":
  version: 7.9.2
  resolution: "@expo/config-plugins@npm:7.9.2"
  dependencies:
    "@expo/config-types": ^50.0.0-alpha.1
    "@expo/fingerprint": ^0.6.0
    "@expo/json-file": ~8.3.0
    "@expo/plist": ^0.1.0
    "@expo/sdk-runtime-versions": ^1.0.0
    "@react-native/normalize-color": ^2.0.0
    chalk: ^4.1.2
    debug: ^4.3.1
    find-up: ~5.0.0
    getenv: ^1.0.0
    glob: 7.1.6
    resolve-from: ^5.0.0
    semver: ^7.5.3
    slash: ^3.0.0
    slugify: ^1.6.6
    xcode: ^3.0.1
    xml2js: 0.6.0
  checksum: cd3440558a6cb757626a21a945781bd1d43ed1b4ab268673c29a8eb2f6ed95217bf8a94b6093a0238c3ce6481c8c963598aab6334e10f72a594ce8d5b9f87505
  languageName: node
  linkType: hard

"@expo/config-types@npm:^50.0.0-alpha.1":
  version: 50.0.1
  resolution: "@expo/config-types@npm:50.0.1"
  checksum: f803730a5a56fb0b1ddc4385d7ac930935c88be4e5a5dfaa9b7733444ba4d6d784946ecdc1e414528e5f642a473ff01a1533b8c4174e87b35d15cdd88c96bf49
  languageName: node
  linkType: hard

"@expo/fingerprint@npm:^0.6.0":
  version: 0.6.1
  resolution: "@expo/fingerprint@npm:0.6.1"
  dependencies:
    "@expo/spawn-async": ^1.5.0
    chalk: ^4.1.2
    debug: ^4.3.4
    find-up: ^5.0.0
    minimatch: ^3.0.4
    p-limit: ^3.1.0
    resolve-from: ^5.0.0
  bin:
    fingerprint: bin/cli.js
  checksum: 8398d466a232604b54fc1438ece486753f6e8e65933ab270377b6f8ed1c4409a886031f0dff56b7f34d17774f71f9fd800e6b78f79fedceb2a6d4a3fdd9f490d
  languageName: node
  linkType: hard

"@expo/json-file@npm:~8.3.0":
  version: 8.3.3
  resolution: "@expo/json-file@npm:8.3.3"
  dependencies:
    "@babel/code-frame": ~7.10.4
    json5: ^2.2.2
    write-file-atomic: ^2.3.0
  checksum: 49fcb3581ac21c1c223459f32e9e931149b56a7587318f666303a62e719e3d0f122ff56a60d47ee31fac937c297a66400a00fcee63a17bebbf4b8cd30c5138c1
  languageName: node
  linkType: hard

"@expo/plist@npm:^0.1.0":
  version: 0.1.3
  resolution: "@expo/plist@npm:0.1.3"
  dependencies:
    "@xmldom/xmldom": ~0.7.7
    base64-js: ^1.2.3
    xmlbuilder: ^14.0.0
  checksum: 8abe78bed4d1849f2cddddd1a238c6fe5c2549a9dee40158224ff70112f31503db3f17a522b6e21f16eea66b5f4b46cc49d22f2b369067d00a88ef6d301a50cd
  languageName: node
  linkType: hard

"@expo/sdk-runtime-versions@npm:^1.0.0":
  version: 1.0.0
  resolution: "@expo/sdk-runtime-versions@npm:1.0.0"
  checksum: 0942d5a356f590e8dc795761456cc48b3e2d6a38ad2a02d6774efcdc5a70424e05623b4e3e5d2fec0cdc30f40dde05c14391c781607eed3971bf8676518bfd9d
  languageName: node
  linkType: hard

"@expo/spawn-async@npm:^1.5.0":
  version: 1.7.2
  resolution: "@expo/spawn-async@npm:1.7.2"
  dependencies:
    cross-spawn: ^7.0.3
  checksum: d99e5ff6d303ec9b0105f97c4fa6c65bca526c7d4d0987997c35cc745fa8224adf009942d01808192ebb9fa30619a53316641958631e85cf17b773d9eeda2597
  languageName: node
  linkType: hard

"@fastify/busboy@npm:^2.0.0":
  version: 2.1.1
  resolution: "@fastify/busboy@npm:2.1.1"
  checksum: 42c32ef75e906c9a4809c1e1930a5ca6d4ddc8d138e1a8c8ba5ea07f997db32210617d23b2e4a85fe376316a41a1a0439fc6ff2dedf5126d96f45a9d80754fb2
  languageName: node
  linkType: hard

"@firebase/analytics-compat@npm:0.2.10":
  version: 0.2.10
  resolution: "@firebase/analytics-compat@npm:0.2.10"
  dependencies:
    "@firebase/analytics": 0.10.4
    "@firebase/analytics-types": 0.8.2
    "@firebase/component": 0.6.7
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app-compat": 0.x
  checksum: 240d6af490e298fea27aec4a0d014b13ca42a6dbe0c0242eab6d742f05c2e357e2673efa410124593088b84dc2906cb036af7c96909af73723ab49ddcc1ba664
  languageName: node
  linkType: hard

"@firebase/analytics-types@npm:0.8.2":
  version: 0.8.2
  resolution: "@firebase/analytics-types@npm:0.8.2"
  checksum: a8279b070b8a2496b596a18111bc51488d2e6e4b7d6cd46cbe4406a61693254c2dbd0c7d0dec77a0016a4277cde7978fd61c711bcb15ea578b33b2a5b9aba46a
  languageName: node
  linkType: hard

"@firebase/analytics@npm:0.10.4":
  version: 0.10.4
  resolution: "@firebase/analytics@npm:0.10.4"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/installations": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app": 0.x
  checksum: c09fc67e7a08f08c3edd8b98a2f9ac6d25a25ae4cdd98e8a503fa8b66b4d0cf69b137c55e6239ad48a1a2f8d4976128c0fa77dde8be2b9e35b81e7a37a23eade
  languageName: node
  linkType: hard

"@firebase/app-check-compat@npm:0.3.11":
  version: 0.3.11
  resolution: "@firebase/app-check-compat@npm:0.3.11"
  dependencies:
    "@firebase/app-check": 0.8.4
    "@firebase/app-check-types": 0.5.2
    "@firebase/component": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app-compat": 0.x
  checksum: 641c14b0f8b921f91ac64503181cd2dddfe2272f86f6dd4c8492dee30044382ce7b21e1b5e4eb5d7acb00c5d1c73f05d9e84a8f998525883463bc30e1a33eeb9
  languageName: node
  linkType: hard

"@firebase/app-check-interop-types@npm:0.3.2":
  version: 0.3.2
  resolution: "@firebase/app-check-interop-types@npm:0.3.2"
  checksum: 7dd452c21cb8b3682082a6f4023de208b4a4808d97ede7d72a54f2e0a51963adf1c1bcc8a8c8338bee1ba0b66516cc101a1fb51a26a80c9322c3a080aee6ec26
  languageName: node
  linkType: hard

"@firebase/app-check-types@npm:0.5.2":
  version: 0.5.2
  resolution: "@firebase/app-check-types@npm:0.5.2"
  checksum: d0ab668274475bdb33a5f7164a9a380e46c21b3405cb46072895386f896953461e113119bd1b2eb63abd14fc9cf249f2f80e87adbbb9bc7ef7564967955cc200
  languageName: node
  linkType: hard

"@firebase/app-check@npm:0.8.4":
  version: 0.8.4
  resolution: "@firebase/app-check@npm:0.8.4"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app": 0.x
  checksum: c5ce3ae97f0a074006ab9edb33cf062ef9f53f64727533131066297b8b7cff863f99a95d08913167025a575815ce733935e95bba021011864a4ecb38fa7560e5
  languageName: node
  linkType: hard

"@firebase/app-compat@npm:0.2.35":
  version: 0.2.35
  resolution: "@firebase/app-compat@npm:0.2.35"
  dependencies:
    "@firebase/app": 0.10.5
    "@firebase/component": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  checksum: 4761339261bccfcd276f22415b98a0759df3ee122c2de1bf68163542303ffe915c813a7148842ab5579e769a65d0dae97b45f634da4c8e82c88ff808cf7b5853
  languageName: node
  linkType: hard

"@firebase/app-types@npm:0.9.2":
  version: 0.9.2
  resolution: "@firebase/app-types@npm:0.9.2"
  checksum: c709592d84e262b980cbeff4fd5f5d5c522a9de7fe33bcdede8e6390fc05a283c11a2bf0b012fef1329251d4599f12f4b4f0dd2228a8ec42da017ae968e740a4
  languageName: node
  linkType: hard

"@firebase/app@npm:0.10.5":
  version: 0.10.5
  resolution: "@firebase/app@npm:0.10.5"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    idb: 7.1.1
    tslib: ^2.1.0
  checksum: cc7f980a7737090edbf7ee6ce94776d1e5ac0ecf3414be6c2d3b93ae3b9e55a9a9954f863ac19690d7202b679140c2864eb62cf712cdfc19ea66cdc6c36abf93
  languageName: node
  linkType: hard

"@firebase/auth-compat@npm:0.5.9":
  version: 0.5.9
  resolution: "@firebase/auth-compat@npm:0.5.9"
  dependencies:
    "@firebase/auth": 1.7.4
    "@firebase/auth-types": 0.12.2
    "@firebase/component": 0.6.7
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
    undici: 5.28.4
  peerDependencies:
    "@firebase/app-compat": 0.x
  checksum: 3b2389c3420192c561d78f2e7d2c3fdaaa46a811dd018ff05e0987e2585638adece759574e9391b49d61f8f9a82325278e5b0568b8629b0af1fc061dd0167a74
  languageName: node
  linkType: hard

"@firebase/auth-interop-types@npm:0.2.3":
  version: 0.2.3
  resolution: "@firebase/auth-interop-types@npm:0.2.3"
  checksum: fdadd64a067fdc1f32464890c861cdcc984a4aae307e7d46f182ba508082e55921c6f70042d1f893dfd18434484783f866adefcdc01dba8818cd7f0b0c89acf2
  languageName: node
  linkType: hard

"@firebase/auth-types@npm:0.12.2":
  version: 0.12.2
  resolution: "@firebase/auth-types@npm:0.12.2"
  peerDependencies:
    "@firebase/app-types": 0.x
    "@firebase/util": 1.x
  checksum: d4bbe222b22bbd213d2e6dc8af9e196b39eb29e55c4aecf4d81d232dc105ae895c587e56e37363e5192c56b1db157c3b18c9378a907d1672e6124c4cd793a04d
  languageName: node
  linkType: hard

"@firebase/auth@npm:1.7.4":
  version: 1.7.4
  resolution: "@firebase/auth@npm:1.7.4"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
    undici: 5.28.4
  peerDependencies:
    "@firebase/app": 0.x
    "@react-native-async-storage/async-storage": ^1.18.1
  peerDependenciesMeta:
    "@react-native-async-storage/async-storage":
      optional: true
  checksum: 12626b8d7c8cab4a976f318a50d56ef3496e3b20d40983cd48a64b0fbd8f329769b74d911e563a0a11e89639c8493e6eb07694a38dfa97f5a1b487c7fcf6060a
  languageName: node
  linkType: hard

"@firebase/component@npm:0.6.7":
  version: 0.6.7
  resolution: "@firebase/component@npm:0.6.7"
  dependencies:
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  checksum: 8a44ef91bec31d062fd45ebe5bf8b84f0d6fd654147a31f38a227e1a2456fb23044fffde858699555c38ecd65c03c9ae8294e419ea47461f265542167a4c9f6d
  languageName: node
  linkType: hard

"@firebase/database-compat@npm:1.0.5":
  version: 1.0.5
  resolution: "@firebase/database-compat@npm:1.0.5"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/database": 1.0.5
    "@firebase/database-types": 1.0.3
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  checksum: 90c47d753c349707d60fbdf35138b4a4f723560d7809c463459efa5f9d1919f2a6e93a561f59e25cb588f8b1a5d0a9187b9d2d3854cc8b9efe57872bddf0168d
  languageName: node
  linkType: hard

"@firebase/database-types@npm:1.0.3":
  version: 1.0.3
  resolution: "@firebase/database-types@npm:1.0.3"
  dependencies:
    "@firebase/app-types": 0.9.2
    "@firebase/util": 1.9.6
  checksum: 4fe04973df96f0b51eac79c0fbb9b02eed57fa030dc2a9ddd8eab37d2a82fbd97f8d6b48f8d5ee49401d3f8bdc778159406f9760e85868539b468caa7df68310
  languageName: node
  linkType: hard

"@firebase/database@npm:1.0.5":
  version: 1.0.5
  resolution: "@firebase/database@npm:1.0.5"
  dependencies:
    "@firebase/app-check-interop-types": 0.3.2
    "@firebase/auth-interop-types": 0.2.3
    "@firebase/component": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    faye-websocket: 0.11.4
    tslib: ^2.1.0
  checksum: 8ffb42f0c708e349a380dc4f27c6b01d9cda167822f6e4efab0e3d9e68769d3b8212be158c0cb1e246c385c877f8201994dbe28bd1dce8340bcb1615536b0b36
  languageName: node
  linkType: hard

"@firebase/firestore-compat@npm:0.3.32":
  version: 0.3.32
  resolution: "@firebase/firestore-compat@npm:0.3.32"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/firestore": 4.6.3
    "@firebase/firestore-types": 3.0.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app-compat": 0.x
  checksum: 8bec83d365afefb52098ee0c93ac79c52ba4b43a63045db875b709aec063d82be85531acde65472b75f26a6408b183fa0a9be6956c1f5e4518501589dc2cdfa4
  languageName: node
  linkType: hard

"@firebase/firestore-types@npm:3.0.2":
  version: 3.0.2
  resolution: "@firebase/firestore-types@npm:3.0.2"
  peerDependencies:
    "@firebase/app-types": 0.x
    "@firebase/util": 1.x
  checksum: b275107a2d65aecb1fe66d44feac4d74f8bd48f309bdfe53e6c84e5ba4787fae0700d8d045b07939cbc7c3c7c19935d1ca8efab9eda4f5f8ad50e3ee330b90ca
  languageName: node
  linkType: hard

"@firebase/firestore@npm:4.6.3":
  version: 4.6.3
  resolution: "@firebase/firestore@npm:4.6.3"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    "@firebase/webchannel-wrapper": 1.0.0
    "@grpc/grpc-js": ~1.9.0
    "@grpc/proto-loader": ^0.7.8
    tslib: ^2.1.0
    undici: 5.28.4
  peerDependencies:
    "@firebase/app": 0.x
  checksum: 42060c35c336ea64afef24b107f6aa873653e019717905d178e5c5ac10a6ead9c42d6c301db2c4812bcd3182db74a52281517290f81d9b03a3601aee125d8775
  languageName: node
  linkType: hard

"@firebase/functions-compat@npm:0.3.11":
  version: 0.3.11
  resolution: "@firebase/functions-compat@npm:0.3.11"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/functions": 0.11.5
    "@firebase/functions-types": 0.6.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app-compat": 0.x
  checksum: 86de792ad8a6d6b94e90e09bbefba3ed8bb15f1f0739b2188f70517973d30b0f1aa44dc154e21b539dc197856d61239a9274ea64f5df9ae71972c8bf4b73bf7a
  languageName: node
  linkType: hard

"@firebase/functions-types@npm:0.6.2":
  version: 0.6.2
  resolution: "@firebase/functions-types@npm:0.6.2"
  checksum: 7973e0de0b709295e7e885929ff10d35dec5a1d92c0f827f9580abc3860d4ccfebf7af69bbbceabc9b62eb88642028a6373a14b5f7be388fa40211e64c5147fb
  languageName: node
  linkType: hard

"@firebase/functions@npm:0.11.5":
  version: 0.11.5
  resolution: "@firebase/functions@npm:0.11.5"
  dependencies:
    "@firebase/app-check-interop-types": 0.3.2
    "@firebase/auth-interop-types": 0.2.3
    "@firebase/component": 0.6.7
    "@firebase/messaging-interop-types": 0.2.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
    undici: 5.28.4
  peerDependencies:
    "@firebase/app": 0.x
  checksum: 01d4f973f3b3a4893d65c60384a1932cbe6ecbb51fcdd7e1eb3b2a316b46036e5d0abc8aa4184946a6a7dc7eb0ddacd1e7867440d187c555665cc29bf8d45435
  languageName: node
  linkType: hard

"@firebase/installations-compat@npm:0.2.7":
  version: 0.2.7
  resolution: "@firebase/installations-compat@npm:0.2.7"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/installations": 0.6.7
    "@firebase/installations-types": 0.5.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app-compat": 0.x
  checksum: ac4270233edeccda701c8f4de2527e8e150e32a5dee4fd80bc07442b3fc89e46619badfed938b55d134258b553a8394d8ea9282ce0a87d3e38c3539a216a8391
  languageName: node
  linkType: hard

"@firebase/installations-types@npm:0.5.2":
  version: 0.5.2
  resolution: "@firebase/installations-types@npm:0.5.2"
  peerDependencies:
    "@firebase/app-types": 0.x
  checksum: 19f31ab2982198ffed0cf0e57307bcf17dbc994f6ec707f508c151108b09a67472728f2ee744548bf079b458a982ac865d2fd6d6879fc7d16a7b7dbfa7263fa8
  languageName: node
  linkType: hard

"@firebase/installations@npm:0.6.7":
  version: 0.6.7
  resolution: "@firebase/installations@npm:0.6.7"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/util": 1.9.6
    idb: 7.1.1
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app": 0.x
  checksum: 617ac87386308d0196360d2f43dcc2fcaf5f29795940bee1b18f1989342d31c9ea466b5d37ec1d2649a64870b72d9abb1469988b0e6ddb5c9e1f859fdd29c288
  languageName: node
  linkType: hard

"@firebase/logger@npm:0.4.2":
  version: 0.4.2
  resolution: "@firebase/logger@npm:0.4.2"
  dependencies:
    tslib: ^2.1.0
  checksum: a0d288debe32108095af691fa8797c5ee2023b0f4e0f5024992f7e49b5353d1fb0280ea950d8bfd5d93af514cf839f663fd3559303d0591fcb8b0efe3d879f0e
  languageName: node
  linkType: hard

"@firebase/messaging-compat@npm:0.2.9":
  version: 0.2.9
  resolution: "@firebase/messaging-compat@npm:0.2.9"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/messaging": 0.12.9
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app-compat": 0.x
  checksum: 6bc6a10698162b5f47ffb9ad314ac6b844d7dfe292c9dc0e2c8ed2a8b33e4aa171aa82d8b1778f486b8f4e2ce4c122e93d69db8f13be21a981dcabebe4580447
  languageName: node
  linkType: hard

"@firebase/messaging-interop-types@npm:0.2.2":
  version: 0.2.2
  resolution: "@firebase/messaging-interop-types@npm:0.2.2"
  checksum: 75dc6c7d3951866145e2706562cc38d98de0d8c23a08c04b41c5641e89da424f85af4606294f1430de3c191be6c74cf7e2be55bab810720f70ba4c2f20297dbb
  languageName: node
  linkType: hard

"@firebase/messaging@npm:0.12.9":
  version: 0.12.9
  resolution: "@firebase/messaging@npm:0.12.9"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/installations": 0.6.7
    "@firebase/messaging-interop-types": 0.2.2
    "@firebase/util": 1.9.6
    idb: 7.1.1
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app": 0.x
  checksum: c45089f0373d5e945d2c7ded924890e2d296c2fb722ce821bedf05970bf894c92e818dd751a53f086cdc5344696974a3638f45d1b7e8606ba8fd4689be4c94db
  languageName: node
  linkType: hard

"@firebase/performance-compat@npm:0.2.7":
  version: 0.2.7
  resolution: "@firebase/performance-compat@npm:0.2.7"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/performance": 0.6.7
    "@firebase/performance-types": 0.2.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app-compat": 0.x
  checksum: dd3db9c513a4a289da916f1ad1b2137d738613570a354458157d0b17e9f7ea6dcb886ef1e84469cbf84f4115df9d2dce99fc01cb61e6f85740d432bc2c8ac321
  languageName: node
  linkType: hard

"@firebase/performance-types@npm:0.2.2":
  version: 0.2.2
  resolution: "@firebase/performance-types@npm:0.2.2"
  checksum: ff4c6b445629ba30a182e476d9ec0c1640a4fdf258716ebfe98573196d8ca67000d588846cf7f17d2e2144315b55146a70a6b0b184e7a05c446eb18cf0b6b8e3
  languageName: node
  linkType: hard

"@firebase/performance@npm:0.6.7":
  version: 0.6.7
  resolution: "@firebase/performance@npm:0.6.7"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/installations": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app": 0.x
  checksum: e309cfea0eafdc3fecdf40288e061567649390fc2f889f6d69313c0a3dfa03e98b403d9f5e34613c4356cb9f533fbccbb17d4d4d5f784070c76c755fbd90834a
  languageName: node
  linkType: hard

"@firebase/remote-config-compat@npm:0.2.7":
  version: 0.2.7
  resolution: "@firebase/remote-config-compat@npm:0.2.7"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/remote-config": 0.4.7
    "@firebase/remote-config-types": 0.3.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app-compat": 0.x
  checksum: 90f1990f5148c171ec01990cc4bf7b6af0eb98b9d28f78eee88f82d52531977cbc088c8d25c348d2a68b45dae6cf247c79b8c50a64c13d22b3b2cd0be83a481a
  languageName: node
  linkType: hard

"@firebase/remote-config-types@npm:0.3.2":
  version: 0.3.2
  resolution: "@firebase/remote-config-types@npm:0.3.2"
  checksum: 15dfab0febb7eb382ba1d702b677a72d11f9a98379464a9047349b844c36edb572ba7f353681ad65ece3cd9bee387a945c0939b13ae5c5f221fa264671152adc
  languageName: node
  linkType: hard

"@firebase/remote-config@npm:0.4.7":
  version: 0.4.7
  resolution: "@firebase/remote-config@npm:0.4.7"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/installations": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app": 0.x
  checksum: 00fb5c06826b089f175dc5d281b12c2a1355899a213b0cec2429e191a48a5bc0a348011bf2cbaa1fc131cb6c87d48bfee68ad175bbd56ab284212f9a19394182
  languageName: node
  linkType: hard

"@firebase/storage-compat@npm:0.3.8":
  version: 0.3.8
  resolution: "@firebase/storage-compat@npm:0.3.8"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/storage": 0.12.5
    "@firebase/storage-types": 0.8.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app-compat": 0.x
  checksum: c08c3728ee6815f3c4a5f6b8b8de7067bee02c17a19fe4bd9b29a383afe47188a984e6272f8e00ac33d9aaf1b0bbeb8922b85821ec06bbd0ece312f33dfed1c7
  languageName: node
  linkType: hard

"@firebase/storage-types@npm:0.8.2":
  version: 0.8.2
  resolution: "@firebase/storage-types@npm:0.8.2"
  peerDependencies:
    "@firebase/app-types": 0.x
    "@firebase/util": 1.x
  checksum: c992f49cc5d326a096e2ec350464c2b0934fc7259c6616e11279bc970db980545d46d150a8edcaa48d028d6fed2ee28c25ff4d5c3ade46ec48c96444d7e11198
  languageName: node
  linkType: hard

"@firebase/storage@npm:0.12.5":
  version: 0.12.5
  resolution: "@firebase/storage@npm:0.12.5"
  dependencies:
    "@firebase/component": 0.6.7
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
    undici: 5.28.4
  peerDependencies:
    "@firebase/app": 0.x
  checksum: 8b163e3dae31d823b57f283a5bd20a4307e298c9acbfc073f9fdf4a6684d71d129fb98b30807404e770e67c7ee51e7f2cb90f37dcf275e85ecdb68db481f5bb6
  languageName: node
  linkType: hard

"@firebase/util@npm:1.9.6":
  version: 1.9.6
  resolution: "@firebase/util@npm:1.9.6"
  dependencies:
    tslib: ^2.1.0
  checksum: 6c9b5dc4e271018aa90cbbd4994b732a96e2395eca0c138eb04f7b8380aac76424b18c61d0643b9f064006d307179864ab0a41b9bbfc45641bee16efdb8a476c
  languageName: node
  linkType: hard

"@firebase/vertexai-preview@npm:0.0.2":
  version: 0.0.2
  resolution: "@firebase/vertexai-preview@npm:0.0.2"
  dependencies:
    "@firebase/app-check-interop-types": 0.3.2
    "@firebase/component": 0.6.7
    "@firebase/logger": 0.4.2
    "@firebase/util": 1.9.6
    tslib: ^2.1.0
  peerDependencies:
    "@firebase/app": 0.x
    "@firebase/app-types": 0.x
  checksum: 4c7c67bc91bdc08702db0e19d9a476d2fc37e5640be732264f319ffe5a833c606b8192a1bfef9a805a8f11e85c9bc5ea5daf1ed386057a9dcd0624b4c3dce4f3
  languageName: node
  linkType: hard

"@firebase/webchannel-wrapper@npm:1.0.0":
  version: 1.0.0
  resolution: "@firebase/webchannel-wrapper@npm:1.0.0"
  checksum: 9e9f2070256ae2cce3cbd79b03f9590719ef7ea9591bc5f6ba804801ac5ca76731bc0f8d687d1ae4f629622741f5044d6804d7e2403e7b90f4fe70ef395ef5a5
  languageName: node
  linkType: hard

"@grpc/grpc-js@npm:~1.9.0":
  version: 1.9.15
  resolution: "@grpc/grpc-js@npm:1.9.15"
  dependencies:
    "@grpc/proto-loader": ^0.7.8
    "@types/node": ">=12.12.47"
  checksum: 5b0f84052ad6610fff7919cae99c79c1182b01d2f529f6e64e1189e902a90abcb6f828a119df8e4abcdab8fa1ac5d5975fe200220293a1ced126c536f3bc1374
  languageName: node
  linkType: hard

"@grpc/proto-loader@npm:^0.7.8":
  version: 0.7.13
  resolution: "@grpc/proto-loader@npm:0.7.13"
  dependencies:
    lodash.camelcase: ^4.3.0
    long: ^5.0.0
    protobufjs: ^7.2.5
    yargs: ^17.7.2
  bin:
    proto-loader-gen-types: build/bin/proto-loader-gen-types.js
  checksum: 399c1b8a4627f93dc31660d9636ea6bf58be5675cc7581e3df56a249369e5be02c6cd0d642c5332b0d5673bc8621619bc06fb045aa3e8f57383737b5d35930dc
  languageName: node
  linkType: hard

"@hapi/hoek@npm:^9.0.0, @hapi/hoek@npm:^9.3.0":
  version: 9.3.0
  resolution: "@hapi/hoek@npm:9.3.0"
  checksum: 4771c7a776242c3c022b168046af4e324d116a9d2e1d60631ee64f474c6e38d1bb07092d898bf95c7bc5d334c5582798a1456321b2e53ca817d4e7c88bc25b43
  languageName: node
  linkType: hard

"@hapi/topo@npm:^5.1.0":
  version: 5.1.0
  resolution: "@hapi/topo@npm:5.1.0"
  dependencies:
    "@hapi/hoek": ^9.0.0
  checksum: 604dfd5dde76d5c334bd03f9001fce69c7ce529883acf92da96f4fe7e51221bf5e5110e964caca287a6a616ba027c071748ab636ff178ad750547fba611d6014
  languageName: node
  linkType: hard

"@hookform/resolvers@npm:^3.6.0":
  version: 3.9.0
  resolution: "@hookform/resolvers@npm:3.9.0"
  peerDependencies:
    react-hook-form: ^7.0.0
  checksum: 64a1e77ea2eeeba521ec624f2cea33ec2d20c60de12847b59520393780a9c8b92b3d76b7b3eeabadef85e8c776826f7bc9016fa90a890580c4ed75503c060dd1
  languageName: node
  linkType: hard

"@humanwhocodes/module-importer@npm:^1.0.1":
  version: 1.0.1
  resolution: "@humanwhocodes/module-importer@npm:1.0.1"
  checksum: 0fd22007db8034a2cdf2c764b140d37d9020bbfce8a49d3ec5c05290e77d4b0263b1b972b752df8c89e5eaa94073408f2b7d977aed131faf6cf396ebb5d7fb61
  languageName: node
  linkType: hard

"@humanwhocodes/retry@npm:^0.3.0":
  version: 0.3.0
  resolution: "@humanwhocodes/retry@npm:0.3.0"
  checksum: 4349cb8b60466a000e945fde8f8551cefb01ebba22ead4a92ac7b145f67f5da6b52e5a1e0c53185d732d0a49958ac29327934a4a5ac1d0bc20efb4429a4f7bf7
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: ^5.1.2
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: ^7.0.1
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: ^8.1.0
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 4a473b9b32a7d4d3cfb7a614226e555091ff0c5a29a1734c28c72a182c2f6699b26fc6b5c2131dfd841e86b185aea714c72201d7c98c2fba5f17709333a67aeb
  languageName: node
  linkType: hard

"@isaacs/ttlcache@npm:^1.4.1":
  version: 1.4.1
  resolution: "@isaacs/ttlcache@npm:1.4.1"
  checksum: b99f0918faf1eba405b6bc3421584282b2edc46cca23f8d8e112a643bf6e4506c6c53a4525901118e229d19c5719bbec3028ec438d758fd71081f6c32af871ec
  languageName: node
  linkType: hard

"@istanbuljs/load-nyc-config@npm:^1.0.0":
  version: 1.1.0
  resolution: "@istanbuljs/load-nyc-config@npm:1.1.0"
  dependencies:
    camelcase: ^5.3.1
    find-up: ^4.1.0
    get-package-type: ^0.1.0
    js-yaml: ^3.13.1
    resolve-from: ^5.0.0
  checksum: d578da5e2e804d5c93228450a1380e1a3c691de4953acc162f387b717258512a3e07b83510a936d9fab03eac90817473917e24f5d16297af3867f59328d58568
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2, @istanbuljs/schema@npm:^0.1.3":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 5282759d961d61350f33d9118d16bcaed914ebf8061a52f4fa474b2cb08720c9c81d165e13b82f2e5a8a212cc5af482f0c6fc1ac27b9e067e5394c9a6ed186c9
  languageName: node
  linkType: hard

"@jest/console@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/console@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    "@types/node": "*"
    chalk: ^4.0.0
    jest-message-util: ^29.7.0
    jest-util: ^29.7.0
    slash: ^3.0.0
  checksum: 0e3624e32c5a8e7361e889db70b170876401b7d70f509a2538c31d5cd50deb0c1ae4b92dc63fe18a0902e0a48c590c21d53787a0df41a52b34fa7cab96c384d6
  languageName: node
  linkType: hard

"@jest/core@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/core@npm:29.7.0"
  dependencies:
    "@jest/console": ^29.7.0
    "@jest/reporters": ^29.7.0
    "@jest/test-result": ^29.7.0
    "@jest/transform": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    ansi-escapes: ^4.2.1
    chalk: ^4.0.0
    ci-info: ^3.2.0
    exit: ^0.1.2
    graceful-fs: ^4.2.9
    jest-changed-files: ^29.7.0
    jest-config: ^29.7.0
    jest-haste-map: ^29.7.0
    jest-message-util: ^29.7.0
    jest-regex-util: ^29.6.3
    jest-resolve: ^29.7.0
    jest-resolve-dependencies: ^29.7.0
    jest-runner: ^29.7.0
    jest-runtime: ^29.7.0
    jest-snapshot: ^29.7.0
    jest-util: ^29.7.0
    jest-validate: ^29.7.0
    jest-watcher: ^29.7.0
    micromatch: ^4.0.4
    pretty-format: ^29.7.0
    slash: ^3.0.0
    strip-ansi: ^6.0.0
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: af759c9781cfc914553320446ce4e47775ae42779e73621c438feb1e4231a5d4862f84b1d8565926f2d1aab29b3ec3dcfdc84db28608bdf5f29867124ebcfc0d
  languageName: node
  linkType: hard

"@jest/create-cache-key-function@npm:^29.6.3":
  version: 29.7.0
  resolution: "@jest/create-cache-key-function@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
  checksum: 681bc761fa1d6fa3dd77578d444f97f28296ea80755e90e46d1c8fa68661b9e67f54dd38b988742db636d26cf160450dc6011892cec98b3a7ceb58cad8ff3aae
  languageName: node
  linkType: hard

"@jest/environment@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/environment@npm:29.7.0"
  dependencies:
    "@jest/fake-timers": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    jest-mock: ^29.7.0
  checksum: 6fb398143b2543d4b9b8d1c6dbce83fa5247f84f550330604be744e24c2bd2178bb893657d62d1b97cf2f24baf85c450223f8237cccb71192c36a38ea2272934
  languageName: node
  linkType: hard

"@jest/expect-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect-utils@npm:29.7.0"
  dependencies:
    jest-get-type: ^29.6.3
  checksum: 75eb177f3d00b6331bcaa057e07c0ccb0733a1d0a1943e1d8db346779039cb7f103789f16e502f888a3096fb58c2300c38d1f3748b36a7fa762eb6f6d1b160ed
  languageName: node
  linkType: hard

"@jest/expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect@npm:29.7.0"
  dependencies:
    expect: ^29.7.0
    jest-snapshot: ^29.7.0
  checksum: a01cb85fd9401bab3370618f4b9013b90c93536562222d920e702a0b575d239d74cecfe98010aaec7ad464f67cf534a353d92d181646a4b792acaa7e912ae55e
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/fake-timers@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    "@sinonjs/fake-timers": ^10.0.2
    "@types/node": "*"
    jest-message-util: ^29.7.0
    jest-mock: ^29.7.0
    jest-util: ^29.7.0
  checksum: caf2bbd11f71c9241b458d1b5a66cbe95debc5a15d96442444b5d5c7ba774f523c76627c6931cca5e10e76f0d08761f6f1f01a608898f4751a0eee54fc3d8d00
  languageName: node
  linkType: hard

"@jest/globals@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/globals@npm:29.7.0"
  dependencies:
    "@jest/environment": ^29.7.0
    "@jest/expect": ^29.7.0
    "@jest/types": ^29.6.3
    jest-mock: ^29.7.0
  checksum: 97dbb9459135693ad3a422e65ca1c250f03d82b2a77f6207e7fa0edd2c9d2015fbe4346f3dc9ebff1678b9d8da74754d4d440b7837497f8927059c0642a22123
  languageName: node
  linkType: hard

"@jest/reporters@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/reporters@npm:29.7.0"
  dependencies:
    "@bcoe/v8-coverage": ^0.2.3
    "@jest/console": ^29.7.0
    "@jest/test-result": ^29.7.0
    "@jest/transform": ^29.7.0
    "@jest/types": ^29.6.3
    "@jridgewell/trace-mapping": ^0.3.18
    "@types/node": "*"
    chalk: ^4.0.0
    collect-v8-coverage: ^1.0.0
    exit: ^0.1.2
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    istanbul-lib-coverage: ^3.0.0
    istanbul-lib-instrument: ^6.0.0
    istanbul-lib-report: ^3.0.0
    istanbul-lib-source-maps: ^4.0.0
    istanbul-reports: ^3.1.3
    jest-message-util: ^29.7.0
    jest-util: ^29.7.0
    jest-worker: ^29.7.0
    slash: ^3.0.0
    string-length: ^4.0.1
    strip-ansi: ^6.0.0
    v8-to-istanbul: ^9.0.1
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 7eadabd62cc344f629024b8a268ecc8367dba756152b761bdcb7b7e570a3864fc51b2a9810cd310d85e0a0173ef002ba4528d5ea0329fbf66ee2a3ada9c40455
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/schemas@npm:29.6.3"
  dependencies:
    "@sinclair/typebox": ^0.27.8
  checksum: 910040425f0fc93cd13e68c750b7885590b8839066dfa0cd78e7def07bbb708ad869381f725945d66f2284de5663bbecf63e8fdd856e2ae6e261ba30b1687e93
  languageName: node
  linkType: hard

"@jest/source-map@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/source-map@npm:29.6.3"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.18
    callsites: ^3.0.0
    graceful-fs: ^4.2.9
  checksum: bcc5a8697d471396c0003b0bfa09722c3cd879ad697eb9c431e6164e2ea7008238a01a07193dfe3cbb48b1d258eb7251f6efcea36f64e1ebc464ea3c03ae2deb
  languageName: node
  linkType: hard

"@jest/test-result@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-result@npm:29.7.0"
  dependencies:
    "@jest/console": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/istanbul-lib-coverage": ^2.0.0
    collect-v8-coverage: ^1.0.0
  checksum: 67b6317d526e335212e5da0e768e3b8ab8a53df110361b80761353ad23b6aea4432b7c5665bdeb87658ea373b90fb1afe02ed3611ef6c858c7fba377505057fa
  languageName: node
  linkType: hard

"@jest/test-sequencer@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-sequencer@npm:29.7.0"
  dependencies:
    "@jest/test-result": ^29.7.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.7.0
    slash: ^3.0.0
  checksum: 73f43599017946be85c0b6357993b038f875b796e2f0950487a82f4ebcb115fa12131932dd9904026b4ad8be131fe6e28bd8d0aa93b1563705185f9804bff8bd
  languageName: node
  linkType: hard

"@jest/transform@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/transform@npm:29.7.0"
  dependencies:
    "@babel/core": ^7.11.6
    "@jest/types": ^29.6.3
    "@jridgewell/trace-mapping": ^0.3.18
    babel-plugin-istanbul: ^6.1.1
    chalk: ^4.0.0
    convert-source-map: ^2.0.0
    fast-json-stable-stringify: ^2.1.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.7.0
    jest-regex-util: ^29.6.3
    jest-util: ^29.7.0
    micromatch: ^4.0.4
    pirates: ^4.0.4
    slash: ^3.0.0
    write-file-atomic: ^4.0.2
  checksum: 0f8ac9f413903b3cb6d240102db848f2a354f63971ab885833799a9964999dd51c388162106a807f810071f864302cdd8e3f0c241c29ce02d85a36f18f3f40ab
  languageName: node
  linkType: hard

"@jest/types@npm:^26.6.2":
  version: 26.6.2
  resolution: "@jest/types@npm:26.6.2"
  dependencies:
    "@types/istanbul-lib-coverage": ^2.0.0
    "@types/istanbul-reports": ^3.0.0
    "@types/node": "*"
    "@types/yargs": ^15.0.0
    chalk: ^4.0.0
  checksum: a0bd3d2f22f26ddb23f41fddf6e6a30bf4fab2ce79ec1cb6ce6fdfaf90a72e00f4c71da91ec61e13db3b10c41de22cf49d07c57ff2b59171d64b29f909c1d8d6
  languageName: node
  linkType: hard

"@jest/types@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/types@npm:29.6.3"
  dependencies:
    "@jest/schemas": ^29.6.3
    "@types/istanbul-lib-coverage": ^2.0.0
    "@types/istanbul-reports": ^3.0.0
    "@types/node": "*"
    "@types/yargs": ^17.0.8
    chalk: ^4.0.0
  checksum: a0bcf15dbb0eca6bdd8ce61a3fb055349d40268622a7670a3b2eb3c3dbafe9eb26af59938366d520b86907b9505b0f9b29b85cec11579a9e580694b87cd90fcc
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.5
  resolution: "@jridgewell/gen-mapping@npm:0.3.5"
  dependencies:
    "@jridgewell/set-array": ^1.2.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.24
  checksum: ff7a1764ebd76a5e129c8890aa3e2f46045109dabde62b0b6c6a250152227647178ff2069ea234753a690d8f3c4ac8b5e7b267bbee272bffb7f3b0a370ab6e52
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 83b85f72c59d1c080b4cbec0fef84528963a1b5db34e4370fa4bd1e3ff64a0d80e0cee7369d11d73c704e0286fb2865b530acac7a871088fbe92b5edf1000870
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 832e513a85a588f8ed4f27d1279420d8547743cc37fcad5a5a76fc74bb895b013dfe614d0eed9cb860048e6546b798f8f2652020b4b2ba0561b05caa8c654b10
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.3":
  version: 0.3.6
  resolution: "@jridgewell/source-map@npm:0.3.6"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.5
    "@jridgewell/trace-mapping": ^0.3.25
  checksum: c9dc7d899397df95e3c9ec287b93c0b56f8e4453cd20743e2b9c8e779b1949bc3cccf6c01bb302779e46560eb45f62ea38d19fedd25370d814734268450a9f30
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 05df4f2538b3b0f998ea4c1cd34574d0feba216fa5d4ccaef0187d12abf82eafe6021cec8b49f9bb4d90f2ba4582ccc581e72986a5fcf4176ae0cfeb04cf52ec
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.12, @jridgewell/trace-mapping@npm:^0.3.18, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": ^3.1.0
    "@jridgewell/sourcemap-codec": ^1.4.14
  checksum: 9d3c40d225e139987b50c48988f8717a54a8c994d8a948ee42e1412e08988761d0754d7d10b803061cc3aebf35f92a5dbbab493bd0e1a9ef9e89a2130e83ba34
  languageName: node
  linkType: hard

"@motionone/animation@npm:^10.12.0":
  version: 10.18.0
  resolution: "@motionone/animation@npm:10.18.0"
  dependencies:
    "@motionone/easing": ^10.18.0
    "@motionone/types": ^10.17.1
    "@motionone/utils": ^10.18.0
    tslib: ^2.3.1
  checksum: 841cb9f4843a89e5e4560b9f960f52cbe78afc86f87c769f71e9edb3aadd53fb87982b7e11914428f228b29fd580756be531369c2ffac06432550afa4e87d1c3
  languageName: node
  linkType: hard

"@motionone/dom@npm:10.12.0":
  version: 10.12.0
  resolution: "@motionone/dom@npm:10.12.0"
  dependencies:
    "@motionone/animation": ^10.12.0
    "@motionone/generators": ^10.12.0
    "@motionone/types": ^10.12.0
    "@motionone/utils": ^10.12.0
    hey-listen: ^1.0.8
    tslib: ^2.3.1
  checksum: 123356f28e44362c4f081aae3df22e576f46bfcb07e01257b2ac64a115668448f29b8de67e4b6e692c5407cffb78ffe7cf9fa1bc064007482bab5dd23a69d380
  languageName: node
  linkType: hard

"@motionone/easing@npm:^10.18.0":
  version: 10.18.0
  resolution: "@motionone/easing@npm:10.18.0"
  dependencies:
    "@motionone/utils": ^10.18.0
    tslib: ^2.3.1
  checksum: 6bd37f7a9d5a88f868cc0ad6e47d2ba8d9fefd7da84fccfea7ed77ec08c2e6d1e42df88dda462665102a5cf03f748231a1a077de7054b5a8ccb0fbf36f61b1e7
  languageName: node
  linkType: hard

"@motionone/generators@npm:^10.12.0":
  version: 10.18.0
  resolution: "@motionone/generators@npm:10.18.0"
  dependencies:
    "@motionone/types": ^10.17.1
    "@motionone/utils": ^10.18.0
    tslib: ^2.3.1
  checksum: 51a0e075681697b11d0771998cac8c76a745f00141502f81adb953896992b7f49478965e4afe696bc83361afaae8d2f1057d71c25b21035fe67258ff73764f1c
  languageName: node
  linkType: hard

"@motionone/types@npm:^10.12.0, @motionone/types@npm:^10.17.1":
  version: 10.17.1
  resolution: "@motionone/types@npm:10.17.1"
  checksum: 3fa74db64e371e61a7f7669d7d541d11c9a8dd871032d59c69041e3b2e07a67ad2ed8767cb9273bac90eed4e1f76efc1f14c8673c2e9a288f6070ee0fef64a25
  languageName: node
  linkType: hard

"@motionone/utils@npm:^10.12.0, @motionone/utils@npm:^10.18.0":
  version: 10.18.0
  resolution: "@motionone/utils@npm:10.18.0"
  dependencies:
    "@motionone/types": ^10.17.1
    hey-listen: ^1.0.8
    tslib: ^2.3.1
  checksum: a27f9afde693a0cbbbcb33962b12bbe40dd2cfa514b0732f3c7953c5ef4beed738e1e8172a2de89e3b9f74a253ef0a70d7f3efb730be97b77d7176a3ffacb67a
  languageName: node
  linkType: hard

"@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1":
  version: 5.1.1-v1
  resolution: "@nicolo-ribaudo/eslint-scope-5-internals@npm:5.1.1-v1"
  dependencies:
    eslint-scope: 5.1.1
  checksum: f2e3b2d6a6e2d9f163ca22105910c9f850dc4897af0aea3ef0a5886b63d8e1ba6505b71c99cb78a3bba24a09557d601eb21c8dede3f3213753fcfef364eb0e57
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": 2.0.5
    run-parallel: ^1.1.9
  checksum: a970d595bd23c66c880e0ef1817791432dbb7acbb8d44b7e7d0e7a22f4521260d4a83f7f9fd61d44fda4610105577f8f58a60718105fb38352baed612fd79e59
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 012480b5ca9d97bff9261571dbbec7bbc6033f69cc92908bc1ecfad0792361a5a1994bc48674b9ef76419d056a03efadfce5a6cf6dbc0a36559571a7a483f6f0
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3, @nodelib/fs.walk@npm:^1.2.8":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": 2.1.5
    fastq: ^1.6.0
  checksum: 190c643f156d8f8f277bf2a6078af1ffde1fd43f498f187c2db24d35b4b4b5785c02c7dc52e356497b9a1b65b13edc996de08de0b961c32844364da02986dc53
  languageName: node
  linkType: hard

"@notifee/react-native@npm:^9.1.8":
  version: 9.1.8
  resolution: "@notifee/react-native@npm:9.1.8"
  peerDependencies:
    react-native: "*"
  checksum: a144f7c5aa54a7e774c95b3a91341653865dfa4c8540b34ead3f281edd733830975c4f86bdfe422467d18ae97879b75be09ca25bb1818ba2ecfc56808cb570b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^2.0.0":
  version: 2.2.2
  resolution: "@npmcli/agent@npm:2.2.2"
  dependencies:
    agent-base: ^7.1.0
    http-proxy-agent: ^7.0.0
    https-proxy-agent: ^7.0.1
    lru-cache: ^10.0.1
    socks-proxy-agent: ^8.0.3
  checksum: 67de7b88cc627a79743c88bab35e023e23daf13831a8aa4e15f998b92f5507b644d8ffc3788afc8e64423c612e0785a6a92b74782ce368f49a6746084b50d874
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^3.1.0":
  version: 3.1.1
  resolution: "@npmcli/fs@npm:3.1.1"
  dependencies:
    semver: ^7.3.5
  checksum: d960cab4b93adcb31ce223bfb75c5714edbd55747342efb67dcc2f25e023d930a7af6ece3e75f2f459b6f38fc14d031c766f116cd124fdc937fd33112579e820
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 6ad6a00fc4f2f2cfc6bff76fb1d88b8ee20bc0601e18ebb01b6d4be583733a860239a521a7fbca73b612e66705078809483549d2b18f370eb346c5155c8e4a0f
  languageName: node
  linkType: hard

"@pkgr/core@npm:^0.1.0":
  version: 0.1.1
  resolution: "@pkgr/core@npm:0.1.1"
  checksum: 6f25fd2e3008f259c77207ac9915b02f1628420403b2630c92a07ff963129238c9262afc9e84344c7a23b5cc1f3965e2cd17e3798219f5fd78a63d144d3cceba
  languageName: node
  linkType: hard

"@protobufjs/aspromise@npm:^1.1.1, @protobufjs/aspromise@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/aspromise@npm:1.1.2"
  checksum: 011fe7ef0826b0fd1a95935a033a3c0fd08483903e1aa8f8b4e0704e3233406abb9ee25350ec0c20bbecb2aad8da0dcea58b392bbd77d6690736f02c143865d2
  languageName: node
  linkType: hard

"@protobufjs/base64@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/base64@npm:1.1.2"
  checksum: 67173ac34de1e242c55da52c2f5bdc65505d82453893f9b51dc74af9fe4c065cf4a657a4538e91b0d4a1a1e0a0642215e31894c31650ff6e3831471061e1ee9e
  languageName: node
  linkType: hard

"@protobufjs/codegen@npm:^2.0.4":
  version: 2.0.4
  resolution: "@protobufjs/codegen@npm:2.0.4"
  checksum: 59240c850b1d3d0b56d8f8098dd04787dcaec5c5bd8de186fa548de86b86076e1c50e80144b90335e705a044edf5bc8b0998548474c2a10a98c7e004a1547e4b
  languageName: node
  linkType: hard

"@protobufjs/eventemitter@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/eventemitter@npm:1.1.0"
  checksum: 0369163a3d226851682f855f81413cbf166cd98f131edb94a0f67f79e75342d86e89df9d7a1df08ac28be2bc77e0a7f0200526bb6c2a407abbfee1f0262d5fd7
  languageName: node
  linkType: hard

"@protobufjs/fetch@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/fetch@npm:1.1.0"
  dependencies:
    "@protobufjs/aspromise": ^1.1.1
    "@protobufjs/inquire": ^1.1.0
  checksum: 3fce7e09eb3f1171dd55a192066450f65324fd5f7cc01a431df01bb00d0a895e6bfb5b0c5561ce157ee1d886349c90703d10a4e11a1a256418ff591b969b3477
  languageName: node
  linkType: hard

"@protobufjs/float@npm:^1.0.2":
  version: 1.0.2
  resolution: "@protobufjs/float@npm:1.0.2"
  checksum: 5781e1241270b8bd1591d324ca9e3a3128d2f768077a446187a049e36505e91bc4156ed5ac3159c3ce3d2ba3743dbc757b051b2d723eea9cd367bfd54ab29b2f
  languageName: node
  linkType: hard

"@protobufjs/inquire@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/inquire@npm:1.1.0"
  checksum: ca06f02eaf65ca36fb7498fc3492b7fc087bfcc85c702bac5b86fad34b692bdce4990e0ef444c1e2aea8c034227bd1f0484be02810d5d7e931c55445555646f4
  languageName: node
  linkType: hard

"@protobufjs/path@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/path@npm:1.1.2"
  checksum: 856eeb532b16a7aac071cacde5c5620df800db4c80cee6dbc56380524736205aae21e5ae47739114bf669ab5e8ba0e767a282ad894f3b5e124197cb9224445ee
  languageName: node
  linkType: hard

"@protobufjs/pool@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/pool@npm:1.1.0"
  checksum: d6a34fbbd24f729e2a10ee915b74e1d77d52214de626b921b2d77288bd8f2386808da2315080f2905761527cceffe7ec34c7647bd21a5ae41a25e8212ff79451
  languageName: node
  linkType: hard

"@protobufjs/utf8@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/utf8@npm:1.1.0"
  checksum: f9bf3163d13aaa3b6f5e6fbf37a116e094ea021c0e1f2a7ccd0e12a29e2ce08dafba4e8b36e13f8ed7397e1591610ce880ed1289af4d66cf4ace8a36a9557278
  languageName: node
  linkType: hard

"@react-native-community/blur@npm:^4.4.0":
  version: 4.4.0
  resolution: "@react-native-community/blur@npm:4.4.0"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 907adeba48ce148f7ba55791011e2cb4ede5c7b6a11497cc9631ad345e865a817dae23f066a39b75ac5fd15d4cbc1f03f4b2d2f0fd8c6a6084a11649c842698a
  languageName: node
  linkType: hard

"@react-native-community/cli-clean@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-clean@npm:13.6.6"
  dependencies:
    "@react-native-community/cli-tools": 13.6.6
    chalk: ^4.1.2
    execa: ^5.0.0
    fast-glob: ^3.3.2
  checksum: ac230d0bc73a9c72cd32fdf6fdb51581db6a46150f6fa33e9b5055be39464e545f4f2ea90f078e2d20c90a4b7a95db8fa810ae6411c4fa692cade1946add012b
  languageName: node
  linkType: hard

"@react-native-community/cli-config@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-config@npm:13.6.6"
  dependencies:
    "@react-native-community/cli-tools": 13.6.6
    chalk: ^4.1.2
    cosmiconfig: ^5.1.0
    deepmerge: ^4.3.0
    fast-glob: ^3.3.2
    joi: ^17.2.1
  checksum: 254272884c417ab50aaf4463683ccc79f82dd152e3905624eb0b433042de3ed4295d129261eb7216dd1b13457ab0fa20a4bc661fc40f106af0964ec09a2325fe
  languageName: node
  linkType: hard

"@react-native-community/cli-debugger-ui@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-debugger-ui@npm:13.6.6"
  dependencies:
    serve-static: ^1.13.1
  checksum: 11c75024d38e04a04a99ecc58727151ebff10dbfcbf8fcfc9a9c38811962874ae01dfcc32d6986e399d6c700197bf5db27b5919fc75a10225d3b379d4e6b9a5b
  languageName: node
  linkType: hard

"@react-native-community/cli-doctor@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-doctor@npm:13.6.6"
  dependencies:
    "@react-native-community/cli-config": 13.6.6
    "@react-native-community/cli-platform-android": 13.6.6
    "@react-native-community/cli-platform-apple": 13.6.6
    "@react-native-community/cli-platform-ios": 13.6.6
    "@react-native-community/cli-tools": 13.6.6
    chalk: ^4.1.2
    command-exists: ^1.2.8
    deepmerge: ^4.3.0
    envinfo: ^7.10.0
    execa: ^5.0.0
    hermes-profile-transformer: ^0.0.6
    node-stream-zip: ^1.9.1
    ora: ^5.4.1
    semver: ^7.5.2
    strip-ansi: ^5.2.0
    wcwidth: ^1.0.1
    yaml: ^2.2.1
  checksum: 388b839a627bdc3523277846a288502f81b14928195be84722c721e0d62942e51fabe35f0a8dd66462ee4c5f3d1193078b5e473990fba869d40ae16dd6d18df7
  languageName: node
  linkType: hard

"@react-native-community/cli-hermes@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-hermes@npm:13.6.6"
  dependencies:
    "@react-native-community/cli-platform-android": 13.6.6
    "@react-native-community/cli-tools": 13.6.6
    chalk: ^4.1.2
    hermes-profile-transformer: ^0.0.6
  checksum: 9370114c47c0f53fa1af0c25fcc2feb5392783099a570099d29d461b355e8bc80e9c5fb51cd50b207435a87069cd1bc6c70efca6a0959b9593e444b401ef003d
  languageName: node
  linkType: hard

"@react-native-community/cli-platform-android@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-platform-android@npm:13.6.6"
  dependencies:
    "@react-native-community/cli-tools": 13.6.6
    chalk: ^4.1.2
    execa: ^5.0.0
    fast-glob: ^3.3.2
    fast-xml-parser: ^4.2.4
    logkitty: ^0.7.1
  checksum: 426833e8a4e925be9447840c34e70d0a1c5a11d0a2177fef91849ce3977bbad346486a4599170361b7d05c6bf9f676ef9d86876da3a0f2534a9c31298a8a7195
  languageName: node
  linkType: hard

"@react-native-community/cli-platform-apple@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-platform-apple@npm:13.6.6"
  dependencies:
    "@react-native-community/cli-tools": 13.6.6
    chalk: ^4.1.2
    execa: ^5.0.0
    fast-glob: ^3.3.2
    fast-xml-parser: ^4.0.12
    ora: ^5.4.1
  checksum: 480fa5f2dc12188c2d1a42d258a845701a1664ea3a9a6a13c5b51476a0ee9571e044906296aa6fa1f3d3ef14f78f4317ae8e3cc0fef576ee498e4d8a306bb07a
  languageName: node
  linkType: hard

"@react-native-community/cli-platform-ios@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-platform-ios@npm:13.6.6"
  dependencies:
    "@react-native-community/cli-platform-apple": 13.6.6
  checksum: f64f8eee493a05a8812f5f36ad3cc079a74817fa20ef9e3a17894ea7bc34cc37bad31c0969062b895a9e195b2dc91ceea543ade3126dc181d7b5eaafd6c527e4
  languageName: node
  linkType: hard

"@react-native-community/cli-server-api@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-server-api@npm:13.6.6"
  dependencies:
    "@react-native-community/cli-debugger-ui": 13.6.6
    "@react-native-community/cli-tools": 13.6.6
    compression: ^1.7.1
    connect: ^3.6.5
    errorhandler: ^1.5.1
    nocache: ^3.0.1
    pretty-format: ^26.6.2
    serve-static: ^1.13.1
    ws: ^6.2.2
  checksum: 48a044811b6efc4139afa81fedd53645da8a56a4531449091ff95a6fd4d2bb228747db1bfdac9790826036fb019d869eda1e5fc729dfccc8ca65703210a405c5
  languageName: node
  linkType: hard

"@react-native-community/cli-tools@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-tools@npm:13.6.6"
  dependencies:
    appdirsjs: ^1.2.4
    chalk: ^4.1.2
    execa: ^5.0.0
    find-up: ^5.0.0
    mime: ^2.4.1
    node-fetch: ^2.6.0
    open: ^6.2.0
    ora: ^5.4.1
    semver: ^7.5.2
    shell-quote: ^1.7.3
    sudo-prompt: ^9.0.0
  checksum: 645979ddf649f23583e2b83c2911a780406bcaa03423b07487924414e383b471512a0b76d34e708d0e5f47a1652cbd282dd505ef6ecdf7e4606b49a3b230d5a3
  languageName: node
  linkType: hard

"@react-native-community/cli-types@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli-types@npm:13.6.6"
  dependencies:
    joi: ^17.2.1
  checksum: f2c8ffcd2e68df552687d687cab663b5c6bf9c2b807ba156eecde109ffd08cad5aa179aeb482d6922a8adce59e14deba67e7916ed077da8986a0ab7a0a9a49c9
  languageName: node
  linkType: hard

"@react-native-community/cli@npm:13.6.6":
  version: 13.6.6
  resolution: "@react-native-community/cli@npm:13.6.6"
  dependencies:
    "@react-native-community/cli-clean": 13.6.6
    "@react-native-community/cli-config": 13.6.6
    "@react-native-community/cli-debugger-ui": 13.6.6
    "@react-native-community/cli-doctor": 13.6.6
    "@react-native-community/cli-hermes": 13.6.6
    "@react-native-community/cli-server-api": 13.6.6
    "@react-native-community/cli-tools": 13.6.6
    "@react-native-community/cli-types": 13.6.6
    chalk: ^4.1.2
    commander: ^9.4.1
    deepmerge: ^4.3.0
    execa: ^5.0.0
    find-up: ^4.1.0
    fs-extra: ^8.1.0
    graceful-fs: ^4.1.3
    prompts: ^2.4.2
    semver: ^7.5.2
  bin:
    react-native: build/bin.js
  checksum: 0b15b763ff97b7930e855373ed16280584ad704287991edcb66178281c75b409d1a1d51a8947054b2061efdf3fc913e3b9eee3d79f9733cae83d524cbc6d0088
  languageName: node
  linkType: hard

"@react-native-community/eslint-config@npm:^3.2.0":
  version: 3.2.0
  resolution: "@react-native-community/eslint-config@npm:3.2.0"
  dependencies:
    "@babel/core": ^7.14.0
    "@babel/eslint-parser": ^7.18.2
    "@react-native-community/eslint-plugin": ^1.1.0
    "@typescript-eslint/eslint-plugin": ^5.30.5
    "@typescript-eslint/parser": ^5.30.5
    eslint-config-prettier: ^8.5.0
    eslint-plugin-eslint-comments: ^3.2.0
    eslint-plugin-ft-flow: ^2.0.1
    eslint-plugin-jest: ^26.5.3
    eslint-plugin-prettier: ^4.2.1
    eslint-plugin-react: ^7.30.1
    eslint-plugin-react-hooks: ^4.6.0
    eslint-plugin-react-native: ^4.0.0
  peerDependencies:
    eslint: ">=8"
    prettier: ">=2"
  checksum: 0a2dce65dbe43067571d7a382cfcfb1cae041b319aff216116797389ef0e431865caf6f48925e3532f1879363dc9f6b15cf81fdc967879d544d54605fd617119
  languageName: node
  linkType: hard

"@react-native-community/eslint-plugin@npm:^1.1.0":
  version: 1.3.0
  resolution: "@react-native-community/eslint-plugin@npm:1.3.0"
  checksum: 5e04fa161fca6453299aed691695ea071fed8166c5da36935047eb6c169bc38c9d599e1ce20402b63cbcaf086a9bb63d2e88836be142cecabf61ba36954ccaae
  languageName: node
  linkType: hard

"@react-native-community/netinfo@npm:6.0.6":
  version: 6.0.6
  resolution: "@react-native-community/netinfo@npm:6.0.6"
  peerDependencies:
    react-native: ">=0.59"
  checksum: 4033b2143ea1a0ff894520b870206d07746ad237f3a015727f2ae3bff44bfc600afd0cdbad4e1a893450344b6780f65e8dedcfe2825dc859e6754618f8b988c4
  languageName: node
  linkType: hard

"@react-native-firebase/app@npm:^20.3.0":
  version: 20.3.0
  resolution: "@react-native-firebase/app@npm:20.3.0"
  dependencies:
    firebase: 10.12.2
    superstruct: ^0.6.2
  peerDependencies:
    expo: ">=47.0.0"
    react: "*"
    react-native: "*"
  peerDependenciesMeta:
    expo:
      optional: true
  checksum: 6bc4dd88c16f528a9a8224864ea03ae137c57fe152b3e2133d821ebe47656734e07e02be09ca54b56895febe215bf74ea656cef9a785b5c30f99b831e8b341e3
  languageName: node
  linkType: hard

"@react-native-firebase/messaging@npm:^20.3.0":
  version: 20.3.0
  resolution: "@react-native-firebase/messaging@npm:20.3.0"
  peerDependencies:
    "@react-native-firebase/app": 20.3.0
    expo: ">=47.0.0"
  peerDependenciesMeta:
    expo:
      optional: true
  checksum: 1fa5691bda665a4ee7aa6645d979297d37dadc1c0543e66b5eb45af8687b8580aa9b1b54ea9f4937d3356d2de3ec23c9608d8a8b88c021be9e833b108728a64a
  languageName: node
  linkType: hard

"@react-native/assets-registry@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/assets-registry@npm:0.74.83"
  checksum: 034ff52a5bec8dd7c2b31edb4ca4a09d537f10ec39b9a3a62c7e028a15905760c308d489697f6a40b051797bbe4bd0ac2f1d4ce526820f5f71393b3cf9a9397e
  languageName: node
  linkType: hard

"@react-native/babel-plugin-codegen@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/babel-plugin-codegen@npm:0.74.83"
  dependencies:
    "@react-native/codegen": 0.74.83
  checksum: fb46f66e243794a684599df74168ca86a2901bd3ce6440b2765381b636dbe5c36ad7acb9a0e44344ede16759c5d2524d9f97624003f49a4f6d231170992aad60
  languageName: node
  linkType: hard

"@react-native/babel-preset@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/babel-preset@npm:0.74.83"
  dependencies:
    "@babel/core": ^7.20.0
    "@babel/plugin-proposal-async-generator-functions": ^7.0.0
    "@babel/plugin-proposal-class-properties": ^7.18.0
    "@babel/plugin-proposal-export-default-from": ^7.0.0
    "@babel/plugin-proposal-logical-assignment-operators": ^7.18.0
    "@babel/plugin-proposal-nullish-coalescing-operator": ^7.18.0
    "@babel/plugin-proposal-numeric-separator": ^7.0.0
    "@babel/plugin-proposal-object-rest-spread": ^7.20.0
    "@babel/plugin-proposal-optional-catch-binding": ^7.0.0
    "@babel/plugin-proposal-optional-chaining": ^7.20.0
    "@babel/plugin-syntax-dynamic-import": ^7.8.0
    "@babel/plugin-syntax-export-default-from": ^7.0.0
    "@babel/plugin-syntax-flow": ^7.18.0
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.0.0
    "@babel/plugin-syntax-optional-chaining": ^7.0.0
    "@babel/plugin-transform-arrow-functions": ^7.0.0
    "@babel/plugin-transform-async-to-generator": ^7.20.0
    "@babel/plugin-transform-block-scoping": ^7.0.0
    "@babel/plugin-transform-classes": ^7.0.0
    "@babel/plugin-transform-computed-properties": ^7.0.0
    "@babel/plugin-transform-destructuring": ^7.20.0
    "@babel/plugin-transform-flow-strip-types": ^7.20.0
    "@babel/plugin-transform-function-name": ^7.0.0
    "@babel/plugin-transform-literals": ^7.0.0
    "@babel/plugin-transform-modules-commonjs": ^7.0.0
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.0.0
    "@babel/plugin-transform-parameters": ^7.0.0
    "@babel/plugin-transform-private-methods": ^7.22.5
    "@babel/plugin-transform-private-property-in-object": ^7.22.11
    "@babel/plugin-transform-react-display-name": ^7.0.0
    "@babel/plugin-transform-react-jsx": ^7.0.0
    "@babel/plugin-transform-react-jsx-self": ^7.0.0
    "@babel/plugin-transform-react-jsx-source": ^7.0.0
    "@babel/plugin-transform-runtime": ^7.0.0
    "@babel/plugin-transform-shorthand-properties": ^7.0.0
    "@babel/plugin-transform-spread": ^7.0.0
    "@babel/plugin-transform-sticky-regex": ^7.0.0
    "@babel/plugin-transform-typescript": ^7.5.0
    "@babel/plugin-transform-unicode-regex": ^7.0.0
    "@babel/template": ^7.0.0
    "@react-native/babel-plugin-codegen": 0.74.83
    babel-plugin-transform-flow-enums: ^0.0.2
    react-refresh: ^0.14.0
  peerDependencies:
    "@babel/core": "*"
  checksum: 6a3ee960625b6da70e87c8da44a518b77b4a0b70bd143dee6017ce8957b357ee10b511a9355f175cbbb3a9a0a6389c862607d52fcb6dc9458f1dd4a5ebe3d81c
  languageName: node
  linkType: hard

"@react-native/codegen@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/codegen@npm:0.74.83"
  dependencies:
    "@babel/parser": ^7.20.0
    glob: ^7.1.1
    hermes-parser: 0.19.1
    invariant: ^2.2.4
    jscodeshift: ^0.14.0
    mkdirp: ^0.5.1
    nullthrows: ^1.1.1
  peerDependencies:
    "@babel/preset-env": ^7.1.6
  checksum: 8408f9f4d4693544cb080668426b6833ca662adc6df2fad0657d6a35775a44acbf6358fd2a1e7c1fd9c84b15173ac1231d3db153a99c4a005898979a60181e46
  languageName: node
  linkType: hard

"@react-native/community-cli-plugin@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/community-cli-plugin@npm:0.74.83"
  dependencies:
    "@react-native-community/cli-server-api": 13.6.6
    "@react-native-community/cli-tools": 13.6.6
    "@react-native/dev-middleware": 0.74.83
    "@react-native/metro-babel-transformer": 0.74.83
    chalk: ^4.0.0
    execa: ^5.1.1
    metro: ^0.80.3
    metro-config: ^0.80.3
    metro-core: ^0.80.3
    node-fetch: ^2.2.0
    querystring: ^0.2.1
    readline: ^1.3.0
  checksum: ef71d38baae09d37657a03e79a084308b882e240faaeddf12adbb36dff77393273ea4e8f57c28c7004cd23323313f61e08832584957caf6fb51bd8014bd4b265
  languageName: node
  linkType: hard

"@react-native/debugger-frontend@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/debugger-frontend@npm:0.74.83"
  checksum: 8bdf8ae7103b740c9ddf421b58c4fc4f182bccc36a55443997fafa69cad2aeb800596d239746fb3e8f714b1c044e31d007db4a1ab490eeafdb4885ead9b512f4
  languageName: node
  linkType: hard

"@react-native/dev-middleware@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/dev-middleware@npm:0.74.83"
  dependencies:
    "@isaacs/ttlcache": ^1.4.1
    "@react-native/debugger-frontend": 0.74.83
    "@rnx-kit/chromium-edge-launcher": ^1.0.0
    chrome-launcher: ^0.15.2
    connect: ^3.6.5
    debug: ^2.2.0
    node-fetch: ^2.2.0
    nullthrows: ^1.1.1
    open: ^7.0.3
    selfsigned: ^2.4.1
    serve-static: ^1.13.1
    temp-dir: ^2.0.0
    ws: ^6.2.2
  checksum: 8324c52af4dffcdce860ffa12795de112635eefab01e3412938dfbda248675a77a8f85c452c93cc485b43b375b6b4c43245977563cbd1221729b909af98f38ba
  languageName: node
  linkType: hard

"@react-native/eslint-config@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/eslint-config@npm:0.74.83"
  dependencies:
    "@babel/core": ^7.20.0
    "@babel/eslint-parser": ^7.20.0
    "@react-native/eslint-plugin": 0.74.83
    "@typescript-eslint/eslint-plugin": ^6.7.4
    "@typescript-eslint/parser": ^6.7.4
    eslint-config-prettier: ^8.5.0
    eslint-plugin-eslint-comments: ^3.2.0
    eslint-plugin-ft-flow: ^2.0.1
    eslint-plugin-jest: ^26.5.3
    eslint-plugin-prettier: ^4.2.1
    eslint-plugin-react: ^7.30.1
    eslint-plugin-react-hooks: ^4.6.0
    eslint-plugin-react-native: ^4.0.0
  peerDependencies:
    eslint: ">=8"
    prettier: ">=2"
  checksum: 9b54b369ffe858bf3b4703d21021e2dc4a3d671bf2a56c02bc13ea638413b067ce4c578b90459cfcb83bed3ae41fb58af678de772febc66fc7602a3b63d23aac
  languageName: node
  linkType: hard

"@react-native/eslint-plugin@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/eslint-plugin@npm:0.74.83"
  checksum: 85d1152ccb34812d5edef4abad1f522352332b08ae56e9c4ff1d9eb7742344aeade7649166259449b6002252cb4db3576bae03d3897618caf20d8cb203673aa9
  languageName: node
  linkType: hard

"@react-native/gradle-plugin@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/gradle-plugin@npm:0.74.83"
  checksum: c29eeedf3fcfe58ed097ba841b67baed61275487ef35da136c8a19dbe59120966f034ab1fc2135e321bc7331ddb949db2fcd8426c073067bffd113f60d051615
  languageName: node
  linkType: hard

"@react-native/js-polyfills@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/js-polyfills@npm:0.74.83"
  checksum: 065b01a43b63e5d62cfb55f1f960e24b2ea79c669863a4dca6ca2c25446fe6ffb7bdf567c81c7fda7cf2b86f1975eeec6883b08a46b99feb7ef40eab087e8b6b
  languageName: node
  linkType: hard

"@react-native/metro-babel-transformer@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/metro-babel-transformer@npm:0.74.83"
  dependencies:
    "@babel/core": ^7.20.0
    "@react-native/babel-preset": 0.74.83
    hermes-parser: 0.19.1
    nullthrows: ^1.1.1
  peerDependencies:
    "@babel/core": "*"
  checksum: 7620ce25ef460ff9461c4a0a0b9765d95300382032a451eea17f6ea1d369302623a98d3f2def4c906082c61c436aa0d978118626ec291e1c42aaa33fa28255b2
  languageName: node
  linkType: hard

"@react-native/metro-config@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/metro-config@npm:0.74.83"
  dependencies:
    "@react-native/js-polyfills": 0.74.83
    "@react-native/metro-babel-transformer": 0.74.83
    metro-config: ^0.80.3
    metro-runtime: ^0.80.3
  checksum: 547f879453fa16b0c07f098c9639b62d20b662b84c46eb0d76f82241ec8d88c54e07fcfaeb4ca20340f5697480866bb904929cd38cd3d6684ef2b5455c82aff7
  languageName: node
  linkType: hard

"@react-native/normalize-color@npm:^2.0.0":
  version: 2.1.0
  resolution: "@react-native/normalize-color@npm:2.1.0"
  checksum: 8ccbd40b3c7629f1dc97b3e9aadd95fd3507fcf2e37535a6299a70436ab891c34cbdc4240b07380553d6e85dd909e23d5773b5be1da2906b026312e0b0768838
  languageName: node
  linkType: hard

"@react-native/normalize-colors@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/normalize-colors@npm:0.74.83"
  checksum: 87e5c5a7d24b0119ba468f2ecbdcb309ac24581a402ff24c980d20c96ec016ccdcabb997c0411393292def859834c785fc8115af91d87bc89b87fb114922ad94
  languageName: node
  linkType: hard

"@react-native/normalize-colors@npm:^0.74.1":
  version: 0.74.86
  resolution: "@react-native/normalize-colors@npm:0.74.86"
  checksum: 10081f56139e6b87f212a11e18a5c693eb9cd166c70a82840be8beb617ff815051850507c9910f9953d5ee37beafcb3ee4b5b944ea7aaba499c0b46cbeecf0ad
  languageName: node
  linkType: hard

"@react-native/typescript-config@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/typescript-config@npm:0.74.83"
  checksum: 318ef304611bc7e42415327916bce358df1c7bc9e971e129d263166edf1179c42d64e5b91784fa28db13d5218ae2c87dc3e2214582673ba2b0f52e8f8615695b
  languageName: node
  linkType: hard

"@react-native/virtualized-lists@npm:0.74.83":
  version: 0.74.83
  resolution: "@react-native/virtualized-lists@npm:0.74.83"
  dependencies:
    invariant: ^2.2.4
    nullthrows: ^1.1.1
  peerDependencies:
    "@types/react": ^18.2.6
    react: "*"
    react-native: "*"
  peerDependenciesMeta:
    "@types/react":
      optional: true
  checksum: ef88e57e3f73c98fe22e2b0d1c8ee1c719eba65b3e58667df561b61ea23ca6c350aa47ae3762a93ed801ead67a6c4c2b4c7c32a2cd0ac3109ff1d236f2b541d0
  languageName: node
  linkType: hard

"@react-navigation/bottom-tabs@npm:^6.5.20":
  version: 6.6.1
  resolution: "@react-navigation/bottom-tabs@npm:6.6.1"
  dependencies:
    "@react-navigation/elements": ^1.3.31
    color: ^4.2.3
    warn-once: ^0.1.0
  peerDependencies:
    "@react-navigation/native": ^6.0.0
    react: "*"
    react-native: "*"
    react-native-safe-area-context: ">= 3.0.0"
    react-native-screens: ">= 3.0.0"
  checksum: 07d6da4b91d7f372b67bcb9f1ff97fba96f1fe226bd95d43d7877362ce71d99c6eebe9ca41d84ea8828f055713386262e089a8207a6c849f33bae49b4df4b196
  languageName: node
  linkType: hard

"@react-navigation/core@npm:^6.4.17":
  version: 6.4.17
  resolution: "@react-navigation/core@npm:6.4.17"
  dependencies:
    "@react-navigation/routers": ^6.1.9
    escape-string-regexp: ^4.0.0
    nanoid: ^3.1.23
    query-string: ^7.1.3
    react-is: ^16.13.0
    use-latest-callback: ^0.2.1
  peerDependencies:
    react: "*"
  checksum: 5e7315bb6ebff8e796eaccb0442d00696466750cc387e93f5edb5293d4ad3f409c1525ef76192894488e2d0979b762b236a1b0fbbb7500b2f065bf4745d509c0
  languageName: node
  linkType: hard

"@react-navigation/drawer@npm:^6.6.15":
  version: 6.7.2
  resolution: "@react-navigation/drawer@npm:6.7.2"
  dependencies:
    "@react-navigation/elements": ^1.3.31
    color: ^4.2.3
    warn-once: ^0.1.0
  peerDependencies:
    "@react-navigation/native": ^6.0.0
    react: "*"
    react-native: "*"
    react-native-gesture-handler: ">= 1.0.0"
    react-native-reanimated: ">= 1.0.0"
    react-native-safe-area-context: ">= 3.0.0"
    react-native-screens: ">= 3.0.0"
  checksum: 6c5b0b118dad5a94141804a1685497464cd9a03cfa426888a8b4027a12898ba3b853fba9bd327cfb8733ffbb94546d612fdcf69fe1eb1bc45a46e14d77aaec82
  languageName: node
  linkType: hard

"@react-navigation/elements@npm:^1.3.31":
  version: 1.3.31
  resolution: "@react-navigation/elements@npm:1.3.31"
  peerDependencies:
    "@react-navigation/native": ^6.0.0
    react: "*"
    react-native: "*"
    react-native-safe-area-context: ">= 3.0.0"
  checksum: 1e4a65ccd9fab757d01bf41f605aafd6ca8301ae25ad7d3f1769320793418cca9fe2f25ac9337578ce1e0a1560bbbc3a88f18b899867aacd4d31de7a789e417e
  languageName: node
  linkType: hard

"@react-navigation/native-stack@npm:^6.9.26":
  version: 6.11.0
  resolution: "@react-navigation/native-stack@npm:6.11.0"
  dependencies:
    "@react-navigation/elements": ^1.3.31
    warn-once: ^0.1.0
  peerDependencies:
    "@react-navigation/native": ^6.0.0
    react: "*"
    react-native: "*"
    react-native-safe-area-context: ">= 3.0.0"
    react-native-screens: ">= 3.0.0"
  checksum: d3dd57c216f5dbe53636bdb9aa48fe27831640f868cf5c68731943a49b68cb457d81182e7868f3e3033da0564e9f193f1b06b69085b8bc5b04ccfbe12ea2bbc0
  languageName: node
  linkType: hard

"@react-navigation/native@npm:^6.1.17":
  version: 6.1.18
  resolution: "@react-navigation/native@npm:6.1.18"
  dependencies:
    "@react-navigation/core": ^6.4.17
    escape-string-regexp: ^4.0.0
    fast-deep-equal: ^3.1.3
    nanoid: ^3.1.23
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 82aeea67723f5dc41403e1c260f04942696f6cde95e30629c383521c3837d18d2d5c21bd78f0ade50beb81ac5edca2d7d38980dcd3a79e3acc86f45d0c09a4b8
  languageName: node
  linkType: hard

"@react-navigation/routers@npm:^6.1.9":
  version: 6.1.9
  resolution: "@react-navigation/routers@npm:6.1.9"
  dependencies:
    nanoid: ^3.1.23
  checksum: 3a3392ce095d6a2bd2aad69856f513b35774f943a3dc73d8ffb75127de6773203e3264188d87058bdea4c0c9a7d43ed28d0cbf3a1f1cdc086df3ee255d8e1e27
  languageName: node
  linkType: hard

"@rnx-kit/chromium-edge-launcher@npm:^1.0.0":
  version: 1.0.0
  resolution: "@rnx-kit/chromium-edge-launcher@npm:1.0.0"
  dependencies:
    "@types/node": ^18.0.0
    escape-string-regexp: ^4.0.0
    is-wsl: ^2.2.0
    lighthouse-logger: ^1.0.0
    mkdirp: ^1.0.4
    rimraf: ^3.0.2
  checksum: c72113e32c222af94482a60e7cea8d296360abbc503afa64394af65ca106c7a36d975a68fed63e8cf5668ffebc33fa636665ceaf55c75d4cf949fb40302fc409
  languageName: node
  linkType: hard

"@sideway/address@npm:^4.1.5":
  version: 4.1.5
  resolution: "@sideway/address@npm:4.1.5"
  dependencies:
    "@hapi/hoek": ^9.0.0
  checksum: 3e3ea0f00b4765d86509282290368a4a5fd39a7995fdc6de42116ca19a96120858e56c2c995081def06e1c53e1f8bccc7d013f6326602bec9d56b72ee2772b9d
  languageName: node
  linkType: hard

"@sideway/formula@npm:^3.0.1":
  version: 3.0.1
  resolution: "@sideway/formula@npm:3.0.1"
  checksum: e4beeebc9dbe2ff4ef0def15cec0165e00d1612e3d7cea0bc9ce5175c3263fc2c818b679bd558957f49400ee7be9d4e5ac90487e1625b4932e15c4aa7919c57a
  languageName: node
  linkType: hard

"@sideway/pinpoint@npm:^2.0.0":
  version: 2.0.0
  resolution: "@sideway/pinpoint@npm:2.0.0"
  checksum: 0f4491e5897fcf5bf02c46f5c359c56a314e90ba243f42f0c100437935daa2488f20482f0f77186bd6bf43345095a95d8143ecf8b1f4d876a7bc0806aba9c3d2
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.27.8":
  version: 0.27.8
  resolution: "@sinclair/typebox@npm:0.27.8"
  checksum: 00bd7362a3439021aa1ea51b0e0d0a0e8ca1351a3d54c606b115fdcc49b51b16db6e5f43b4fe7a28c38688523e22a94d49dd31168868b655f0d4d50f032d07a1
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^3.0.0":
  version: 3.0.1
  resolution: "@sinonjs/commons@npm:3.0.1"
  dependencies:
    type-detect: 4.0.8
  checksum: a7c3e7cc612352f4004873747d9d8b2d4d90b13a6d483f685598c945a70e734e255f1ca5dc49702515533c403b32725defff148177453b3f3915bcb60e9d4601
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^10.0.2":
  version: 10.3.0
  resolution: "@sinonjs/fake-timers@npm:10.3.0"
  dependencies:
    "@sinonjs/commons": ^3.0.0
  checksum: 614d30cb4d5201550c940945d44c9e0b6d64a888ff2cd5b357f95ad6721070d6b8839cd10e15b76bf5e14af0bcc1d8f9ec00d49a46318f1f669a4bec1d7f3148
  languageName: node
  linkType: hard

"@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-add-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3fc8e35d16f5abe0af5efe5851f27581225ac405d6a1ca44cda0df064cddfcc29a428c48c2e4bef6cebf627c9ac2f652a096030edb02cf5a120ce28d3c234710
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-attribute@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ff992893c6c4ac802713ba3a97c13be34e62e6d981c813af40daabcd676df68a72a61bd1e692bb1eda3587f1b1d700ea462222ae2153bb0f46886632d4f88d08
  languageName: node
  linkType: hard

"@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-remove-jsx-empty-expression@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0fb691b63a21bac00da3aa2dccec50d0d5a5b347ff408d60803b84410d8af168f2656e4ba1ee1f24dab0ae4e4af77901f2928752bb0434c1f6788133ec599ec8
  languageName: node
  linkType: hard

"@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-replace-jsx-attribute-value@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 1edda65ef4f4dd8f021143c8ec276a08f6baa6f733b8e8ee2e7775597bf6b97afb47fdeefd579d6ae6c959fe2e634f55cd61d99377631212228c8cfb351b8921
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-dynamic-title@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 876cec891488992e6a9aebb8155e2bea4ec461b4718c51de36e988e00e271c6d9d01ef6be17b9effd44b2b3d7db0b41c161a5904a46ae6f38b26b387ad7f3709
  languageName: node
  linkType: hard

"@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-svg-em-dimensions@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: be0e2d391164428327d9ec469a52cea7d93189c6b0e2c290999e048f597d777852f701c64dca44cd45b31ed14a7f859520326e2e4ad7c3a4545d0aa235bc7e9a
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-plugin-transform-react-native-svg@npm:8.1.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 85b434a57572f53bd2b9f0606f253e1fcf57b4a8c554ec3f2d43ed17f50d8cae200cb3aaf1ec9d626e1456e8b135dce530ae047eb0bed6d4bf98a752d6640459
  languageName: node
  linkType: hard

"@svgr/babel-plugin-transform-svg-component@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/babel-plugin-transform-svg-component@npm:8.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 04e2023d75693eeb0890341c40e449881184663056c249be7e5c80168e4aabb0fadd255e8d5d2dbf54b8c2a6e700efba994377135bfa4060dc4a2e860116ef8c
  languageName: node
  linkType: hard

"@svgr/babel-preset@npm:8.1.0":
  version: 8.1.0
  resolution: "@svgr/babel-preset@npm:8.1.0"
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute": 8.0.0
    "@svgr/babel-plugin-remove-jsx-attribute": 8.0.0
    "@svgr/babel-plugin-remove-jsx-empty-expression": 8.0.0
    "@svgr/babel-plugin-replace-jsx-attribute-value": 8.0.0
    "@svgr/babel-plugin-svg-dynamic-title": 8.0.0
    "@svgr/babel-plugin-svg-em-dimensions": 8.0.0
    "@svgr/babel-plugin-transform-react-native-svg": 8.1.0
    "@svgr/babel-plugin-transform-svg-component": 8.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3a67930f080b8891e1e8e2595716b879c944d253112bae763dce59807ba23454d162216c8d66a0a0e3d4f38a649ecd6c387e545d1e1261dd69a68e9a3392ee08
  languageName: node
  linkType: hard

"@svgr/core@npm:^8.1.0":
  version: 8.1.0
  resolution: "@svgr/core@npm:8.1.0"
  dependencies:
    "@babel/core": ^7.21.3
    "@svgr/babel-preset": 8.1.0
    camelcase: ^6.2.0
    cosmiconfig: ^8.1.3
    snake-case: ^3.0.4
  checksum: da4a12865c7dc59829d58df8bd232d6c85b7115fda40da0d2f844a1a51886e2e945560596ecfc0345d37837ac457de86a931e8b8d8550e729e0c688c02250d8a
  languageName: node
  linkType: hard

"@svgr/hast-util-to-babel-ast@npm:8.0.0":
  version: 8.0.0
  resolution: "@svgr/hast-util-to-babel-ast@npm:8.0.0"
  dependencies:
    "@babel/types": ^7.21.3
    entities: ^4.4.0
  checksum: 88401281a38bbc7527e65ff5437970414391a86158ef4b4046c89764c156d2d39ecd7cce77be8a51994c9fb3249170cb1eb8b9128b62faaa81743ef6ed3534ab
  languageName: node
  linkType: hard

"@svgr/plugin-jsx@npm:^8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-jsx@npm:8.1.0"
  dependencies:
    "@babel/core": ^7.21.3
    "@svgr/babel-preset": 8.1.0
    "@svgr/hast-util-to-babel-ast": 8.0.0
    svg-parser: ^2.0.4
  peerDependencies:
    "@svgr/core": "*"
  checksum: 0418a9780753d3544912ee2dad5d2cf8d12e1ba74df8053651b3886aeda54d5f0f7d2dece0af5e0d838332c4f139a57f0dabaa3ca1afa4d1a765efce6a7656f2
  languageName: node
  linkType: hard

"@svgr/plugin-svgo@npm:^8.1.0":
  version: 8.1.0
  resolution: "@svgr/plugin-svgo@npm:8.1.0"
  dependencies:
    cosmiconfig: ^8.1.3
    deepmerge: ^4.3.1
    svgo: ^3.0.2
  peerDependencies:
    "@svgr/core": "*"
  checksum: 59d9d214cebaacca9ca71a561f463d8b7e5a68ca9443e4792a42d903acd52259b1790c0680bc6afecc3f00a255a6cbd7ea278a9f625bac443620ea58a590c2d0
  languageName: node
  linkType: hard

"@trysound/sax@npm:0.2.0":
  version: 0.2.0
  resolution: "@trysound/sax@npm:0.2.0"
  checksum: 11226c39b52b391719a2a92e10183e4260d9651f86edced166da1d95f39a0a1eaa470e44d14ac685ccd6d3df7e2002433782872c0feeb260d61e80f21250e65c
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.1.14":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": ^7.20.7
    "@babel/types": ^7.20.7
    "@types/babel__generator": "*"
    "@types/babel__template": "*"
    "@types/babel__traverse": "*"
  checksum: a3226f7930b635ee7a5e72c8d51a357e799d19cbf9d445710fa39ab13804f79ab1a54b72ea7d8e504659c7dfc50675db974b526142c754398d7413aa4bc30845
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.6.8
  resolution: "@types/babel__generator@npm:7.6.8"
  dependencies:
    "@babel/types": ^7.0.0
  checksum: 5b332ea336a2efffbdeedb92b6781949b73498606ddd4205462f7d96dafd45ff3618770b41de04c4881e333dd84388bfb8afbdf6f2764cbd98be550d85c6bb48
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": ^7.1.0
    "@babel/types": ^7.0.0
  checksum: d7a02d2a9b67e822694d8e6a7ddb8f2b71a1d6962dfd266554d2513eefbb205b33ca71a0d163b1caea3981ccf849211f9964d8bd0727124d18ace45aa6c9ae29
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*, @types/babel__traverse@npm:^7.0.6":
  version: 7.20.6
  resolution: "@types/babel__traverse@npm:7.20.6"
  dependencies:
    "@babel/types": ^7.20.7
  checksum: 2bdc65eb62232c2d5c1086adeb0c31e7980e6fd7e50a3483b4a724a1a1029c84d9cb59749cf8de612f9afa2bc14c85b8f50e64e21f8a4398fa77eb9059a4283c
  languageName: node
  linkType: hard

"@types/graceful-fs@npm:^4.1.3":
  version: 4.1.9
  resolution: "@types/graceful-fs@npm:4.1.9"
  dependencies:
    "@types/node": "*"
  checksum: 79d746a8f053954bba36bd3d94a90c78de995d126289d656fb3271dd9f1229d33f678da04d10bce6be440494a5a73438e2e363e92802d16b8315b051036c5256
  languageName: node
  linkType: hard

"@types/hammerjs@npm:^2.0.36":
  version: 2.0.45
  resolution: "@types/hammerjs@npm:2.0.45"
  checksum: 40a29067c485a2a1f4345718104218fcf769adb1dbe091cdb6f679b3293dfa0798b207eb498ee7fd79ae8664c999117738fa0c01753f7465a639128f38c3ff5b
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0, @types/istanbul-lib-coverage@npm:^2.0.1":
  version: 2.0.6
  resolution: "@types/istanbul-lib-coverage@npm:2.0.6"
  checksum: 3feac423fd3e5449485afac999dcfcb3d44a37c830af898b689fadc65d26526460bedb889db278e0d4d815a670331796494d073a10ee6e3a6526301fe7415778
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.3
  resolution: "@types/istanbul-lib-report@npm:3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage": "*"
  checksum: b91e9b60f865ff08cb35667a427b70f6c2c63e88105eadd29a112582942af47ed99c60610180aa8dcc22382fa405033f141c119c69b95db78c4c709fbadfeeb4
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/istanbul-reports@npm:3.0.4"
  dependencies:
    "@types/istanbul-lib-report": "*"
  checksum: 93eb18835770b3431f68ae9ac1ca91741ab85f7606f310a34b3586b5a34450ec038c3eed7ab19266635499594de52ff73723a54a72a75b9f7d6a956f01edee95
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.12, @types/json-schema@npm:^7.0.9":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 97ed0cb44d4070aecea772b7b2e2ed971e10c81ec87dd4ecc160322ffa55ff330dace1793489540e3e318d90942064bb697cc0f8989391797792d919737b3b98
  languageName: node
  linkType: hard

"@types/node-forge@npm:^1.3.0":
  version: 1.3.11
  resolution: "@types/node-forge@npm:1.3.11"
  dependencies:
    "@types/node": "*"
  checksum: 1e86bd55b92a492eaafd75f6d01f31e7d86a5cdadd0c6bcdc0b1df4103b7f99bb75b832efd5217c7ddda5c781095dc086a868e20b9de00f5a427ddad4c296cd5
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 22.0.2
  resolution: "@types/node@npm:22.0.2"
  dependencies:
    undici-types: ~6.11.1
  checksum: a83d7e9c81ddc5e58050b61413e871e68468f127367172fa4c14ba3182f8548be59e045096619a4742d665c38790d9843b71664ec61c0ec4ba59c771a739eded
  languageName: node
  linkType: hard

"@types/node@npm:>=12.12.47, @types/node@npm:>=13.7.0":
  version: 22.1.0
  resolution: "@types/node@npm:22.1.0"
  dependencies:
    undici-types: ~6.13.0
  checksum: 3544c35da06009790a2e07742a7dfa0ac0f0d64ec47d9e6d3edf0ff6dcfc1a7cc2efdc5e524e80f8ed80aa37154513b2c1c724f95146ff89fc5aefb8e33575f2
  languageName: node
  linkType: hard

"@types/node@npm:^18.0.0":
  version: 18.19.42
  resolution: "@types/node@npm:18.19.42"
  dependencies:
    undici-types: ~5.26.4
  checksum: 3f976583d3f4ff6040187f98e838337d59134e53bfe1cf241d8143e87e6f9507a1ad0aa435ea550c21d76c6cabb78f63a410413de476764f45695378cc022377
  languageName: node
  linkType: hard

"@types/prop-types@npm:*":
  version: 15.7.12
  resolution: "@types/prop-types@npm:15.7.12"
  checksum: ac16cc3d0a84431ffa5cfdf89579ad1e2269549f32ce0c769321fdd078f84db4fbe1b461ed5a1a496caf09e637c0e367d600c541435716a55b1d9713f5035dfe
  languageName: node
  linkType: hard

"@types/react-test-renderer@npm:^18.0.0":
  version: 18.3.0
  resolution: "@types/react-test-renderer@npm:18.3.0"
  dependencies:
    "@types/react": "*"
  checksum: c53683990bd194cb68e3987bda79c78eff41517f7a747e92f3e54217c2ce3addd031b8a45bf631982c909cc2caeeb905372f322758e05bb76c03754a3f24426e
  languageName: node
  linkType: hard

"@types/react@npm:*, @types/react@npm:^18.2.6":
  version: 18.3.3
  resolution: "@types/react@npm:18.3.3"
  dependencies:
    "@types/prop-types": "*"
    csstype: ^3.0.2
  checksum: c63d6a78163244e2022b01ef79b0baec4fe4da3475dc4a90bb8accefad35ef0c43560fd0312e5974f92a0f1108aa4d669ac72d73d66396aa060ea03b5d2e3873
  languageName: node
  linkType: hard

"@types/semver@npm:^7.3.12, @types/semver@npm:^7.5.0":
  version: 7.5.8
  resolution: "@types/semver@npm:7.5.8"
  checksum: ea6f5276f5b84c55921785a3a27a3cd37afee0111dfe2bcb3e03c31819c197c782598f17f0b150a69d453c9584cd14c4c4d7b9a55d2c5e6cacd4d66fdb3b3663
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0":
  version: 2.0.3
  resolution: "@types/stack-utils@npm:2.0.3"
  checksum: 72576cc1522090fe497337c2b99d9838e320659ac57fa5560fcbdcbafcf5d0216c6b3a0a8a4ee4fdb3b1f5e3420aa4f6223ab57b82fef3578bec3206425c6cf5
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.3
  resolution: "@types/yargs-parser@npm:21.0.3"
  checksum: ef236c27f9432983e91432d974243e6c4cdae227cb673740320eff32d04d853eed59c92ca6f1142a335cfdc0e17cccafa62e95886a8154ca8891cc2dec4ee6fc
  languageName: node
  linkType: hard

"@types/yargs@npm:^15.0.0":
  version: 15.0.19
  resolution: "@types/yargs@npm:15.0.19"
  dependencies:
    "@types/yargs-parser": "*"
  checksum: 6a509db36304825674f4f00300323dce2b4d850e75819c3db87e9e9f213ac2c4c6ed3247a3e4eed6e8e45b3f191b133a356d3391dd694d9ea27a0507d914ef4c
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.8":
  version: 17.0.32
  resolution: "@types/yargs@npm:17.0.32"
  dependencies:
    "@types/yargs-parser": "*"
  checksum: 4505bdebe8716ff383640c6e928f855b5d337cb3c68c81f7249fc6b983d0aa48de3eee26062b84f37e0d75a5797bc745e0c6e76f42f81771252a758c638f36ba
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/eslint-plugin@npm:7.18.0"
  dependencies:
    "@eslint-community/regexpp": ^4.10.0
    "@typescript-eslint/scope-manager": 7.18.0
    "@typescript-eslint/type-utils": 7.18.0
    "@typescript-eslint/utils": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
    graphemer: ^1.4.0
    ignore: ^5.3.1
    natural-compare: ^1.4.0
    ts-api-utils: ^1.3.0
  peerDependencies:
    "@typescript-eslint/parser": ^7.0.0
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: dfcf150628ca2d4ccdfc20b46b0eae075c2f16ef5e70d9d2f0d746acf4c69a09f962b93befee01a529f14bbeb3e817b5aba287d7dd0edc23396bc5ed1f448c3d
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^5.30.5":
  version: 5.62.0
  resolution: "@typescript-eslint/eslint-plugin@npm:5.62.0"
  dependencies:
    "@eslint-community/regexpp": ^4.4.0
    "@typescript-eslint/scope-manager": 5.62.0
    "@typescript-eslint/type-utils": 5.62.0
    "@typescript-eslint/utils": 5.62.0
    debug: ^4.3.4
    graphemer: ^1.4.0
    ignore: ^5.2.0
    natural-compare-lite: ^1.4.0
    semver: ^7.3.7
    tsutils: ^3.21.0
  peerDependencies:
    "@typescript-eslint/parser": ^5.0.0
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: fc104b389c768f9fa7d45a48c86d5c1ad522c1d0512943e782a56b1e3096b2cbcc1eea3fcc590647bf0658eef61aac35120a9c6daf979bf629ad2956deb516a1
  languageName: node
  linkType: hard

"@typescript-eslint/eslint-plugin@npm:^6.7.4":
  version: 6.21.0
  resolution: "@typescript-eslint/eslint-plugin@npm:6.21.0"
  dependencies:
    "@eslint-community/regexpp": ^4.5.1
    "@typescript-eslint/scope-manager": 6.21.0
    "@typescript-eslint/type-utils": 6.21.0
    "@typescript-eslint/utils": 6.21.0
    "@typescript-eslint/visitor-keys": 6.21.0
    debug: ^4.3.4
    graphemer: ^1.4.0
    ignore: ^5.2.4
    natural-compare: ^1.4.0
    semver: ^7.5.4
    ts-api-utils: ^1.0.1
  peerDependencies:
    "@typescript-eslint/parser": ^6.0.0 || ^6.0.0-alpha
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 5ef2c502255e643e98051e87eb682c2a257e87afd8ec3b9f6274277615e1c2caf3131b352244cfb1987b8b2c415645eeacb9113fa841fc4c9b2ac46e8aed6efd
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/parser@npm:7.18.0"
  dependencies:
    "@typescript-eslint/scope-manager": 7.18.0
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/typescript-estree": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 132b56ac3b2d90b588d61d005a70f6af322860974225b60201cbf45abf7304d67b7d8a6f0ade1c188ac4e339884e78d6dcd450417f1481998f9ddd155bab0801
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^5.30.5":
  version: 5.62.0
  resolution: "@typescript-eslint/parser@npm:5.62.0"
  dependencies:
    "@typescript-eslint/scope-manager": 5.62.0
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/typescript-estree": 5.62.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: d168f4c7f21a7a63f47002e2d319bcbb6173597af5c60c1cf2de046b46c76b4930a093619e69faf2d30214c29ab27b54dcf1efc7046a6a6bd6f37f59a990e752
  languageName: node
  linkType: hard

"@typescript-eslint/parser@npm:^6.7.4":
  version: 6.21.0
  resolution: "@typescript-eslint/parser@npm:6.21.0"
  dependencies:
    "@typescript-eslint/scope-manager": 6.21.0
    "@typescript-eslint/types": 6.21.0
    "@typescript-eslint/typescript-estree": 6.21.0
    "@typescript-eslint/visitor-keys": 6.21.0
    debug: ^4.3.4
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 162fe3a867eeeffda7328bce32dae45b52283c68c8cb23258fb9f44971f761991af61f71b8c9fe1aa389e93dfe6386f8509c1273d870736c507d76dd40647b68
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/scope-manager@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/visitor-keys": 5.62.0
  checksum: 6062d6b797fe1ce4d275bb0d17204c827494af59b5eaf09d8a78cdd39dadddb31074dded4297aaf5d0f839016d601032857698b0e4516c86a41207de606e9573
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/scope-manager@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": 6.21.0
    "@typescript-eslint/visitor-keys": 6.21.0
  checksum: 71028b757da9694528c4c3294a96cc80bc7d396e383a405eab3bc224cda7341b88e0fc292120b35d3f31f47beac69f7083196c70616434072fbcd3d3e62d3376
  languageName: node
  linkType: hard

"@typescript-eslint/scope-manager@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/scope-manager@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
  checksum: b982c6ac13d8c86bb3b949c6b4e465f3f60557c2ccf4cc229799827d462df56b9e4d3eaed7711d79b875422fc3d71ec1ebcb5195db72134d07c619e3c5506b57
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/type-utils@npm:5.62.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 5.62.0
    "@typescript-eslint/utils": 5.62.0
    debug: ^4.3.4
    tsutils: ^3.21.0
  peerDependencies:
    eslint: "*"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: fc41eece5f315dfda14320be0da78d3a971d650ea41300be7196934b9715f3fe1120a80207551eb71d39568275dbbcf359bde540d1ca1439d8be15e9885d2739
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/type-utils@npm:6.21.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 6.21.0
    "@typescript-eslint/utils": 6.21.0
    debug: ^4.3.4
    ts-api-utils: ^1.0.1
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 77025473f4d80acf1fafcce99c5c283e557686a61861febeba9c9913331f8a41e930bf5cd8b7a54db502a57b6eb8ea6d155cbd4f41349ed00e3d7aeb1f477ddc
  languageName: node
  linkType: hard

"@typescript-eslint/type-utils@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/type-utils@npm:7.18.0"
  dependencies:
    "@typescript-eslint/typescript-estree": 7.18.0
    "@typescript-eslint/utils": 7.18.0
    debug: ^4.3.4
    ts-api-utils: ^1.3.0
  peerDependencies:
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 68fd5df5146c1a08cde20d59b4b919acab06a1b06194fe4f7ba1b928674880249890785fbbc97394142f2ef5cff5a7fba9b8a940449e7d5605306505348e38bc
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/types@npm:5.62.0"
  checksum: 48c87117383d1864766486f24de34086155532b070f6264e09d0e6139449270f8a9559cfef3c56d16e3bcfb52d83d42105d61b36743626399c7c2b5e0ac3b670
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/types@npm:6.21.0"
  checksum: 9501b47d7403417af95fc1fb72b2038c5ac46feac0e1598a46bcb43e56a606c387e9dcd8a2a0abe174c91b509f2d2a8078b093786219eb9a01ab2fbf9ee7b684
  languageName: node
  linkType: hard

"@typescript-eslint/types@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/types@npm:7.18.0"
  checksum: 7df2750cd146a0acd2d843208d69f153b458e024bbe12aab9e441ad2c56f47de3ddfeb329c4d1ea0079e2577fea4b8c1c1ce15315a8d49044586b04fedfe7a4d
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/typescript-estree@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/visitor-keys": 5.62.0
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    semver: ^7.3.7
    tsutils: ^3.21.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 3624520abb5807ed8f57b1197e61c7b1ed770c56dfcaca66372d584ff50175225798bccb701f7ef129d62c5989070e1ee3a0aa2d84e56d9524dcf011a2bb1a52
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/typescript-estree@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": 6.21.0
    "@typescript-eslint/visitor-keys": 6.21.0
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    minimatch: 9.0.3
    semver: ^7.5.4
    ts-api-utils: ^1.0.1
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: dec02dc107c4a541e14fb0c96148f3764b92117c3b635db3a577b5a56fc48df7a556fa853fb82b07c0663b4bf2c484c9f245c28ba3e17e5cb0918ea4cab2ea21
  languageName: node
  linkType: hard

"@typescript-eslint/typescript-estree@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/typescript-estree@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/visitor-keys": 7.18.0
    debug: ^4.3.4
    globby: ^11.1.0
    is-glob: ^4.0.3
    minimatch: ^9.0.4
    semver: ^7.6.0
    ts-api-utils: ^1.3.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: c82d22ec9654973944f779eb4eb94c52f4a6eafaccce2f0231ff7757313f3a0d0256c3252f6dfe6d43f57171d09656478acb49a629a9d0c193fb959bc3f36116
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:5.62.0, @typescript-eslint/utils@npm:^5.10.0":
  version: 5.62.0
  resolution: "@typescript-eslint/utils@npm:5.62.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@types/json-schema": ^7.0.9
    "@types/semver": ^7.3.12
    "@typescript-eslint/scope-manager": 5.62.0
    "@typescript-eslint/types": 5.62.0
    "@typescript-eslint/typescript-estree": 5.62.0
    eslint-scope: ^5.1.1
    semver: ^7.3.7
  peerDependencies:
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: ee9398c8c5db6d1da09463ca7bf36ed134361e20131ea354b2da16a5fdb6df9ba70c62a388d19f6eebb421af1786dbbd79ba95ddd6ab287324fc171c3e28d931
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/utils@npm:6.21.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    "@types/json-schema": ^7.0.12
    "@types/semver": ^7.5.0
    "@typescript-eslint/scope-manager": 6.21.0
    "@typescript-eslint/types": 6.21.0
    "@typescript-eslint/typescript-estree": 6.21.0
    semver: ^7.5.4
  peerDependencies:
    eslint: ^7.0.0 || ^8.0.0
  checksum: b129b3a4aebec8468259f4589985cb59ea808afbfdb9c54f02fad11e17d185e2bf72bb332f7c36ec3c09b31f18fc41368678b076323e6e019d06f74ee93f7bf2
  languageName: node
  linkType: hard

"@typescript-eslint/utils@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/utils@npm:7.18.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.4.0
    "@typescript-eslint/scope-manager": 7.18.0
    "@typescript-eslint/types": 7.18.0
    "@typescript-eslint/typescript-estree": 7.18.0
  peerDependencies:
    eslint: ^8.56.0
  checksum: 751dbc816dab8454b7dc6b26a56671dbec08e3f4ef94c2661ce1c0fc48fa2d05a64e03efe24cba2c22d03ba943cd3c5c7a5e1b7b03bbb446728aec1c640bd767
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:5.62.0":
  version: 5.62.0
  resolution: "@typescript-eslint/visitor-keys@npm:5.62.0"
  dependencies:
    "@typescript-eslint/types": 5.62.0
    eslint-visitor-keys: ^3.3.0
  checksum: 976b05d103fe8335bef5c93ad3f76d781e3ce50329c0243ee0f00c0fcfb186c81df50e64bfdd34970148113f8ade90887f53e3c4938183afba830b4ba8e30a35
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:6.21.0":
  version: 6.21.0
  resolution: "@typescript-eslint/visitor-keys@npm:6.21.0"
  dependencies:
    "@typescript-eslint/types": 6.21.0
    eslint-visitor-keys: ^3.4.1
  checksum: 67c7e6003d5af042d8703d11538fca9d76899f0119130b373402819ae43f0bc90d18656aa7add25a24427ccf1a0efd0804157ba83b0d4e145f06107d7d1b7433
  languageName: node
  linkType: hard

"@typescript-eslint/visitor-keys@npm:7.18.0":
  version: 7.18.0
  resolution: "@typescript-eslint/visitor-keys@npm:7.18.0"
  dependencies:
    "@typescript-eslint/types": 7.18.0
    eslint-visitor-keys: ^3.4.3
  checksum: 6e806a7cdb424c5498ea187a5a11d0fef7e4602a631be413e7d521e5aec1ab46ba00c76cfb18020adaa0a8c9802354a163bfa0deb74baa7d555526c7517bb158
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:^0.8.8":
  version: 0.8.10
  resolution: "@xmldom/xmldom@npm:0.8.10"
  checksum: 4c136aec31fb3b49aaa53b6fcbfe524d02a1dc0d8e17ee35bd3bf35e9ce1344560481cd1efd086ad1a4821541482528672306d5e37cdbd187f33d7fadd3e2cf0
  languageName: node
  linkType: hard

"@xmldom/xmldom@npm:~0.7.7":
  version: 0.7.13
  resolution: "@xmldom/xmldom@npm:0.7.13"
  checksum: b4054078530e5fa8ede9677425deff0fce6d965f4c477ca73f8490d8a089e60b8498a15560425a1335f5ff99ecb851ed2c734b0a9a879299a5694302f212f37a
  languageName: node
  linkType: hard

"abbrev@npm:^2.0.0":
  version: 2.0.0
  resolution: "abbrev@npm:2.0.0"
  checksum: 0e994ad2aa6575f94670d8a2149afe94465de9cedaaaac364e7fb43a40c3691c980ff74899f682f4ca58fa96b4cbd7421a015d3a6defe43a442117d7821a2f36
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: ^5.0.0
  checksum: 170bdba9b47b7e65906a28c8ce4f38a7a369d78e2271706f020849c1bfe0ee2067d4261df8bbb66eb84f79208fd5b710df759d64191db58cfba7ce8ef9c54b75
  languageName: node
  linkType: hard

"accepts@npm:^1.3.7, accepts@npm:~1.3.5, accepts@npm:~1.3.7":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: ~2.1.34
    negotiator: 0.6.3
  checksum: 50c43d32e7b50285ebe84b613ee4a3aa426715a7d131b65b786e2ead0fd76b6b60091b9916d3478a75f11f162628a2139991b6c03ab3f1d9ab7c86075dc8eab4
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.3.2":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn@npm:^8.12.0, acorn@npm:^8.8.2":
  version: 8.12.1
  resolution: "acorn@npm:8.12.1"
  bin:
    acorn: bin/acorn
  checksum: 677880034aee5bdf7434cc2d25b641d7bedb0b5ef47868a78dadabedccf58e1c5457526d9d8249cd253f2df087e081c3fe7d903b448d8e19e5131a3065b83c07
  languageName: node
  linkType: hard

"agent-base@npm:^7.0.2, agent-base@npm:^7.1.0, agent-base@npm:^7.1.1":
  version: 7.1.1
  resolution: "agent-base@npm:7.1.1"
  dependencies:
    debug: ^4.3.4
  checksum: 51c158769c5c051482f9ca2e6e1ec085ac72b5a418a9b31b4e82fe6c0a6699adb94c1c42d246699a587b3335215037091c79e0de512c516f73b6ea844202f037
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv@npm:^6.12.4":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"anser@npm:^1.4.9":
  version: 1.4.10
  resolution: "anser@npm:1.4.10"
  checksum: 3823c64f8930d3d97f36e56cdf646fa6351f1227e25eee70c3a17697447cae4238fc3a309bb3bc2003cf930687fa72aed71426dbcf3c0a15565e120a7fee5507
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: ^0.21.3
  checksum: 93111c42189c0a6bed9cdb4d7f2829548e943827ee8479c74d6e0b22ee127b2a21d3f8b5ca57723b8ef78ce011fbfc2784350eb2bde3ccfccf2f575fa8489815
  languageName: node
  linkType: hard

"ansi-fragments@npm:^0.2.1":
  version: 0.2.1
  resolution: "ansi-fragments@npm:0.2.1"
  dependencies:
    colorette: ^1.0.7
    slice-ansi: ^2.0.0
    strip-ansi: ^5.0.0
  checksum: 22c3eb8a0aec6bcc15f4e78d77a264ee0c92160b09c94260d1161d051eb8c77c7ecfeb3c8ec44ca180bad554fef3489528c509a644a7589635fc36bcaf08234f
  languageName: node
  linkType: hard

"ansi-regex@npm:^4.1.0":
  version: 4.1.1
  resolution: "ansi-regex@npm:4.1.1"
  checksum: b1a6ee44cb6ecdabaa770b2ed500542714d4395d71c7e5c25baa631f680fb2ad322eb9ba697548d498a6fd366949fc8b5bfcf48d49a32803611f648005b01888
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.0, ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.0.1
  resolution: "ansi-regex@npm:6.0.1"
  checksum: 1ff8b7667cded1de4fa2c9ae283e979fc87036864317da86a2e546725f96406746411d0d85e87a2d12fa5abd715d90006de7fa4fa0477c92321ad3b4c7d4e169
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.2.0, ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: d7f4e97ce0623aea6bc0d90dcd28881ee04cba06c570b97fd3391bd7a268eedfd9d5e2dd4fdcbdd82b8105df5faf6f24aaedc08eaf3da898e702db5948f63469
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: ef940f2f0ced1a6347398da88a91da7930c33ecac3c77b72c5905f8b8fe402c52e6fde304ff5347f616e27a742da3f1dc76de98f6866c69251ad0b07a66776d9
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 3e044fd6d1d26545f235a9fe4d7a534e2029d8e59fa7fd9f2a6eb21230f6b5380ea1eaf55136e60cbf8e613544b3b766e7a6fa2102e2a3a117505466e3025dc2
  languageName: node
  linkType: hard

"appdirsjs@npm:^1.2.4":
  version: 1.2.7
  resolution: "appdirsjs@npm:1.2.7"
  checksum: 3411b4e31edf8687ad69638ef81b92b4889ad31e527b673a364990c28c99b6b8c3ea81b2b2b636d5b08e166a18706c4464fd8436b298f85384d499ba6b8dc4b7
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 83644b56493e89a254bae05702abf3a1101b4fa4d0ca31df1c9985275a5a5bd47b3c27b7fa0b71098d41114d8ca000e6ed90cad764b306f8a503665e4d517ced
  languageName: node
  linkType: hard

"array-buffer-byte-length@npm:^1.0.1":
  version: 1.0.1
  resolution: "array-buffer-byte-length@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.5
    is-array-buffer: ^3.0.4
  checksum: 53524e08f40867f6a9f35318fafe467c32e45e9c682ba67b11943e167344d2febc0f6977a17e699b05699e805c3e8f073d876f8bbf1b559ed494ad2cd0fae09e
  languageName: node
  linkType: hard

"array-includes@npm:^3.1.6, array-includes@npm:^3.1.8":
  version: 3.1.8
  resolution: "array-includes@npm:3.1.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.4
    is-string: ^1.0.7
  checksum: eb39ba5530f64e4d8acab39297c11c1c5be2a4ea188ab2b34aba5fb7224d918f77717a9d57a3e2900caaa8440e59431bdaf5c974d5212ef65d97f132e38e2d91
  languageName: node
  linkType: hard

"array-union@npm:^2.1.0":
  version: 2.1.0
  resolution: "array-union@npm:2.1.0"
  checksum: 5bee12395cba82da674931df6d0fea23c4aa4660cb3b338ced9f828782a65caa232573e6bf3968f23e0c5eb301764a382cef2f128b170a9dc59de0e36c39f98d
  languageName: node
  linkType: hard

"array.prototype.findlast@npm:^1.2.5":
  version: 1.2.5
  resolution: "array.prototype.findlast@npm:1.2.5"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-shim-unscopables: ^1.0.2
  checksum: 83ce4ad95bae07f136d316f5a7c3a5b911ac3296c3476abe60225bc4a17938bf37541972fcc37dd5adbc99cbb9c928c70bbbfc1c1ce549d41a415144030bb446
  languageName: node
  linkType: hard

"array.prototype.flat@npm:^1.3.1":
  version: 1.3.2
  resolution: "array.prototype.flat@npm:1.3.2"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    es-shim-unscopables: ^1.0.0
  checksum: 5d6b4bf102065fb3f43764bfff6feb3295d372ce89591e6005df3d0ce388527a9f03c909af6f2a973969a4d178ab232ffc9236654149173e0e187ec3a1a6b87b
  languageName: node
  linkType: hard

"array.prototype.flatmap@npm:^1.3.2":
  version: 1.3.2
  resolution: "array.prototype.flatmap@npm:1.3.2"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    es-shim-unscopables: ^1.0.0
  checksum: ce09fe21dc0bcd4f30271f8144083aa8c13d4639074d6c8dc82054b847c7fc9a0c97f857491f4da19d4003e507172a78f4bcd12903098adac8b9cd374f734be3
  languageName: node
  linkType: hard

"array.prototype.tosorted@npm:^1.1.4":
  version: 1.1.4
  resolution: "array.prototype.tosorted@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.3
    es-errors: ^1.3.0
    es-shim-unscopables: ^1.0.2
  checksum: e4142d6f556bcbb4f393c02e7dbaea9af8f620c040450c2be137c9cbbd1a17f216b9c688c5f2c08fbb038ab83f55993fa6efdd9a05881d84693c7bcb5422127a
  languageName: node
  linkType: hard

"arraybuffer.prototype.slice@npm:^1.0.3":
  version: 1.0.3
  resolution: "arraybuffer.prototype.slice@npm:1.0.3"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    call-bind: ^1.0.5
    define-properties: ^1.2.1
    es-abstract: ^1.22.3
    es-errors: ^1.2.1
    get-intrinsic: ^1.2.3
    is-array-buffer: ^3.0.4
    is-shared-array-buffer: ^1.0.2
  checksum: 352259cba534dcdd969c92ab002efd2ba5025b2e3b9bead3973150edbdf0696c629d7f4b3f061c5931511e8207bdc2306da614703c820b45dabce39e3daf7e3e
  languageName: node
  linkType: hard

"asap@npm:~2.0.3, asap@npm:~2.0.6":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: b296c92c4b969e973260e47523207cd5769abd27c245a68c26dc7a0fe8053c55bb04360237cb51cab1df52be939da77150ace99ad331fb7fb13b3423ed73ff3d
  languageName: node
  linkType: hard

"ast-types@npm:0.15.2":
  version: 0.15.2
  resolution: "ast-types@npm:0.15.2"
  dependencies:
    tslib: ^2.0.1
  checksum: 24f0d86bf9e4c8dae16fa24b13c1776f2c2677040bcfbd4eb4f27911db49020be4876885e45e6cfcc548ed4dfea3a0742d77e3346b84fae47379cb0b89e9daa0
  languageName: node
  linkType: hard

"astral-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "astral-regex@npm:1.0.0"
  checksum: 93417fc0879531cd95ace2560a54df865c9461a3ac0714c60cbbaa5f1f85d2bee85489e78d82f70b911b71ac25c5f05fc5a36017f44c9bb33c701bee229ff848
  languageName: node
  linkType: hard

"async-limiter@npm:~1.0.0":
  version: 1.0.1
  resolution: "async-limiter@npm:1.0.1"
  checksum: 2b849695b465d93ad44c116220dee29a5aeb63adac16c1088983c339b0de57d76e82533e8e364a93a9f997f28bbfc6a92948cefc120652bd07f3b59f8d75cf2b
  languageName: node
  linkType: hard

"available-typed-arrays@npm:^1.0.7":
  version: 1.0.7
  resolution: "available-typed-arrays@npm:1.0.7"
  dependencies:
    possible-typed-array-names: ^1.0.0
  checksum: 1aa3ffbfe6578276996de660848b6e95669d9a95ad149e3dd0c0cda77db6ee1dbd9d1dd723b65b6d277b882dd0c4b91a654ae9d3cf9e1254b7e93e4908d78fd3
  languageName: node
  linkType: hard

"b4a@npm:^1.6.4":
  version: 1.6.6
  resolution: "b4a@npm:1.6.6"
  checksum: c46a27e3ac9c84426ae728f0fc46a6ae7703a7bc03e771fa0bef4827fd7cf3bb976d1a3d5afff54606248372ab8fdf595bd0114406690edf37f14d120630cf7f
  languageName: node
  linkType: hard

"babel-core@npm:^7.0.0-bridge.0":
  version: 7.0.0-bridge.0
  resolution: "babel-core@npm:7.0.0-bridge.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2a1cb879019dffb08d17bec36e13c3a6d74c94773f41c1fd8b14de13f149cc34b705b0a1e07b42fcf35917b49d78db6ff0c5c3b00b202a5235013d517b5c6bbb
  languageName: node
  linkType: hard

"babel-jest@npm:^29.6.3, babel-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "babel-jest@npm:29.7.0"
  dependencies:
    "@jest/transform": ^29.7.0
    "@types/babel__core": ^7.1.14
    babel-plugin-istanbul: ^6.1.1
    babel-preset-jest: ^29.6.3
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    slash: ^3.0.0
  peerDependencies:
    "@babel/core": ^7.8.0
  checksum: ee6f8e0495afee07cac5e4ee167be705c711a8cc8a737e05a587a131fdae2b3c8f9aa55dfd4d9c03009ac2d27f2de63d8ba96d3e8460da4d00e8af19ef9a83f7
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^6.1.1":
  version: 6.1.1
  resolution: "babel-plugin-istanbul@npm:6.1.1"
  dependencies:
    "@babel/helper-plugin-utils": ^7.0.0
    "@istanbuljs/load-nyc-config": ^1.0.0
    "@istanbuljs/schema": ^0.1.2
    istanbul-lib-instrument: ^5.0.4
    test-exclude: ^6.0.0
  checksum: cb4fd95738219f232f0aece1116628cccff16db891713c4ccb501cddbbf9272951a5df81f2f2658dfdf4b3e7b236a9d5cbcf04d5d8c07dd5077297339598061a
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-plugin-jest-hoist@npm:29.6.3"
  dependencies:
    "@babel/template": ^7.3.3
    "@babel/types": ^7.3.3
    "@types/babel__core": ^7.1.14
    "@types/babel__traverse": ^7.0.6
  checksum: 51250f22815a7318f17214a9d44650ba89551e6d4f47a2dc259128428324b52f5a73979d010cefd921fd5a720d8c1d55ad74ff601cd94c7bd44d5f6292fde2d1
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.4.10":
  version: 0.4.11
  resolution: "babel-plugin-polyfill-corejs2@npm:0.4.11"
  dependencies:
    "@babel/compat-data": ^7.22.6
    "@babel/helper-define-polyfill-provider": ^0.6.2
    semver: ^6.3.1
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: f098353ce7c7dde1a1d2710858e01b471e85689110c9e37813e009072347eb8c55d5f84d20d3bf1cab31755f20078ba90f8855fdc4686a9daa826a95ff280bd7
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.10.1, babel-plugin-polyfill-corejs3@npm:^0.10.4":
  version: 0.10.4
  resolution: "babel-plugin-polyfill-corejs3@npm:0.10.4"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.6.1
    core-js-compat: ^3.36.1
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: b96a54495f7cc8b3797251c8c15f5ed015edddc3110fc122f6b32c94bec33af1e8bc56fa99091808f500bde0cccaaa266889cdc5935d9e6e9cf09898214f02dd
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.6.1":
  version: 0.6.2
  resolution: "babel-plugin-polyfill-regenerator@npm:0.6.2"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.6.2
  peerDependencies:
    "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0
  checksum: 150233571072b6b3dfe946242da39cba8587b7f908d1c006f7545fc88b0e3c3018d445739beb61e7a75835f0c2751dbe884a94ff9b245ec42369d9267e0e1b3f
  languageName: node
  linkType: hard

"babel-plugin-transform-flow-enums@npm:^0.0.2":
  version: 0.0.2
  resolution: "babel-plugin-transform-flow-enums@npm:0.0.2"
  dependencies:
    "@babel/plugin-syntax-flow": ^7.12.1
  checksum: fd52aef54448e01948a9d1cca0c8f87d064970c8682458962b7a222c372704bc2ce26ae8109e0ab2566e7ea5106856460f04c1a5ed794ab3bcd2f42cae1d9845
  languageName: node
  linkType: hard

"babel-preset-current-node-syntax@npm:^1.0.0":
  version: 1.0.1
  resolution: "babel-preset-current-node-syntax@npm:1.0.1"
  dependencies:
    "@babel/plugin-syntax-async-generators": ^7.8.4
    "@babel/plugin-syntax-bigint": ^7.8.3
    "@babel/plugin-syntax-class-properties": ^7.8.3
    "@babel/plugin-syntax-import-meta": ^7.8.3
    "@babel/plugin-syntax-json-strings": ^7.8.3
    "@babel/plugin-syntax-logical-assignment-operators": ^7.8.3
    "@babel/plugin-syntax-nullish-coalescing-operator": ^7.8.3
    "@babel/plugin-syntax-numeric-separator": ^7.8.3
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
    "@babel/plugin-syntax-optional-chaining": ^7.8.3
    "@babel/plugin-syntax-top-level-await": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: d118c2742498c5492c095bc8541f4076b253e705b5f1ad9a2e7d302d81a84866f0070346662355c8e25fc02caa28dc2da8d69bcd67794a0d60c4d6fab6913cc8
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-preset-jest@npm:29.6.3"
  dependencies:
    babel-plugin-jest-hoist: ^29.6.3
    babel-preset-current-node-syntax: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: aa4ff2a8a728d9d698ed521e3461a109a1e66202b13d3494e41eea30729a5e7cc03b3a2d56c594423a135429c37bf63a9fa8b0b9ce275298be3095a88c69f6fb
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"bare-events@npm:^2.0.0, bare-events@npm:^2.2.0":
  version: 2.4.2
  resolution: "bare-events@npm:2.4.2"
  checksum: 6cd2b10dd32a3410787e120c091b6082fbc2df0c45ed723a7ae51d0e2f55d2a4037e1daff21dae90b671d36582f9f8d50df337875c281d10adb60df81b8cd861
  languageName: node
  linkType: hard

"bare-fs@npm:^2.1.1":
  version: 2.3.1
  resolution: "bare-fs@npm:2.3.1"
  dependencies:
    bare-events: ^2.0.0
    bare-path: ^2.0.0
    bare-stream: ^2.0.0
  checksum: cc5ee2eece085e39f553e56bef156c1e68185fa96668a86d9ffb6e421d6f6aa28f98a96fa0266dc3398afd5efab180c933bd34a74a34eec9c8c90a0261102a7f
  languageName: node
  linkType: hard

"bare-os@npm:^2.1.0":
  version: 2.4.0
  resolution: "bare-os@npm:2.4.0"
  checksum: 1089d1f5ebc71674392ca8407a0823b21909f09cb99b46f1568c0f36effcb6a0b22a3ce7c333ea43e28dd28d76b05cf6aeb94273e45ae831de56cb80f266a53d
  languageName: node
  linkType: hard

"bare-path@npm:^2.0.0, bare-path@npm:^2.1.0":
  version: 2.1.3
  resolution: "bare-path@npm:2.1.3"
  dependencies:
    bare-os: ^2.1.0
  checksum: 20301aeb05b735852a396515464908e51e896922c3bb353ef2a09ff54e81ced94e6ad857bb0a36d2ce659c42bd43dd5c3d5643edd8faaf910ee9950c4e137b88
  languageName: node
  linkType: hard

"bare-stream@npm:^2.0.0":
  version: 2.1.3
  resolution: "bare-stream@npm:2.1.3"
  dependencies:
    streamx: ^2.18.0
  checksum: d0c0a58de9d0d0bf0a66c71593f42b74fe3a41d13b63a65f9662a8fe11eda3b0166d9bedcb36e6dbbbfe67a70c8d2929db9c2f054b47e749bdc8a135c35fcb43
  languageName: node
  linkType: hard

"base-64@npm:^0.1.0":
  version: 0.1.0
  resolution: "base-64@npm:0.1.0"
  checksum: 5a42938f82372ab5392cbacc85a5a78115cbbd9dbef9f7540fa47d78763a3a8bd7d598475f0d92341f66285afd377509851a9bb5c67bbecb89686e9255d5b3eb
  languageName: node
  linkType: hard

"base64-js@npm:1.5.1, base64-js@npm:^1.2.3, base64-js@npm:^1.3.1, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"big-integer@npm:1.6.x":
  version: 1.6.52
  resolution: "big-integer@npm:1.6.52"
  checksum: 6e86885787a20fed96521958ae9086960e4e4b5e74d04f3ef7513d4d0ad631a9f3bde2730fc8aaa4b00419fc865f6ec573e5320234531ef37505da7da192c40b
  languageName: node
  linkType: hard

"bl@npm:^4.0.3, bl@npm:^4.1.0":
  version: 4.1.0
  resolution: "bl@npm:4.1.0"
  dependencies:
    buffer: ^5.5.0
    inherits: ^2.0.4
    readable-stream: ^3.4.0
  checksum: 9e8521fa7e83aa9427c6f8ccdcba6e8167ef30cc9a22df26effcc5ab682ef91d2cbc23a239f945d099289e4bbcfae7a192e9c28c84c6202e710a0dfec3722662
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"bplist-creator@npm:0.1.1":
  version: 0.1.1
  resolution: "bplist-creator@npm:0.1.1"
  dependencies:
    stream-buffers: 2.2.x
  checksum: b0d40d1d1623f1afdbb575cfc8075d742d2c4f0eb458574be809e3857752d1042a39553b3943d2d7f505dde92bcd43e1d7bdac61c9cd44475d696deb79f897ce
  languageName: node
  linkType: hard

"bplist-parser@npm:0.3.2":
  version: 0.3.2
  resolution: "bplist-parser@npm:0.3.2"
  dependencies:
    big-integer: 1.6.x
  checksum: fad0f6eb155a9b636b4096a1725ce972a0386490d7d38df7be11a3a5645372446b7c44aacbc6626d24d2c17d8b837765361520ebf2960aeffcaf56765811620e
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: ^1.0.0
  checksum: a61e7cd2e8a8505e9f0036b3b6108ba5e926b4b55089eeb5550cd04a471fe216c96d4fe7e4c7f995c728c554ae20ddfc4244cad10aef255e72b62930afd233d1
  languageName: node
  linkType: hard

"braces@npm:^3.0.3":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: ^7.1.1
  checksum: b95aa0b3bd909f6cd1720ffcf031aeaf46154dd88b4da01f9a1d3f7ea866a79eba76a6d01cbc3c422b2ee5cdc39a4f02491058d5df0d7bf6e6a162a832df1f69
  languageName: node
  linkType: hard

"browserslist@npm:^4.23.0, browserslist@npm:^4.23.1":
  version: 4.23.3
  resolution: "browserslist@npm:4.23.3"
  dependencies:
    caniuse-lite: ^1.0.30001646
    electron-to-chromium: ^1.5.4
    node-releases: ^2.0.18
    update-browserslist-db: ^1.1.0
  bin:
    browserslist: cli.js
  checksum: 7906064f9970aeb941310b2fcb8b4ace4a1b50aa657c986677c6f1553a8cabcc94ee9c5922f715baffbedaa0e6cf0831b6fed7b059dde6873a4bfadcbe069c7e
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: ^0.4.0
  checksum: 9ba4dc58ce86300c862bffc3ae91f00b2a03b01ee07f3564beeeaf82aa243b8b03ba53f123b0b842c190d4399b94697970c8e7cf7b1ea44b61aa28c3526a4449
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer@npm:^5.5.0":
  version: 5.7.1
  resolution: "buffer@npm:5.7.1"
  dependencies:
    base64-js: ^1.3.1
    ieee754: ^1.1.13
  checksum: e2cf8429e1c4c7b8cbd30834ac09bd61da46ce35f5c22a78e6c2f04497d6d25541b16881e30a019c6fd3154150650ccee27a308eff3e26229d788bbdeb08ab84
  languageName: node
  linkType: hard

"bytes@npm:3.0.0":
  version: 3.0.0
  resolution: "bytes@npm:3.0.0"
  checksum: a2b386dd8188849a5325f58eef69c3b73c51801c08ffc6963eddc9be244089ba32d19347caf6d145c86f315ae1b1fc7061a32b0c1aa6379e6a719090287ed101
  languageName: node
  linkType: hard

"cacache@npm:^18.0.0":
  version: 18.0.4
  resolution: "cacache@npm:18.0.4"
  dependencies:
    "@npmcli/fs": ^3.1.0
    fs-minipass: ^3.0.0
    glob: ^10.2.2
    lru-cache: ^10.0.1
    minipass: ^7.0.3
    minipass-collect: ^2.0.1
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    p-map: ^4.0.0
    ssri: ^10.0.0
    tar: ^6.1.11
    unique-filename: ^3.0.0
  checksum: b7422c113b4ec750f33beeca0f426a0024c28e3172f332218f48f963e5b970647fa1ac05679fe5bb448832c51efea9fda4456b9a95c3a1af1105fe6c1833cde2
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.2, call-bind@npm:^1.0.5, call-bind@npm:^1.0.6, call-bind@npm:^1.0.7":
  version: 1.0.7
  resolution: "call-bind@npm:1.0.7"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    set-function-length: ^1.2.1
  checksum: 295c0c62b90dd6522e6db3b0ab1ce26bdf9e7404215bda13cfee25b626b5ff1a7761324d58d38b1ef1607fc65aca2d06e44d2e18d0dfc6c14b465b00d8660029
  languageName: node
  linkType: hard

"caller-callsite@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-callsite@npm:2.0.0"
  dependencies:
    callsites: ^2.0.0
  checksum: b685e9d126d9247b320cfdfeb3bc8da0c4be28d8fb98c471a96bc51aab3130099898a2fe3bf0308f0fe048d64c37d6d09f563958b9afce1a1e5e63d879c128a2
  languageName: node
  linkType: hard

"caller-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-path@npm:2.0.0"
  dependencies:
    caller-callsite: ^2.0.0
  checksum: 3e12ccd0c71ec10a057aac69e3ec175b721ca858c640df021ef0d25999e22f7c1d864934b596b7d47038e9b56b7ec315add042abbd15caac882998b50102fb12
  languageName: node
  linkType: hard

"callsites@npm:^2.0.0":
  version: 2.0.0
  resolution: "callsites@npm:2.0.0"
  checksum: be2f67b247df913732b7dec1ec0bbfcdbaea263e5a95968b19ec7965affae9496b970e3024317e6d4baa8e28dc6ba0cec03f46fdddc2fdcc51396600e53c2623
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0, camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 8c96818a9076434998511251dcb2761a94817ea17dbdc37f47ac080bd088fc62c7369429a19e2178b993497132c8cbcf5cc1f44ba963e76782ba469c0474938d
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001646":
  version: 1.0.30001646
  resolution: "caniuse-lite@npm:1.0.30001646"
  checksum: 53d45b990d21036aaab7547e164174a0ac9a117acdd14a6c33822c4983e2671b1df48686d5383002d0ef158b208b0047a7dc404312a6229bf8ee629de3351b44
  languageName: node
  linkType: hard

"chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.1.0, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: b563e4b6039b15213114626621e7a3d12f31008bdce20f9c741d69987f62aeaace7ec30f6018890ad77b2e9b4d95324c9f5acfca58a9441e3b1dcdd1e2525d17
  languageName: node
  linkType: hard

"chownr@npm:^1.1.1":
  version: 1.1.4
  resolution: "chownr@npm:1.1.4"
  checksum: 115648f8eb38bac5e41c3857f3e663f9c39ed6480d1349977c4d96c95a47266fcacc5a5aabf3cb6c481e22d72f41992827db47301851766c4fd77ac21a4f081d
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"chrome-launcher@npm:^0.15.2":
  version: 0.15.2
  resolution: "chrome-launcher@npm:0.15.2"
  dependencies:
    "@types/node": "*"
    escape-string-regexp: ^4.0.0
    is-wsl: ^2.2.0
    lighthouse-logger: ^1.0.0
  bin:
    print-chrome-path: bin/print-chrome-path.js
  checksum: e1f8131b9f7bd931248ea85f413c6cdb93a0d41440ff5bf0987f36afb081d2b2c7b60ba6062ee7ae2dd9b052143f6b275b38c9eb115d11b49c3ea8829bad7db0
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 3b374666a85ea3ca43fa49aa3a048d21c9b475c96eb13c133505d2324e7ae5efd6a454f41efe46a152269e9b6a00c9edbe63ec7fa1921957165aae16625acd67
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 6b19dc9b2966d1f8c2041a838217299718f15d6c4b63ae36e4674edd2bee48f780e94761286a56aa59eb305a85fbea4ddffb7630ec063e7ec7e7e5ad42549a87
  languageName: node
  linkType: hard

"cjs-module-lexer@npm:^1.0.0":
  version: 1.3.1
  resolution: "cjs-module-lexer@npm:1.3.1"
  checksum: 75f20ac264a397ea5c63f9c2343a51ab878043666468f275e94862f7180ec1d764a400ec0c09085dcf0db3193c74a8b571519abd2bf4be0d2be510d1377c8d4b
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "cli-cursor@npm:3.1.0"
  dependencies:
    restore-cursor: ^3.1.0
  checksum: 2692784c6cd2fd85cfdbd11f53aea73a463a6d64a77c3e098b2b4697a20443f430c220629e1ca3b195ea5ac4a97a74c2ee411f3807abf6df2b66211fec0c0a29
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.5.0":
  version: 2.9.2
  resolution: "cli-spinners@npm:2.9.2"
  checksum: 1bd588289b28432e4676cb5d40505cfe3e53f2e4e10fbe05c8a710a154d6fe0ce7836844b00d6858f740f2ffe67cdc36e0fce9c7b6a8430e80e6388d5aa4956c
  languageName: node
  linkType: hard

"cliui@npm:^6.0.0":
  version: 6.0.0
  resolution: "cliui@npm:6.0.0"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.0
    wrap-ansi: ^6.2.0
  checksum: 4fcfd26d292c9f00238117f39fc797608292ae36bac2168cfee4c85923817d0607fe21b3329a8621e01aedf512c99b7eaa60e363a671ffd378df6649fb48ae42
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: ^4.2.0
    strip-ansi: ^6.0.1
    wrap-ansi: ^7.0.0
  checksum: 79648b3b0045f2e285b76fb2e24e207c6db44323581e421c3acbd0e86454cba1b37aea976ab50195a49e7384b871e6dfb2247ad7dec53c02454ac6497394cb56
  languageName: node
  linkType: hard

"clone-deep@npm:^2.0.1":
  version: 2.0.2
  resolution: "clone-deep@npm:2.0.2"
  dependencies:
    for-own: ^1.0.0
    is-plain-object: ^2.0.4
    kind-of: ^6.0.0
    shallow-clone: ^1.0.0
  checksum: c33ae31e332cdfd477a8115c9d044984eb69bf009fce3e1f0ff002176652f572d8742aa5e6caeaf16cf5d6084e33fe51bfa482fec53f43e767b3518c797955b1
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: ^2.0.4
    kind-of: ^6.0.2
    shallow-clone: ^3.0.0
  checksum: 770f912fe4e6f21873c8e8fbb1e99134db3b93da32df271d00589ea4a29dbe83a9808a322c93f3bcaf8584b8b4fa6fc269fc8032efbaa6728e0c9886c74467d2
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: d06418b7335897209e77bdd430d04f882189582e67bd1f75a04565f3f07f5b3f119a9d670c943b6697d0afb100f03b866b3b8a1f91d4d02d72c4ecf2bb64b5dd
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 5210d9223010eb95b29df06a91116f2cf7c8e0748a9013ed853b53f362ea0e822f1e5bb054fb3cefc645239a4cf966af1f6133a3b43f40d591f3b68ed6cf0510
  languageName: node
  linkType: hard

"collect-v8-coverage@npm:^1.0.0":
  version: 1.0.2
  resolution: "collect-v8-coverage@npm:1.0.2"
  checksum: c10f41c39ab84629d16f9f6137bc8a63d332244383fc368caf2d2052b5e04c20cd1fd70f66fcf4e2422b84c8226598b776d39d5f2d2a51867cc1ed5d1982b4da
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-string@npm:^1.9.0":
  version: 1.9.1
  resolution: "color-string@npm:1.9.1"
  dependencies:
    color-name: ^1.0.0
    simple-swizzle: ^0.2.2
  checksum: c13fe7cff7885f603f49105827d621ce87f4571d78ba28ef4a3f1a104304748f620615e6bf065ecd2145d0d9dad83a3553f52bb25ede7239d18e9f81622f1cc5
  languageName: node
  linkType: hard

"color@npm:^4.2.3":
  version: 4.2.3
  resolution: "color@npm:4.2.3"
  dependencies:
    color-convert: ^2.0.1
    color-string: ^1.9.0
  checksum: 0579629c02c631b426780038da929cca8e8d80a40158b09811a0112a107c62e10e4aad719843b791b1e658ab4e800558f2e87ca4522c8b32349d497ecb6adeb4
  languageName: node
  linkType: hard

"colorette@npm:^1.0.7":
  version: 1.4.0
  resolution: "colorette@npm:1.4.0"
  checksum: 01c3c16058b182a4ab4c126a65a75faa4d38a20fa7c845090b25453acec6c371bb2c5dceb0a2338511f17902b9d1a9af0cadd8509c9403894b79311032c256c3
  languageName: node
  linkType: hard

"command-exists@npm:^1.2.8":
  version: 1.2.9
  resolution: "command-exists@npm:1.2.9"
  checksum: 729ae3d88a2058c93c58840f30341b7f82688a573019535d198b57a4d8cb0135ced0ad7f52b591e5b28a90feb2c675080ce916e56254a0f7c15cb2395277cac3
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:^7.2.0":
  version: 7.2.0
  resolution: "commander@npm:7.2.0"
  checksum: 53501cbeee61d5157546c0bef0fedb6cdfc763a882136284bed9a07225f09a14b82d2a84e7637edfd1a679fb35ed9502fd58ef1d091e6287f60d790147f68ddc
  languageName: node
  linkType: hard

"commander@npm:^9.4.1":
  version: 9.5.0
  resolution: "commander@npm:9.5.0"
  checksum: c7a3e27aa59e913b54a1bafd366b88650bc41d6651f0cbe258d4ff09d43d6a7394232a4dadd0bf518b3e696fdf595db1028a0d82c785b88bd61f8a440cecfade
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 59715f2fc456a73f68826285718503340b9f0dd89bfffc42749906c5cf3d4277ef11ef1cca0350d0e79204f00f1f6d83851ececc9095dc88512a697ac0b9bdcb
  languageName: node
  linkType: hard

"compressible@npm:~2.0.16":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: ">= 1.43.0 < 2"
  checksum: 58321a85b375d39230405654721353f709d0c1442129e9a17081771b816302a012471a9b8f4864c7dbe02eef7f2aaac3c614795197092262e94b409c9be108f0
  languageName: node
  linkType: hard

"compression@npm:^1.7.1":
  version: 1.7.4
  resolution: "compression@npm:1.7.4"
  dependencies:
    accepts: ~1.3.5
    bytes: 3.0.0
    compressible: ~2.0.16
    debug: 2.6.9
    on-headers: ~1.0.2
    safe-buffer: 5.1.2
    vary: ~1.1.2
  checksum: 35c0f2eb1f28418978615dc1bc02075b34b1568f7f56c62d60f4214d4b7cc00d0f6d282b5f8a954f59872396bd770b6b15ffd8aa94c67d4bce9b8887b906999b
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"connect@npm:^3.6.5":
  version: 3.7.0
  resolution: "connect@npm:3.7.0"
  dependencies:
    debug: 2.6.9
    finalhandler: 1.1.2
    parseurl: ~1.3.3
    utils-merge: 1.0.1
  checksum: 96e1c4effcf219b065c7823e57351c94366d2e2a6952fa95e8212bffb35c86f1d5a3f9f6c5796d4cd3a5fdda628368b1c3cc44bf19c66cfd68fe9f9cab9177e2
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 63ae9933be5a2b8d4509daca5124e20c14d023c820258e484e32dc324d34c2754e71297c94a05784064ad27615037ef677e3f0c00469fb55f409d2bb21261035
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.36.1, core-js-compat@npm:^3.37.1":
  version: 3.37.1
  resolution: "core-js-compat@npm:3.37.1"
  dependencies:
    browserslist: ^4.23.0
  checksum: 5e7430329358bced08c30950512d2081aea0a5652b4c5892cbb3c4a6db05b0d3893a191a955162a07fdb5f4fe74e61b6429fdb503f54e062336d76e43c9555d9
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cosmiconfig@npm:^5.0.5, cosmiconfig@npm:^5.1.0":
  version: 5.2.1
  resolution: "cosmiconfig@npm:5.2.1"
  dependencies:
    import-fresh: ^2.0.0
    is-directory: ^0.3.1
    js-yaml: ^3.13.1
    parse-json: ^4.0.0
  checksum: 8b6f1d3c8a5ffdf663a952f17af0761adf210b7a5933d0fe8988f3ca3a1f0e1e5cbbb74d5b419c15933dd2fdcaec31dbc5cc85cb8259a822342b93b529eff89c
  languageName: node
  linkType: hard

"cosmiconfig@npm:^8.1.3":
  version: 8.3.6
  resolution: "cosmiconfig@npm:8.3.6"
  dependencies:
    import-fresh: ^3.3.0
    js-yaml: ^4.1.0
    parse-json: ^5.2.0
    path-type: ^4.0.0
  peerDependencies:
    typescript: ">=4.9.5"
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: dc339ebea427898c9e03bf01b56ba7afbac07fc7d2a2d5a15d6e9c14de98275a9565da949375aee1809591c152c0a3877bb86dbeaf74d5bd5aaa79955ad9e7a0
  languageName: node
  linkType: hard

"create-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "create-jest@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    chalk: ^4.0.0
    exit: ^0.1.2
    graceful-fs: ^4.2.9
    jest-config: ^29.7.0
    jest-util: ^29.7.0
    prompts: ^2.0.1
  bin:
    create-jest: bin/create-jest.js
  checksum: 1427d49458adcd88547ef6fa39041e1fe9033a661293aa8d2c3aa1b4967cb5bf4f0c00436c7a61816558f28ba2ba81a94d5c962e8022ea9a883978fc8e1f2945
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.5":
  version: 3.1.8
  resolution: "cross-fetch@npm:3.1.8"
  dependencies:
    node-fetch: ^2.6.12
  checksum: 78f993fa099eaaa041122ab037fe9503ecbbcb9daef234d1d2e0b9230a983f64d645d088c464e21a247b825a08dc444a6e7064adfa93536d3a9454b4745b3632
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0, cross-spawn@npm:^7.0.2, cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 671cc7c7288c3a8406f3c69a3ae2fc85555c04169e9d611def9a675635472614f1c0ed0ef80955d5b6d4e724f6ced67f0ad1bb006c2ea643488fcfef994d7f52
  languageName: node
  linkType: hard

"css-in-js-utils@npm:^3.1.0":
  version: 3.1.0
  resolution: "css-in-js-utils@npm:3.1.0"
  dependencies:
    hyphenate-style-name: ^1.0.3
  checksum: 066318e918c04a5e5bce46b38fe81052ea6ac051bcc6d3c369a1d59ceb1546cb2b6086901ab5d22be084122ee3732169996a3dfb04d3406eaee205af77aec61b
  languageName: node
  linkType: hard

"css-select@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-select@npm:5.1.0"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^6.1.0
    domhandler: ^5.0.2
    domutils: ^3.0.1
    nth-check: ^2.0.1
  checksum: 2772c049b188d3b8a8159907192e926e11824aea525b8282981f72ba3f349cf9ecd523fdf7734875ee2cb772246c22117fc062da105b6d59afe8dcd5c99c9bda
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.3":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: 2.0.14
    source-map: ^0.6.1
  checksum: 79f9b81803991b6977b7fcb1588799270438274d89066ce08f117f5cdb5e20019b446d766c61506dd772c839df84caa16042d6076f20c97187f5abe3b50e7d1f
  languageName: node
  linkType: hard

"css-tree@npm:^2.3.1":
  version: 2.3.1
  resolution: "css-tree@npm:2.3.1"
  dependencies:
    mdn-data: 2.0.30
    source-map-js: ^1.0.1
  checksum: 493cc24b5c22b05ee5314b8a0d72d8a5869491c1458017ae5ed75aeb6c3596637dbe1b11dac2548974624adec9f7a1f3a6cf40593dc1f9185eb0e8279543fbc0
  languageName: node
  linkType: hard

"css-tree@npm:~2.2.0":
  version: 2.2.1
  resolution: "css-tree@npm:2.2.1"
  dependencies:
    mdn-data: 2.0.28
    source-map-js: ^1.0.1
  checksum: b94aa8cc2f09e6f66c91548411fcf74badcbad3e150345074715012d16333ce573596ff5dfca03c2a87edf1924716db765120f94247e919d72753628ba3aba27
  languageName: node
  linkType: hard

"css-what@npm:^6.1.0":
  version: 6.1.0
  resolution: "css-what@npm:6.1.0"
  checksum: b975e547e1e90b79625918f84e67db5d33d896e6de846c9b584094e529f0c63e2ab85ee33b9daffd05bff3a146a1916bec664e18bb76dd5f66cbff9fc13b2bbe
  languageName: node
  linkType: hard

"csso@npm:^5.0.5":
  version: 5.0.5
  resolution: "csso@npm:5.0.5"
  dependencies:
    css-tree: ~2.2.0
  checksum: 0ad858d36bf5012ed243e9ec69962a867509061986d2ee07cc040a4b26e4d062c00d4c07e5ba8d430706ceb02dd87edd30a52b5937fd45b1b6f2119c4993d59a
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.3
  resolution: "csstype@npm:3.1.3"
  checksum: 8db785cc92d259102725b3c694ec0c823f5619a84741b5c7991b8ad135dfaa66093038a1cc63e03361a6cd28d122be48f2106ae72334e067dd619a51f49eddf7
  languageName: node
  linkType: hard

"data-view-buffer@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-buffer@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.6
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: ce24348f3c6231223b216da92e7e6a57a12b4af81a23f27eff8feabdf06acfb16c00639c8b705ca4d167f761cfc756e27e5f065d0a1f840c10b907fdaf8b988c
  languageName: node
  linkType: hard

"data-view-byte-length@npm:^1.0.1":
  version: 1.0.1
  resolution: "data-view-byte-length@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.7
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: dbb3200edcb7c1ef0d68979834f81d64fd8cab2f7691b3a4c6b97e67f22182f3ec2c8602efd7b76997b55af6ff8bce485829c1feda4fa2165a6b71fb7baa4269
  languageName: node
  linkType: hard

"data-view-byte-offset@npm:^1.0.0":
  version: 1.0.0
  resolution: "data-view-byte-offset@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.6
    es-errors: ^1.3.0
    is-data-view: ^1.0.1
  checksum: 7f0bf8720b7414ca719eedf1846aeec392f2054d7af707c5dc9a753cc77eb8625f067fa901e0b5127e831f9da9056138d894b9c2be79c27a21f6db5824f009c2
  languageName: node
  linkType: hard

"dayjs@npm:^1.8.15":
  version: 1.11.12
  resolution: "dayjs@npm:1.11.12"
  checksum: 40a4f67c2df3af125ae0ddec68d3a6d806d3009a7414bf45479aaf82f1dd82f3e139e6642e72391abccc37488897830c56afcabb4c819014130d283644df8128
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0, debug@npm:^2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.2, debug@npm:^4.3.4":
  version: 4.3.6
  resolution: "debug@npm:4.3.6"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 1630b748dea3c581295e02137a9f5cbe2c1d85fea35c1e6597a65ca2b16a6fce68cec61b299d480787ef310ba927dc8c92d3061faba0ad06c6a724672f66be7f
  languageName: node
  linkType: hard

"debug@npm:4.3.4":
  version: 4.3.4
  resolution: "debug@npm:4.3.4"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 3dbad3f94ea64f34431a9cbf0bafb61853eda57bff2880036153438f50fb5a84f27683ba0d8e5426bf41a8c6ff03879488120cf5b3a761e77953169c0600a708
  languageName: node
  linkType: hard

"decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: ad8c51a7e7e0720c70ec2eeb1163b66da03e7616d7b98c9ef43cce2416395e84c1e9548dd94f5f6ffecfee9f8b94251fc57121a8b021f2ff2469b2bae247b8aa
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.2":
  version: 0.2.2
  resolution: "decode-uri-component@npm:0.2.2"
  checksum: 95476a7d28f267292ce745eac3524a9079058bbb35767b76e3ee87d42e34cd0275d2eb19d9d08c3e167f97556e8a2872747f5e65cbebcac8b0c98d83e285f139
  languageName: node
  linkType: hard

"decompress-response@npm:^6.0.0":
  version: 6.0.0
  resolution: "decompress-response@npm:6.0.0"
  dependencies:
    mimic-response: ^3.1.0
  checksum: d377cf47e02d805e283866c3f50d3d21578b779731e8c5072d6ce8c13cc31493db1c2f6784da9d1d5250822120cefa44f1deab112d5981015f2e17444b763812
  languageName: node
  linkType: hard

"dedent@npm:^1.0.0":
  version: 1.5.3
  resolution: "dedent@npm:1.5.3"
  peerDependencies:
    babel-plugin-macros: ^3.1.0
  peerDependenciesMeta:
    babel-plugin-macros:
      optional: true
  checksum: 045b595557b2a8ea2eb9b0b4623d764e9a87326486fe2b61191b4342ed93dc01245644d8a09f3108a50c0ee7965f1eedd92e4a3a503ed89ea8e810566ea27f9a
  languageName: node
  linkType: hard

"deep-extend@npm:^0.6.0":
  version: 0.6.0
  resolution: "deep-extend@npm:0.6.0"
  checksum: 7be7e5a8d468d6b10e6a67c3de828f55001b6eb515d014f7aeb9066ce36bd5717161eb47d6a0f7bed8a9083935b465bc163ee2581c8b128d29bf61092fdf57a7
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2, deepmerge@npm:^4.3.0, deepmerge@npm:^4.3.1":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 2024c6a980a1b7128084170c4cf56b0fd58a63f2da1660dcfe977415f27b17dbe5888668b59d0b063753f3220719d5e400b7f113609489c90160bb9a5518d052
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.4
  resolution: "defaults@npm:1.0.4"
  dependencies:
    clone: ^1.0.2
  checksum: 3a88b7a587fc076b84e60affad8b85245c01f60f38fc1d259e7ac1d89eb9ce6abb19e27215de46b98568dd5bc48471730b327637e6f20b0f1bc85cf00440c80a
  languageName: node
  linkType: hard

"define-data-property@npm:^1.0.1, define-data-property@npm:^1.1.4":
  version: 1.1.4
  resolution: "define-data-property@npm:1.1.4"
  dependencies:
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    gopd: ^1.0.1
  checksum: 8068ee6cab694d409ac25936eb861eea704b7763f7f342adbdfe337fc27c78d7ae0eff2364b2917b58c508d723c7a074326d068eef2e45c4edcd85cf94d0313b
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.3, define-properties@npm:^1.2.0, define-properties@npm:^1.2.1":
  version: 1.2.1
  resolution: "define-properties@npm:1.2.1"
  dependencies:
    define-data-property: ^1.0.1
    has-property-descriptors: ^1.0.0
    object-keys: ^1.1.1
  checksum: b4ccd00597dd46cb2d4a379398f5b19fca84a16f3374e2249201992f36b30f6835949a9429669ee6b41b6e837205a163eadd745e472069e70dfc10f03e5fcc12
  languageName: node
  linkType: hard

"denodeify@npm:^1.2.1":
  version: 1.2.1
  resolution: "denodeify@npm:1.2.1"
  checksum: a85c8f7fce5626e311edd897c27ad571b29393c4a739dc29baee48328e09edd82364ff697272dd612462c67e48b4766389642b5bdfaea0dc114b7c6a276c0eae
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: abbe19c768c97ee2eed6282d8ce3031126662252c58d711f646921c9623f9052e3e1906443066beec1095832f534e57c523b7333f8e7e0d93051ab6baef5ab3a
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 0acb300b7478a08b92d810ab229d5afe0d2f4399272045ab22affa0d99dbaf12637659411530a6fcd597a9bdac718fc94373a61a95b4651bbc7b83684a565e38
  languageName: node
  linkType: hard

"detect-indent@npm:^6.1.0":
  version: 6.1.0
  resolution: "detect-indent@npm:6.1.0"
  checksum: ab953a73c72dbd4e8fc68e4ed4bfd92c97eb6c43734af3900add963fd3a9316f3bc0578b018b24198d4c31a358571eff5f0656e81a1f3b9ad5c547d58b2d093d
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.0, detect-libc@npm:^2.0.2":
  version: 2.0.3
  resolution: "detect-libc@npm:2.0.3"
  checksum: 2ba6a939ae55f189aea996ac67afceb650413c7a34726ee92c40fb0deb2400d57ef94631a8a3f052055eea7efb0f99a9b5e6ce923415daa3e68221f963cfc27d
  languageName: node
  linkType: hard

"detect-newline@npm:^3.0.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: ae6cd429c41ad01b164c59ea36f264a2c479598e61cba7c99da24175a7ab80ddf066420f2bec9a1c57a6bead411b4655ff15ad7d281c000a89791f48cbe939e7
  languageName: node
  linkType: hard

"diff-sequences@npm:^29.6.3":
  version: 29.6.3
  resolution: "diff-sequences@npm:29.6.3"
  checksum: f4914158e1f2276343d98ff5b31fc004e7304f5470bf0f1adb2ac6955d85a531a6458d33e87667f98f6ae52ebd3891bb47d420bb48a5bd8b7a27ee25b20e33aa
  languageName: node
  linkType: hard

"dir-glob@npm:^3.0.1":
  version: 3.0.1
  resolution: "dir-glob@npm:3.0.1"
  dependencies:
    path-type: ^4.0.0
  checksum: fa05e18324510d7283f55862f3161c6759a3f2f8dbce491a2fc14c8324c498286c54282c1f0e933cb930da8419b30679389499b919122952a4f8592362ef4615
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: ^2.0.2
  checksum: a45e277f7feaed309fe658ace1ff286c6e2002ac515af0aaf37145b8baa96e49899638c7cd47dccf84c3d32abfc113246625b3ac8f552d1046072adee13b0dc8
  languageName: node
  linkType: hard

"dom-serializer@npm:^2.0.0":
  version: 2.0.0
  resolution: "dom-serializer@npm:2.0.0"
  dependencies:
    domelementtype: ^2.3.0
    domhandler: ^5.0.2
    entities: ^4.2.0
  checksum: cd1810544fd8cdfbd51fa2c0c1128ec3a13ba92f14e61b7650b5de421b88205fd2e3f0cc6ace82f13334114addb90ed1c2f23074a51770a8e9c1273acbc7f3e6
  languageName: node
  linkType: hard

"domelementtype@npm:^2.3.0":
  version: 2.3.0
  resolution: "domelementtype@npm:2.3.0"
  checksum: ee837a318ff702622f383409d1f5b25dd1024b692ef64d3096ff702e26339f8e345820f29a68bcdcea8cfee3531776b3382651232fbeae95612d6f0a75efb4f6
  languageName: node
  linkType: hard

"domhandler@npm:^5.0.2, domhandler@npm:^5.0.3":
  version: 5.0.3
  resolution: "domhandler@npm:5.0.3"
  dependencies:
    domelementtype: ^2.3.0
  checksum: 0f58f4a6af63e6f3a4320aa446d28b5790a009018707bce2859dcb1d21144c7876482b5188395a188dfa974238c019e0a1e610d2fc269a12b2c192ea2b0b131c
  languageName: node
  linkType: hard

"domutils@npm:^3.0.1":
  version: 3.1.0
  resolution: "domutils@npm:3.1.0"
  dependencies:
    dom-serializer: ^2.0.0
    domelementtype: ^2.3.0
    domhandler: ^5.0.3
  checksum: e5757456ddd173caa411cfc02c2bb64133c65546d2c4081381a3bafc8a57411a41eed70494551aa58030be9e58574fcc489828bebd673863d39924fb4878f416
  languageName: node
  linkType: hard

"dooboolab-welcome@npm:^1.3.2":
  version: 1.3.2
  resolution: "dooboolab-welcome@npm:1.3.2"
  bin:
    dooboolab-welcome: bin/hello.js
  checksum: 9a4e5c17a16b5cf874bdc1482ee7a2a221a8f269d8bfe6ba1425f678e764d256e265e30ca6169635ce9fa77af53b2e5a0b810a5c4b48b01c13d906613f799c96
  languageName: node
  linkType: hard

"dot-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "dot-case@npm:3.0.4"
  dependencies:
    no-case: ^3.0.4
    tslib: ^2.0.3
  checksum: a65e3519414856df0228b9f645332f974f2bf5433370f544a681122eab59e66038fc3349b4be1cdc47152779dac71a5864f1ccda2f745e767c46e9c6543b1169
  languageName: node
  linkType: hard

"dotenv@npm:^16.4.5":
  version: 16.4.5
  resolution: "dotenv@npm:16.4.5"
  checksum: 301a12c3d44fd49888b74eb9ccf9f07a1f5df43f489e7fcb89647a2edcd84c42d6bc349dc8df099cd18f07c35c7b04685c1a4f3e6a6a9e6b30f8d48c15b7f49c
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 7d00d7cd8e49b9afa762a813faac332dee781932d6f2c848dc348939c4253f1d4564341b7af1d041853bc3f32c2ef141b58e0a4d9862c17a7f08f68df1e0f1ed
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.4":
  version: 1.5.4
  resolution: "electron-to-chromium@npm:1.5.4"
  checksum: 352f13c043cb185b464efe20f9b0a1adea2b1a7dad56e41dac995d0ad060f9981e479d632ebc73a1dce3bd5c36bbceeffe0667161ce296c2488fbb95f89bc793
  languageName: node
  linkType: hard

"emittery@npm:^0.13.1":
  version: 0.13.1
  resolution: "emittery@npm:0.13.1"
  checksum: 2b089ab6306f38feaabf4f6f02792f9ec85fc054fda79f44f6790e61bbf6bc4e1616afb9b232e0c5ec5289a8a452f79bfa6d905a6fd64e94b49981f0934001c6
  languageName: node
  linkType: hard

"emoji-datasource@npm:^6.0.0":
  version: 6.1.1
  resolution: "emoji-datasource@npm:6.1.1"
  checksum: e7e48f6303448ee5fa2e761f24c7df35080319d41049e7c8d8a3d57103d2f0ae5dfcb859a19e3fc34d70a69b024fe9a40924f5376820d623fd865641b0b98517
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 8487182da74aabd810ac6d6f1994111dfc0e331b01271ae01ec1eb0ad7b5ecc2bbbbd2f053c05cb55a1ac30449527d819bbfbf0e3de1023db308cbcb47f86601
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0, end-of-stream@npm:^1.4.1":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: ^1.4.0
  checksum: 530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"entities@npm:^4.2.0, entities@npm:^4.4.0":
  version: 4.5.0
  resolution: "entities@npm:4.5.0"
  checksum: 853f8ebd5b425d350bffa97dd6958143179a5938352ccae092c62d1267c4e392a039be1bae7d51b6e4ffad25f51f9617531fedf5237f15df302ccfb452cbf2d7
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"envinfo@npm:^7.10.0":
  version: 7.13.0
  resolution: "envinfo@npm:7.13.0"
  bin:
    envinfo: dist/cli.js
  checksum: 822fc30f53bd0be67f0e25be96eb6a2562b8062f3058846bbd7ec471bd4b7835fca6436ee72c4029c8ae4a3d8f8cddbe2ee725b22291f015232d20a682bee732
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.0.6":
  version: 2.1.4
  resolution: "error-stack-parser@npm:2.1.4"
  dependencies:
    stackframe: ^1.3.4
  checksum: 3b916d2d14c6682f287c8bfa28e14672f47eafe832701080e420e7cdbaebb2c50293868256a95706ac2330fe078cf5664713158b49bc30d7a5f2ac229ded0e18
  languageName: node
  linkType: hard

"errorhandler@npm:^1.5.1":
  version: 1.5.1
  resolution: "errorhandler@npm:1.5.1"
  dependencies:
    accepts: ~1.3.7
    escape-html: ~1.0.3
  checksum: 73b7abb08fb751107e9bebecc33c40c0641a54be8bda8e4a045f3f5cb7b805041927fef5629ea39b1737799eb52fe2499ca531f11ac51b0294ccc4667d72cb91
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.5, es-abstract@npm:^1.22.1, es-abstract@npm:^1.22.3, es-abstract@npm:^1.23.0, es-abstract@npm:^1.23.1, es-abstract@npm:^1.23.2, es-abstract@npm:^1.23.3":
  version: 1.23.3
  resolution: "es-abstract@npm:1.23.3"
  dependencies:
    array-buffer-byte-length: ^1.0.1
    arraybuffer.prototype.slice: ^1.0.3
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.7
    data-view-buffer: ^1.0.1
    data-view-byte-length: ^1.0.1
    data-view-byte-offset: ^1.0.0
    es-define-property: ^1.0.0
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    es-set-tostringtag: ^2.0.3
    es-to-primitive: ^1.2.1
    function.prototype.name: ^1.1.6
    get-intrinsic: ^1.2.4
    get-symbol-description: ^1.0.2
    globalthis: ^1.0.3
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
    has-proto: ^1.0.3
    has-symbols: ^1.0.3
    hasown: ^2.0.2
    internal-slot: ^1.0.7
    is-array-buffer: ^3.0.4
    is-callable: ^1.2.7
    is-data-view: ^1.0.1
    is-negative-zero: ^2.0.3
    is-regex: ^1.1.4
    is-shared-array-buffer: ^1.0.3
    is-string: ^1.0.7
    is-typed-array: ^1.1.13
    is-weakref: ^1.0.2
    object-inspect: ^1.13.1
    object-keys: ^1.1.1
    object.assign: ^4.1.5
    regexp.prototype.flags: ^1.5.2
    safe-array-concat: ^1.1.2
    safe-regex-test: ^1.0.3
    string.prototype.trim: ^1.2.9
    string.prototype.trimend: ^1.0.8
    string.prototype.trimstart: ^1.0.8
    typed-array-buffer: ^1.0.2
    typed-array-byte-length: ^1.0.1
    typed-array-byte-offset: ^1.0.2
    typed-array-length: ^1.0.6
    unbox-primitive: ^1.0.2
    which-typed-array: ^1.1.15
  checksum: f840cf161224252512f9527306b57117192696571e07920f777cb893454e32999206198b4f075516112af6459daca282826d1735c450528470356d09eff3a9ae
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-define-property@npm:1.0.0"
  dependencies:
    get-intrinsic: ^1.2.4
  checksum: f66ece0a887b6dca71848fa71f70461357c0e4e7249696f81bad0a1f347eed7b31262af4a29f5d726dc026426f085483b6b90301855e647aa8e21936f07293c6
  languageName: node
  linkType: hard

"es-errors@npm:^1.2.1, es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: ec1414527a0ccacd7f15f4a3bc66e215f04f595ba23ca75cdae0927af099b5ec865f9f4d33e9d7e86f512f252876ac77d4281a7871531a50678132429b1271b5
  languageName: node
  linkType: hard

"es-iterator-helpers@npm:^1.0.19":
  version: 1.0.19
  resolution: "es-iterator-helpers@npm:1.0.19"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.3
    es-errors: ^1.3.0
    es-set-tostringtag: ^2.0.3
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    globalthis: ^1.0.3
    has-property-descriptors: ^1.0.2
    has-proto: ^1.0.3
    has-symbols: ^1.0.3
    internal-slot: ^1.0.7
    iterator.prototype: ^1.1.2
    safe-array-concat: ^1.1.2
  checksum: 7ae112b88359fbaf4b9d7d1d1358ae57c5138768c57ba3a8fb930393662653b0512bfd7917c15890d1471577fb012fee8b73b4465e59b331739e6ee94f961683
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.0.0
  resolution: "es-object-atoms@npm:1.0.0"
  dependencies:
    es-errors: ^1.3.0
  checksum: 26f0ff78ab93b63394e8403c353842b2272836968de4eafe97656adfb8a7c84b9099bf0fe96ed58f4a4cddc860f6e34c77f91649a58a5daa4a9c40b902744e3c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.0.3":
  version: 2.0.3
  resolution: "es-set-tostringtag@npm:2.0.3"
  dependencies:
    get-intrinsic: ^1.2.4
    has-tostringtag: ^1.0.2
    hasown: ^2.0.1
  checksum: 7227fa48a41c0ce83e0377b11130d324ac797390688135b8da5c28994c0165be8b252e15cd1de41e1325e5a5412511586960213e88f9ab4a5e7d028895db5129
  languageName: node
  linkType: hard

"es-shim-unscopables@npm:^1.0.0, es-shim-unscopables@npm:^1.0.2":
  version: 1.0.2
  resolution: "es-shim-unscopables@npm:1.0.2"
  dependencies:
    hasown: ^2.0.0
  checksum: 432bd527c62065da09ed1d37a3f8e623c423683285e6188108286f4a1e8e164a5bcbfbc0051557c7d14633cd2a41ce24c7048e6bbb66a985413fd32f1be72626
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-to-primitive@npm:1.2.1"
  dependencies:
    is-callable: ^1.1.4
    is-date-object: ^1.0.1
    is-symbol: ^1.0.2
  checksum: 4ead6671a2c1402619bdd77f3503991232ca15e17e46222b0a41a5d81aebc8740a77822f5b3c965008e631153e9ef0580540007744521e72de8e33599fca2eed
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.1.2":
  version: 3.1.2
  resolution: "escalade@npm:3.1.2"
  checksum: 1ec0977aa2772075493002bdbd549d595ff6e9393b1cb0d7d6fcaf78c750da0c158f180938365486f75cb69fba20294351caddfce1b46552a7b6c3cde52eaa02
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 9f8a2d5743677c16e85c810e3024d54f0c8dea6424fad3c79ef6666e81dd0846f7437f5e729dfcdac8981bc9e5294c39b4580814d114076b8d36318f46ae4395
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^4.0.0":
  version: 4.0.0
  resolution: "escape-string-regexp@npm:4.0.0"
  checksum: 98b48897d93060f2322108bf29db0feba7dd774be96cd069458d1453347b25ce8682ecc39859d4bca2203cc0ab19c237bcc71755eff49a0f8d90beadeeba5cc5
  languageName: node
  linkType: hard

"eslint-config-prettier@npm:^8.5.0":
  version: 8.10.0
  resolution: "eslint-config-prettier@npm:8.10.0"
  peerDependencies:
    eslint: ">=7.0.0"
  bin:
    eslint-config-prettier: bin/cli.js
  checksum: 153266badd477e49b0759816246b2132f1dbdb6c7f313ca60a9af5822fd1071c2bc5684a3720d78b725452bbac04bb130878b2513aea5e72b1b792de5a69fec8
  languageName: node
  linkType: hard

"eslint-plugin-eslint-comments@npm:^3.2.0":
  version: 3.2.0
  resolution: "eslint-plugin-eslint-comments@npm:3.2.0"
  dependencies:
    escape-string-regexp: ^1.0.5
    ignore: ^5.0.5
  peerDependencies:
    eslint: ">=4.19.1"
  checksum: c9fe273dd56699abdf7e416cfad0344eb50aa01564a5a9133e72d982defb89310bc2e9b0b148ce19c5190d7ff641223b0ba9e667a194bc48467c3dd0d471e657
  languageName: node
  linkType: hard

"eslint-plugin-ft-flow@npm:^2.0.1":
  version: 2.0.3
  resolution: "eslint-plugin-ft-flow@npm:2.0.3"
  dependencies:
    lodash: ^4.17.21
    string-natural-compare: ^3.0.1
  peerDependencies:
    "@babel/eslint-parser": ^7.12.0
    eslint: ^8.1.0
  checksum: 6272f7c352154875dc85c7dcd7cf66f6ed926a9a6aba81c675583bcc6695147597d6b9a6db0f643a387d14eccd61dc36daf20eec1c49e91ce1c63c01ffe295f7
  languageName: node
  linkType: hard

"eslint-plugin-jest@npm:^26.5.3":
  version: 26.9.0
  resolution: "eslint-plugin-jest@npm:26.9.0"
  dependencies:
    "@typescript-eslint/utils": ^5.10.0
  peerDependencies:
    "@typescript-eslint/eslint-plugin": ^5.0.0
    eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
  peerDependenciesMeta:
    "@typescript-eslint/eslint-plugin":
      optional: true
    jest:
      optional: true
  checksum: 6d5fd5c95368f1ca2640389aeb7ce703d6202493c3ec6bdedb4eaca37233710508b0c75829e727765a16fd27029a466d34202bc7f2811c752038ccbbce224400
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^4.2.1":
  version: 4.2.1
  resolution: "eslint-plugin-prettier@npm:4.2.1"
  dependencies:
    prettier-linter-helpers: ^1.0.0
  peerDependencies:
    eslint: ">=7.28.0"
    prettier: ">=2.0.0"
  peerDependenciesMeta:
    eslint-config-prettier:
      optional: true
  checksum: b9e839d2334ad8ec7a5589c5cb0f219bded260839a857d7a486997f9870e95106aa59b8756ff3f37202085ebab658de382b0267cae44c3a7f0eb0bcc03a4f6d6
  languageName: node
  linkType: hard

"eslint-plugin-prettier@npm:^5.1.3":
  version: 5.2.1
  resolution: "eslint-plugin-prettier@npm:5.2.1"
  dependencies:
    prettier-linter-helpers: ^1.0.0
    synckit: ^0.9.1
  peerDependencies:
    "@types/eslint": ">=8.0.0"
    eslint: ">=8.0.0"
    eslint-config-prettier: "*"
    prettier: ">=3.0.0"
  peerDependenciesMeta:
    "@types/eslint":
      optional: true
    eslint-config-prettier:
      optional: true
  checksum: 812f4d1596dcd3a55963212dfbd818a4b38f880741aac75f6869aa740dc5d934060674d3b85d10ff9fec424defa61967dbdef26b8a893a92c9b51880264ed0d9
  languageName: node
  linkType: hard

"eslint-plugin-react-hooks@npm:^4.6.0, eslint-plugin-react-hooks@npm:^4.6.2":
  version: 4.6.2
  resolution: "eslint-plugin-react-hooks@npm:4.6.2"
  peerDependencies:
    eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0
  checksum: 395c433610f59577cfcf3f2e42bcb130436c8a0b3777ac64f441d88c5275f4fcfc89094cedab270f2822daf29af1079151a7a6579a8e9ea8cee66540ba0384c4
  languageName: node
  linkType: hard

"eslint-plugin-react-native-globals@npm:^0.1.1":
  version: 0.1.2
  resolution: "eslint-plugin-react-native-globals@npm:0.1.2"
  checksum: ab91e8ecbb51718fb0763f29226b1c2d402251ab2c4730a8bf85f38b805e32d4243da46d07ccdb12cb9dcce9e7514364a1706142cf970f58dcc9a820bcf4b732
  languageName: node
  linkType: hard

"eslint-plugin-react-native@npm:^4.0.0, eslint-plugin-react-native@npm:^4.1.0":
  version: 4.1.0
  resolution: "eslint-plugin-react-native@npm:4.1.0"
  dependencies:
    eslint-plugin-react-native-globals: ^0.1.1
  peerDependencies:
    eslint: ^3.17.0 || ^4 || ^5 || ^6 || ^7 || ^8
  checksum: b6acc5aa91f95cb4600d6ab4c00cf22577083e72c61aabcf010f4388d97e4fc53ba075db54eeee53cba25b297e1a6ec611434f2c2d0bfb3e8dc6419400663fe9
  languageName: node
  linkType: hard

"eslint-plugin-react@npm:^7.30.1, eslint-plugin-react@npm:^7.34.3":
  version: 7.35.0
  resolution: "eslint-plugin-react@npm:7.35.0"
  dependencies:
    array-includes: ^3.1.8
    array.prototype.findlast: ^1.2.5
    array.prototype.flatmap: ^1.3.2
    array.prototype.tosorted: ^1.1.4
    doctrine: ^2.1.0
    es-iterator-helpers: ^1.0.19
    estraverse: ^5.3.0
    hasown: ^2.0.2
    jsx-ast-utils: ^2.4.1 || ^3.0.0
    minimatch: ^3.1.2
    object.entries: ^1.1.8
    object.fromentries: ^2.0.8
    object.values: ^1.2.0
    prop-types: ^15.8.1
    resolve: ^2.0.0-next.5
    semver: ^6.3.1
    string.prototype.matchall: ^4.0.11
    string.prototype.repeat: ^1.0.0
  peerDependencies:
    eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7
  checksum: cd4d3c0567e947964643dda5fc80147e058d75f06bac47c3f086ff0cd6156286c669d98e685e3834997c4043f3922b90e6374b6c3658f22abd025dbd41acc23f
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1, eslint-scope@npm:^5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"eslint-scope@npm:^8.0.2":
  version: 8.0.2
  resolution: "eslint-scope@npm:8.0.2"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^5.2.0
  checksum: bd1e7a0597ec605cf3bc9b35c9e13d7ea6c11fee031b0cada9e8993b0ecf16d81d6f40f1dcd463424af439abf53cd62302ea25707c1599689eb2750d6aa29688
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^2.1.0":
  version: 2.1.0
  resolution: "eslint-visitor-keys@npm:2.1.0"
  checksum: e3081d7dd2611a35f0388bbdc2f5da60b3a3c5b8b6e928daffff7391146b434d691577aa95064c8b7faad0b8a680266bcda0a42439c18c717b80e6718d7e267d
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^3.3.0, eslint-visitor-keys@npm:^3.4.1, eslint-visitor-keys@npm:^3.4.3":
  version: 3.4.3
  resolution: "eslint-visitor-keys@npm:3.4.3"
  checksum: 36e9ef87fca698b6fd7ca5ca35d7b2b6eeaaf106572e2f7fd31c12d3bfdaccdb587bba6d3621067e5aece31c8c3a348b93922ab8f7b2cbc6aaab5e1d89040c60
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^4.0.0":
  version: 4.0.0
  resolution: "eslint-visitor-keys@npm:4.0.0"
  checksum: 5c09f89cf29d87cdbfbac38802a880d3c2e65f8cb61c689888346758f1e24a4c7f6caefeac9474dfa52058a99920623599bdb00516976a30134abeba91275aa2
  languageName: node
  linkType: hard

"eslint@npm:9.x":
  version: 9.8.0
  resolution: "eslint@npm:9.8.0"
  dependencies:
    "@eslint-community/eslint-utils": ^4.2.0
    "@eslint-community/regexpp": ^4.11.0
    "@eslint/config-array": ^0.17.1
    "@eslint/eslintrc": ^3.1.0
    "@eslint/js": 9.8.0
    "@humanwhocodes/module-importer": ^1.0.1
    "@humanwhocodes/retry": ^0.3.0
    "@nodelib/fs.walk": ^1.2.8
    ajv: ^6.12.4
    chalk: ^4.0.0
    cross-spawn: ^7.0.2
    debug: ^4.3.2
    escape-string-regexp: ^4.0.0
    eslint-scope: ^8.0.2
    eslint-visitor-keys: ^4.0.0
    espree: ^10.1.0
    esquery: ^1.5.0
    esutils: ^2.0.2
    fast-deep-equal: ^3.1.3
    file-entry-cache: ^8.0.0
    find-up: ^5.0.0
    glob-parent: ^6.0.2
    ignore: ^5.2.0
    imurmurhash: ^0.1.4
    is-glob: ^4.0.0
    is-path-inside: ^3.0.3
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.4.1
    lodash.merge: ^4.6.2
    minimatch: ^3.1.2
    natural-compare: ^1.4.0
    optionator: ^0.9.3
    strip-ansi: ^6.0.1
    text-table: ^0.2.0
  bin:
    eslint: bin/eslint.js
  checksum: 3fdcf4047ccb2e18bf5d072542d41bd60b5816a68bfbe95dff17cc680c90c29dfb9c595f9691db290190e55219a8088cf09c675095790c80db58f1b4560b8a14
  languageName: node
  linkType: hard

"espree@npm:^10.0.1, espree@npm:^10.1.0":
  version: 10.1.0
  resolution: "espree@npm:10.1.0"
  dependencies:
    acorn: ^8.12.0
    acorn-jsx: ^5.3.2
    eslint-visitor-keys: ^4.0.0
  checksum: a4708ab987f6c03734b8738b1588e9f31b2e305e869ca4677c60d82294eb05f7099b6687eb39eeb0913bb2d49bdf0bd0f31c511599ea7ee171281f871a9c897e
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:~4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esquery@npm:^1.5.0":
  version: 1.6.0
  resolution: "esquery@npm:1.6.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: 08ec4fe446d9ab27186da274d979558557fbdbbd10968fa9758552482720c54152a5640e08b9009e5a30706b66aba510692054d4129d32d0e12e05bbc0b96fb2
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0, estraverse@npm:^5.3.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"event-target-shim@npm:6.0.2":
  version: 6.0.2
  resolution: "event-target-shim@npm:6.0.2"
  checksum: 9be93437e5b84056a7dc70af8b8962f4ef7f6fd41a988efcd39dfa2853e33242a4058e0dac9cc589cb16ed7409010590ac8cbcc2e3f823100cd337e13be953a0
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0, event-target-shim@npm:^5.0.1":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 1ffe3bb22a6d51bdeb6bf6f7cf97d2ff4a74b017ad12284cc9e6a279e727dc30a5de6bb613e5596ff4dc3e517841339ad09a7eec44266eccb1aa201a30448166
  languageName: node
  linkType: hard

"execa@npm:^5.0.0, execa@npm:^5.1.1":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: ^7.0.3
    get-stream: ^6.0.0
    human-signals: ^2.1.0
    is-stream: ^2.0.0
    merge-stream: ^2.0.0
    npm-run-path: ^4.0.1
    onetime: ^5.1.2
    signal-exit: ^3.0.3
    strip-final-newline: ^2.0.0
  checksum: fba9022c8c8c15ed862847e94c252b3d946036d7547af310e344a527e59021fd8b6bb0723883ea87044dc4f0201f949046993124a42ccb0855cae5bf8c786343
  languageName: node
  linkType: hard

"exit@npm:^0.1.2":
  version: 0.1.2
  resolution: "exit@npm:0.1.2"
  checksum: abc407f07a875c3961e4781dfcb743b58d6c93de9ab263f4f8c9d23bb6da5f9b7764fc773f86b43dd88030444d5ab8abcb611cb680fba8ca075362b77114bba3
  languageName: node
  linkType: hard

"expand-template@npm:^2.0.3":
  version: 2.0.3
  resolution: "expand-template@npm:2.0.3"
  checksum: 588c19847216421ed92befb521767b7018dc88f88b0576df98cb242f20961425e96a92cbece525ef28cc5becceae5d544ae0f5b9b5e2aa05acb13716ca5b3099
  languageName: node
  linkType: hard

"expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "expect@npm:29.7.0"
  dependencies:
    "@jest/expect-utils": ^29.7.0
    jest-get-type: ^29.6.3
    jest-matcher-utils: ^29.7.0
    jest-message-util: ^29.7.0
    jest-util: ^29.7.0
  checksum: 9257f10288e149b81254a0fda8ffe8d54a7061cd61d7515779998b012579d2b8c22354b0eb901daf0145f347403da582f75f359f4810c007182ad3fb318b5c0c
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.1
  resolution: "exponential-backoff@npm:3.1.1"
  checksum: 3d21519a4f8207c99f7457287291316306255a328770d320b401114ec8481986e4e467e854cb9914dd965e0a1ca810a23ccb559c642c88f4c7f55c55778a9b48
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-diff@npm:^1.1.2":
  version: 1.3.0
  resolution: "fast-diff@npm:1.3.0"
  checksum: d22d371b994fdc8cce9ff510d7b8dc4da70ac327bcba20df607dd5b9cae9f908f4d1028f5fe467650f058d1e7270235ae0b8230809a262b4df587a3b3aa216c3
  languageName: node
  linkType: hard

"fast-fifo@npm:^1.2.0, fast-fifo@npm:^1.3.2":
  version: 1.3.2
  resolution: "fast-fifo@npm:1.3.2"
  checksum: 6bfcba3e4df5af7be3332703b69a7898a8ed7020837ec4395bb341bd96cc3a6d86c3f6071dd98da289618cf2234c70d84b2a6f09a33dd6f988b1ff60d8e54275
  languageName: node
  linkType: hard

"fast-glob@npm:^3.2.9, fast-glob@npm:^3.3.2":
  version: 3.3.2
  resolution: "fast-glob@npm:3.3.2"
  dependencies:
    "@nodelib/fs.stat": ^2.0.2
    "@nodelib/fs.walk": ^1.2.3
    glob-parent: ^5.1.2
    merge2: ^1.3.0
    micromatch: ^4.0.4
  checksum: 900e4979f4dbc3313840078419245621259f349950411ca2fa445a2f9a1a6d98c3b5e7e0660c5ccd563aa61abe133a21765c6c0dec8e57da1ba71d8000b05ec1
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0, fast-json-stable-stringify@npm:^2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:^2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fast-loops@npm:^1.1.3":
  version: 1.1.4
  resolution: "fast-loops@npm:1.1.4"
  checksum: 8031a20f465ef35ac4ad98258470250636112d34f7e4efcb4ef21f3ced99df95a1ef1f0d6943df729a1e3e12a9df9319f3019df8cc1a0e0ed5a118bd72e505f9
  languageName: node
  linkType: hard

"fast-xml-parser@npm:^4.0.12, fast-xml-parser@npm:^4.2.4":
  version: 4.4.1
  resolution: "fast-xml-parser@npm:4.4.1"
  dependencies:
    strnum: ^1.0.5
  bin:
    fxparser: src/cli/cli.js
  checksum: f440c01cd141b98789ae777503bcb6727393296094cc82924ae9f88a5b971baa4eec7e65306c7e07746534caa661fc83694ff437d9012dc84dee39dfbfaab947
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.17.1
  resolution: "fastq@npm:1.17.1"
  dependencies:
    reusify: ^1.0.4
  checksum: a8c5b26788d5a1763f88bae56a8ddeee579f935a831c5fe7a8268cea5b0a91fbfe705f612209e02d639b881d7b48e461a50da4a10cfaa40da5ca7cc9da098d88
  languageName: node
  linkType: hard

"faye-websocket@npm:0.11.4":
  version: 0.11.4
  resolution: "faye-websocket@npm:0.11.4"
  dependencies:
    websocket-driver: ">=0.5.1"
  checksum: d49a62caf027f871149fc2b3f3c7104dc6d62744277eb6f9f36e2d5714e847d846b9f7f0d0b7169b25a012e24a594cde11a93034b30732e4c683f20b8a5019fa
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: 2.1.1
  checksum: b15a124cef28916fe07b400eb87cbc73ca082c142abf7ca8e8de6af43eca79ca7bd13eb4d4d48240b3bd3136eaac40d16e42d6edf87a8e5d1dd8070626860c78
  languageName: node
  linkType: hard

"fbjs-css-vars@npm:^1.0.0":
  version: 1.0.2
  resolution: "fbjs-css-vars@npm:1.0.2"
  checksum: 72baf6d22c45b75109118b4daecb6c8016d4c83c8c0f23f683f22e9d7c21f32fff6201d288df46eb561e3c7d4bb4489b8ad140b7f56444c453ba407e8bd28511
  languageName: node
  linkType: hard

"fbjs@npm:^3.0.4":
  version: 3.0.5
  resolution: "fbjs@npm:3.0.5"
  dependencies:
    cross-fetch: ^3.1.5
    fbjs-css-vars: ^1.0.0
    loose-envify: ^1.0.0
    object-assign: ^4.1.0
    promise: ^7.1.1
    setimmediate: ^1.0.5
    ua-parser-js: ^1.0.35
  checksum: e609b5b64686bc96495a5c67728ed9b2710b9b3d695c5759c5f5e47c9483d1c323543ac777a86459e3694efc5712c6ce7212e944feb19752867d699568bb0e54
  languageName: node
  linkType: hard

"file-entry-cache@npm:^8.0.0":
  version: 8.0.0
  resolution: "file-entry-cache@npm:8.0.0"
  dependencies:
    flat-cache: ^4.0.0
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: b4abfbca3839a3d55e4ae5ec62e131e2e356bf4859ce8480c64c4876100f4df292a63e5bb1618e1d7460282ca2b305653064f01654474aa35c68000980f17798
  languageName: node
  linkType: hard

"filter-obj@npm:^1.1.0":
  version: 1.1.0
  resolution: "filter-obj@npm:1.1.0"
  checksum: cf2104a7c45ff48e7f505b78a3991c8f7f30f28bd8106ef582721f321f1c6277f7751aacd5d83026cb079d9d5091082f588d14a72e7c5d720ece79118fa61e10
  languageName: node
  linkType: hard

"finalhandler@npm:1.1.2":
  version: 1.1.2
  resolution: "finalhandler@npm:1.1.2"
  dependencies:
    debug: 2.6.9
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    on-finished: ~2.3.0
    parseurl: ~1.3.3
    statuses: ~1.5.0
    unpipe: ~1.0.0
  checksum: 617880460c5138dd7ccfd555cb5dde4d8f170f4b31b8bd51e4b646bb2946c30f7db716428a1f2882d730d2b72afb47d1f67cc487b874cb15426f95753a88965e
  languageName: node
  linkType: hard

"find-cache-dir@npm:^2.0.0":
  version: 2.1.0
  resolution: "find-cache-dir@npm:2.1.0"
  dependencies:
    commondir: ^1.0.1
    make-dir: ^2.0.0
    pkg-dir: ^3.0.0
  checksum: 60ad475a6da9f257df4e81900f78986ab367d4f65d33cf802c5b91e969c28a8762f098693d7a571b6e4dd4c15166c2da32ae2d18b6766a18e2071079448fdce4
  languageName: node
  linkType: hard

"find-up@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-up@npm:3.0.0"
  dependencies:
    locate-path: ^3.0.0
  checksum: 38eba3fe7a66e4bc7f0f5a1366dc25508b7cfc349f852640e3678d26ad9a6d7e2c43eff0a472287de4a9753ef58f066a0ea892a256fa3636ad51b3fe1e17fae9
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"find-up@npm:^5.0.0, find-up@npm:~5.0.0":
  version: 5.0.0
  resolution: "find-up@npm:5.0.0"
  dependencies:
    locate-path: ^6.0.0
    path-exists: ^4.0.0
  checksum: 07955e357348f34660bde7920783204ff5a26ac2cafcaa28bace494027158a97b9f56faaf2d89a6106211a8174db650dd9f503f9c0d526b1202d5554a00b9095
  languageName: node
  linkType: hard

"firebase@npm:10.12.2":
  version: 10.12.2
  resolution: "firebase@npm:10.12.2"
  dependencies:
    "@firebase/analytics": 0.10.4
    "@firebase/analytics-compat": 0.2.10
    "@firebase/app": 0.10.5
    "@firebase/app-check": 0.8.4
    "@firebase/app-check-compat": 0.3.11
    "@firebase/app-compat": 0.2.35
    "@firebase/app-types": 0.9.2
    "@firebase/auth": 1.7.4
    "@firebase/auth-compat": 0.5.9
    "@firebase/database": 1.0.5
    "@firebase/database-compat": 1.0.5
    "@firebase/firestore": 4.6.3
    "@firebase/firestore-compat": 0.3.32
    "@firebase/functions": 0.11.5
    "@firebase/functions-compat": 0.3.11
    "@firebase/installations": 0.6.7
    "@firebase/installations-compat": 0.2.7
    "@firebase/messaging": 0.12.9
    "@firebase/messaging-compat": 0.2.9
    "@firebase/performance": 0.6.7
    "@firebase/performance-compat": 0.2.7
    "@firebase/remote-config": 0.4.7
    "@firebase/remote-config-compat": 0.2.7
    "@firebase/storage": 0.12.5
    "@firebase/storage-compat": 0.3.8
    "@firebase/util": 1.9.6
    "@firebase/vertexai-preview": 0.0.2
  checksum: d5b00dd2a7b8b779bb2d589a4f8e43952857e28903bc24b6713752a942118a32fbcd545a8fc20341c78e9b21bee43c11cb1a9f75ce56e0f81fbac578c27bce2a
  languageName: node
  linkType: hard

"flat-cache@npm:^4.0.0":
  version: 4.0.1
  resolution: "flat-cache@npm:4.0.1"
  dependencies:
    flatted: ^3.2.9
    keyv: ^4.5.4
  checksum: 899fc86bf6df093547d76e7bfaeb900824b869d7d457d02e9b8aae24836f0a99fbad79328cfd6415ee8908f180699bf259dc7614f793447cb14f707caf5996f6
  languageName: node
  linkType: hard

"flatted@npm:^3.2.9":
  version: 3.3.1
  resolution: "flatted@npm:3.3.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"flow-enums-runtime@npm:^0.0.6":
  version: 0.0.6
  resolution: "flow-enums-runtime@npm:0.0.6"
  checksum: c60412ed6d43b26bf5dfa66be8e588c3ccdb20191fd269e02ca7e8e1d350c73a327cc9a7edb626c80c31eb906981945d12a87ca37118985f33406303806dab79
  languageName: node
  linkType: hard

"flow-parser@npm:0.*":
  version: 0.242.1
  resolution: "flow-parser@npm:0.242.1"
  checksum: 8284e79ad13acd1ee874997aaf1f8303e38c0cb8e8ac2034b1f7505afc84fdef1c3127009f1b54a16c43c733ada33117e4d65bf0561c1060e16d1838949b30b7
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: ^1.1.3
  checksum: 6c48ff2bc63362319c65e2edca4a8e1e3483a2fabc72fbe7feaf8c73db94fc7861bd53bc02c8a66a0c1dd709da6b04eec42e0abdd6b40ce47305ae92a25e5d28
  languageName: node
  linkType: hard

"for-in@npm:^0.1.3":
  version: 0.1.8
  resolution: "for-in@npm:0.1.8"
  checksum: f5bdad7811700ee6a0f96b33d72a1db966aea75a1f03c7245d147f8369305e709f53a55ee7ae8eaddcfa85c7c89bca78472be8f1bc605475ce5bb2c70f77f8da
  languageName: node
  linkType: hard

"for-in@npm:^1.0.1":
  version: 1.0.2
  resolution: "for-in@npm:1.0.2"
  checksum: 09f4ae93ce785d253ac963d94c7f3432d89398bf25ac7a24ed034ca393bf74380bdeccc40e0f2d721a895e54211b07c8fad7132e8157827f6f7f059b70b4043d
  languageName: node
  linkType: hard

"for-own@npm:^1.0.0":
  version: 1.0.0
  resolution: "for-own@npm:1.0.0"
  dependencies:
    for-in: ^1.0.1
  checksum: 233238f6e9060f61295a7f7c7e3e9de11aaef57e82a108e7f350dc92ae84fe2189848077ac4b8db47fd8edd45337ed8d9f66bd0b1efa4a6a1b3f38aa21b7ab2e
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.2.1
  resolution: "foreground-child@npm:3.2.1"
  dependencies:
    cross-spawn: ^7.0.0
    signal-exit: ^4.0.1
  checksum: 3e2e844d6003c96d70affe8ae98d7eaaba269a868c14d997620c088340a8775cd5d2d9043e6ceebae1928d8d9a874911c4d664b9a267e8995945df20337aebc0
  languageName: node
  linkType: hard

"framer-motion@npm:^6.5.1":
  version: 6.5.1
  resolution: "framer-motion@npm:6.5.1"
  dependencies:
    "@emotion/is-prop-valid": ^0.8.2
    "@motionone/dom": 10.12.0
    framesync: 6.0.1
    hey-listen: ^1.0.8
    popmotion: 11.0.3
    style-value-types: 5.0.0
    tslib: ^2.1.0
  peerDependencies:
    react: ">=16.8 || ^17.0.0 || ^18.0.0"
    react-dom: ">=16.8 || ^17.0.0 || ^18.0.0"
  dependenciesMeta:
    "@emotion/is-prop-valid":
      optional: true
  checksum: 737959063137b4ccafe01e0ac0c9e5a9531bf3f729f62c34ca7a5d7955e6664f70affd22b044f7db51df41acb21d120a4f71a860e17a80c4db766ad66f2153a1
  languageName: node
  linkType: hard

"framesync@npm:6.0.1":
  version: 6.0.1
  resolution: "framesync@npm:6.0.1"
  dependencies:
    tslib: ^2.1.0
  checksum: a23ebe8f7e20a32c0b99c2f8175b6f07af3ec6316aad52a2316316a6d011d717af8d2175dcc2827031c59fabb30232ed3e19a720a373caba7f070e1eae436325
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 13ea8b08f91e669a64e3ba3a20eb79d7ca5379a81f1ff7f4310d54e2320645503cc0c78daedc93dfb6191287295f6479544a649c64d8e41a1c0fb0c221552346
  languageName: node
  linkType: hard

"fs-constants@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs-constants@npm:1.0.0"
  checksum: 18f5b718371816155849475ac36c7d0b24d39a11d91348cfcb308b4494824413e03572c403c86d3a260e049465518c4f0d5bd00f0371cdfcad6d4f30a85b350d
  languageName: node
  linkType: hard

"fs-extra@npm:^8.1.0":
  version: 8.1.0
  resolution: "fs-extra@npm:8.1.0"
  dependencies:
    graceful-fs: ^4.2.0
    jsonfile: ^4.0.0
    universalify: ^0.1.0
  checksum: bf44f0e6cea59d5ce071bba4c43ca76d216f89e402dc6285c128abc0902e9b8525135aa808adad72c9d5d218e9f4bcc63962815529ff2f684ad532172a284880
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: ^7.0.3
  checksum: 8722a41109130851d979222d3ec88aabaceeaaf8f57b2a8f744ef8bd2d1ce95453b04a61daa0078822bc5cd21e008814f06fe6586f56fef511e71b8d2394d802
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: latest
  checksum: 11e6ea6fea15e42461fc55b4b0e4a0a3c654faa567f1877dbd353f39156f69def97a69936d1746619d656c4b93de2238bf731f6085a03a50cabf287c9d024317
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@^2.3.2#~builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#~builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 2b0ff4ce708d99715ad14a6d1f894e2a83242e4a52ccfcefaee5e40050562e5f6dafc1adbb4ce2d4ab47279a45dc736ab91ea5042d843c3c092820dfe032efb1
  languageName: node
  linkType: hard

"function.prototype.name@npm:^1.1.6":
  version: 1.1.6
  resolution: "function.prototype.name@npm:1.1.6"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.2.0
    es-abstract: ^1.22.1
    functions-have-names: ^1.2.3
  checksum: 7a3f9bd98adab09a07f6e1f03da03d3f7c26abbdeaeee15223f6c04a9fb5674792bdf5e689dac19b97ac71de6aad2027ba3048a9b883aa1b3173eed6ab07f479
  languageName: node
  linkType: hard

"functions-have-names@npm:^1.2.3":
  version: 1.2.3
  resolution: "functions-have-names@npm:1.2.3"
  checksum: c3f1f5ba20f4e962efb71344ce0a40722163e85bee2101ce25f88214e78182d2d2476aa85ef37950c579eb6cf6ee811c17b3101bb84004bb75655f3e33f3fdb5
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.1, get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.1.3, get-intrinsic@npm:^1.2.1, get-intrinsic@npm:^1.2.3, get-intrinsic@npm:^1.2.4":
  version: 1.2.4
  resolution: "get-intrinsic@npm:1.2.4"
  dependencies:
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    has-proto: ^1.0.1
    has-symbols: ^1.0.3
    hasown: ^2.0.0
  checksum: 414e3cdf2c203d1b9d7d33111df746a4512a1aa622770b361dadddf8ed0b5aeb26c560f49ca077e24bfafb0acb55ca908d1f709216ccba33ffc548ec8a79a951
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: bba0811116d11e56d702682ddef7c73ba3481f114590e705fc549f4d868972263896af313c57a25c076e3c0d567e11d919a64ba1b30c879be985fc9d44f96148
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: e04ecece32c92eebf5b8c940f51468cd53554dcbb0ea725b2748be583c9523d00128137966afce410b9b051eb2ef16d657cd2b120ca8edafcf5a65e81af63cad
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.0.2":
  version: 1.0.2
  resolution: "get-symbol-description@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.5
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.4
  checksum: e1cb53bc211f9dbe9691a4f97a46837a553c4e7caadd0488dc24ac694db8a390b93edd412b48dcdd0b4bbb4c595de1709effc75fc87c0839deedc6968f5bd973
  languageName: node
  linkType: hard

"getenv@npm:^1.0.0":
  version: 1.0.0
  resolution: "getenv@npm:1.0.0"
  checksum: 19ae5cad603a1cf1bcb8fa3bed48e00d062eb0572a4404c02334b67f3b3499f238383082b064bb42515e9e25c2b08aef1a3e3d2b6852347721aa8b174825bd56
  languageName: node
  linkType: hard

"github-from-package@npm:0.0.0":
  version: 0.0.0
  resolution: "github-from-package@npm:0.0.0"
  checksum: 14e448192a35c1e42efee94c9d01a10f42fe790375891a24b25261246ce9336ab9df5d274585aedd4568f7922246c2a78b8a8cd2571bfe99c693a9718e7dd0e3
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-parent@npm:^6.0.2":
  version: 6.0.2
  resolution: "glob-parent@npm:6.0.2"
  dependencies:
    is-glob: ^4.0.3
  checksum: c13ee97978bef4f55106b71e66428eb1512e71a7466ba49025fc2aec59a5bfb0954d5abd58fc5ee6c9b076eef4e1f6d3375c2e964b88466ca390da4419a786a8
  languageName: node
  linkType: hard

"glob@npm:7.1.6":
  version: 7.1.6
  resolution: "glob@npm:7.1.6"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 351d549dd90553b87c2d3f90ce11aed9e1093c74130440e7ae0592e11bbcd2ce7f0ebb8ba6bfe63aaf9b62166a7f4c80cb84490ae5d78408bb2572bf7d4ee0a6
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: ^3.1.0
    jackspeak: ^3.1.2
    minimatch: ^9.0.4
    minipass: ^7.1.2
    package-json-from-dist: ^1.0.0
    path-scurry: ^1.11.1
  bin:
    glob: dist/esm/bin.mjs
  checksum: 0bc725de5e4862f9f387fd0f2b274baf16850dcd2714502ccf471ee401803997983e2c05590cb65f9675a3c6f2a58e7a53f9e365704108c6ad3cbf1d60934c4a
  languageName: node
  linkType: hard

"glob@npm:^7.1.1, glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.1.1
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 29452e97b38fa704dabb1d1045350fb2467cf0277e155aa9ff7077e90ad81d1ea9d53d3ee63bd37c05b09a065e90f16aec4a65f5b8de401d1dac40bc5605d133
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"globals@npm:^14.0.0":
  version: 14.0.0
  resolution: "globals@npm:14.0.0"
  checksum: 534b8216736a5425737f59f6e6a5c7f386254560c9f41d24a9227d60ee3ad4a9e82c5b85def0e212e9d92162f83a92544be4c7fd4c902cb913736c10e08237ac
  languageName: node
  linkType: hard

"globals@npm:^15.6.0":
  version: 15.9.0
  resolution: "globals@npm:15.9.0"
  checksum: 32c4470ffcc26db3ddbc579ddf968b74c26462d1a268039980c2fa2e107090fd442a7a7445d953dc4ee874f68846e713066c5a8e63d146fd9349cd1fc5a6f63d
  languageName: node
  linkType: hard

"globalthis@npm:^1.0.3":
  version: 1.0.4
  resolution: "globalthis@npm:1.0.4"
  dependencies:
    define-properties: ^1.2.1
    gopd: ^1.0.1
  checksum: 39ad667ad9f01476474633a1834a70842041f70a55571e8dcef5fb957980a92da5022db5430fca8aecc5d47704ae30618c0bc877a579c70710c904e9ef06108a
  languageName: node
  linkType: hard

"globby@npm:^11.1.0":
  version: 11.1.0
  resolution: "globby@npm:11.1.0"
  dependencies:
    array-union: ^2.1.0
    dir-glob: ^3.0.1
    fast-glob: ^3.2.9
    ignore: ^5.2.0
    merge2: ^1.4.1
    slash: ^3.0.0
  checksum: b4be8885e0cfa018fc783792942d53926c35c50b3aefd3fdcfb9d22c627639dc26bd2327a40a0b74b074100ce95bb7187bfeae2f236856aa3de183af7a02aea6
  languageName: node
  linkType: hard

"gopd@npm:^1.0.1":
  version: 1.0.1
  resolution: "gopd@npm:1.0.1"
  dependencies:
    get-intrinsic: ^1.1.3
  checksum: a5ccfb8806e0917a94e0b3de2af2ea4979c1da920bc381667c260e00e7cafdbe844e2cb9c5bcfef4e5412e8bf73bab837285bc35c7ba73aaaf0134d4583393a6
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.11, graceful-fs@npm:^4.1.3, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: ac85f94da92d8eb6b7f5a8b20ce65e43d66761c55ce85ac96df6865308390da45a8d3f0296dd3a663de65d30ba497bd46c696cc1e248c72b13d6d567138a4fc7
  languageName: node
  linkType: hard

"graphemer@npm:^1.4.0":
  version: 1.4.0
  resolution: "graphemer@npm:1.4.0"
  checksum: bab8f0be9b568857c7bec9fda95a89f87b783546d02951c40c33f84d05bb7da3fd10f863a9beb901463669b6583173a8c8cc6d6b306ea2b9b9d5d3d943c3a673
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.1, has-bigints@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-bigints@npm:1.0.2"
  checksum: 390e31e7be7e5c6fe68b81babb73dfc35d413604d7ee5f56da101417027a4b4ce6a27e46eff97ad040c835b5d228676eae99a9b5c3bc0e23c8e81a49241ff45b
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-property-descriptors@npm:^1.0.0, has-property-descriptors@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-property-descriptors@npm:1.0.2"
  dependencies:
    es-define-property: ^1.0.0
  checksum: fcbb246ea2838058be39887935231c6d5788babed499d0e9d0cc5737494c48aba4fe17ba1449e0d0fbbb1e36175442faa37f9c427ae357d6ccb1d895fbcd3de3
  languageName: node
  linkType: hard

"has-proto@npm:^1.0.1, has-proto@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-proto@npm:1.0.3"
  checksum: fe7c3d50b33f50f3933a04413ed1f69441d21d2d2944f81036276d30635cad9279f6b43bc8f32036c31ebdfcf6e731150f46c1907ad90c669ffe9b066c3ba5c4
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.2, has-symbols@npm:^1.0.3":
  version: 1.0.3
  resolution: "has-symbols@npm:1.0.3"
  checksum: a054c40c631c0d5741a8285010a0777ea0c068f99ed43e5d6eb12972da223f8af553a455132fdb0801bdcfa0e0f443c0c03a68d8555aa529b3144b446c3f2410
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0, has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: ^1.0.3
  checksum: 999d60bb753ad714356b2c6c87b7fb74f32463b8426e159397da4bde5bca7e598ab1073f4d8d4deafac297f2eb311484cd177af242776bf05f0d11565680468d
  languageName: node
  linkType: hard

"hasown@npm:^2.0.0, hasown@npm:^2.0.1, hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: ^1.1.2
  checksum: e8516f776a15149ca6c6ed2ae3110c417a00b62260e222590e54aa367cbcd6ed99122020b37b7fbdf05748df57b265e70095d7bf35a47660587619b15ffb93db
  languageName: node
  linkType: hard

"he@npm:1.2.0":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 3d4d6babccccd79c5c5a3f929a68af33360d6445587d628087f39a965079d84f18ce9c3d3f917ee1e3978916fc833bb8b29377c3b403f919426f91bc6965e7a7
  languageName: node
  linkType: hard

"hermes-estree@npm:0.19.1":
  version: 0.19.1
  resolution: "hermes-estree@npm:0.19.1"
  checksum: d451114bca12ae97627f0113ede0d42271d75aad01b8e575e5261b576bd7e58b8a1670297a4b7e226236db2c0967b5a4bf1056a51bcd9ce074d654fcf365bdae
  languageName: node
  linkType: hard

"hermes-estree@npm:0.20.1":
  version: 0.20.1
  resolution: "hermes-estree@npm:0.20.1"
  checksum: 226378c62e29a79f8e0935cc8bdefd987195c069b835a9ed1cae08109cd228f6e97a2e580d5de057e4437dc988c972b9fe7227a1d9353dc2abbe142dbd5260c6
  languageName: node
  linkType: hard

"hermes-parser@npm:0.19.1":
  version: 0.19.1
  resolution: "hermes-parser@npm:0.19.1"
  dependencies:
    hermes-estree: 0.19.1
  checksum: 840e5ede07f6567283359a98c3e4e94d89c9b68f9d07cce379aed7b97aacae463aec622cfb13e47186770b68512b2981da3be09f316bde5f87359d5ab9bf1a1a
  languageName: node
  linkType: hard

"hermes-parser@npm:0.20.1":
  version: 0.20.1
  resolution: "hermes-parser@npm:0.20.1"
  dependencies:
    hermes-estree: 0.20.1
  checksum: 2a0c17b5f8fbb0a377f42d480f577b5cc64eafe4d5ebc0a9cbce23b79a02042693134bef1b71163f771d67cd10a450138c8d24b9a431c487fa9ed57cba67e85c
  languageName: node
  linkType: hard

"hermes-profile-transformer@npm:^0.0.6":
  version: 0.0.6
  resolution: "hermes-profile-transformer@npm:0.0.6"
  dependencies:
    source-map: ^0.7.3
  checksum: b5f874eaa65b70d88df7a4ce3b20d73312bb0bc73410f1b63d708f02e1c532ae16975da84e23b977eab8592ac95d7e6fc0c4094c78604fd0a092ed886c62aa7a
  languageName: node
  linkType: hard

"hey-listen@npm:^1.0.8":
  version: 1.0.8
  resolution: "hey-listen@npm:1.0.8"
  checksum: 6bad60b367688f5348e25e7ca3276a74b59ac5a09b0455e6ff8ab7d4a9e38cd2116c708a7dcd8a954d27253ce1d8717ec891d175723ea739885b828cf44e4072
  languageName: node
  linkType: hard

"hoist-non-react-statics@npm:^3.3.0":
  version: 3.3.2
  resolution: "hoist-non-react-statics@npm:3.3.2"
  dependencies:
    react-is: ^16.7.0
  checksum: b1538270429b13901ee586aa44f4cc3ecd8831c061d06cb8322e50ea17b3f5ce4d0e2e66394761e6c8e152cd8c34fb3b4b690116c6ce2bd45b18c746516cb9e8
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: d2df2da3ad40ca9ee3a39c5cc6475ef67c8f83c234475f24d8e9ce0dc80a2c82df8e1d6fa78ddd1e9022a586ea1bd247a615e80a5cd9273d90111ddda7d9e974
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 83ac0bc60b17a3a36f9953e7be55e5c8f41acc61b22583060e8dedc9dd5e3607c823a88d0926f9150e571f90946835c7fe150732801010845c72cd8bbff1a236
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: 2.0.0
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: 2.0.1
    toidentifier: 1.0.1
  checksum: 9b0a3782665c52ce9dc658a0d1560bcb0214ba5699e4ea15aefb2a496e2ca83db03ebc42e1cce4ac1f413e4e0d2d736a3fd755772c556a9a06853ba2a0b7d920
  languageName: node
  linkType: hard

"http-parser-js@npm:>=0.5.1":
  version: 0.5.8
  resolution: "http-parser-js@npm:0.5.8"
  checksum: 6bbdf2429858e8cf13c62375b0bfb6dc3955ca0f32e58237488bc86cd2378f31d31785fd3ac4ce93f1c74e0189cf8823c91f5cb061696214fd368d2452dc871d
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: ^7.1.0
    debug: ^4.3.4
  checksum: 670858c8f8f3146db5889e1fa117630910101db601fff7d5a8aa637da0abedf68c899f03d3451cac2f83bcc4c3d2dabf339b3aa00ff8080571cceb02c3ce02f3
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.5
  resolution: "https-proxy-agent@npm:7.0.5"
  dependencies:
    agent-base: ^7.0.2
    debug: 4
  checksum: 2e1a28960f13b041a50702ee74f240add8e75146a5c37fc98f1960f0496710f6918b3a9fe1e5aba41e50f58e6df48d107edd9c405c5f0d73ac260dabf2210857
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: b87fd89fce72391625271454e70f67fe405277415b48bcc0117ca73d31fa23a4241787afdc8d67f5a116cf37258c052f59ea82daffa72364d61351423848e3b8
  languageName: node
  linkType: hard

"husky@npm:^9.1.7":
  version: 9.1.7
  resolution: "husky@npm:9.1.7"
  bin:
    husky: bin.js
  checksum: c2412753f15695db369634ba70f50f5c0b7e5cb13b673d0826c411ec1bd9ddef08c1dad89ea154f57da2521d2605bd64308af748749b27d08c5f563bcd89975f
  languageName: node
  linkType: hard

"hyphenate-style-name@npm:^1.0.3":
  version: 1.1.0
  resolution: "hyphenate-style-name@npm:1.1.0"
  checksum: b9ed74e29181d96bd58a2d0e62fc4a19879db591dba268275829ff0ae595fcdf11faafaeaa63330a45c3004664d7db1f0fc7cdb372af8ee4615ed8260302c207
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"idb@npm:7.1.1":
  version: 7.1.1
  resolution: "idb@npm:7.1.1"
  checksum: 1973c28d53c784b177bdef9f527ec89ec239ec7cf5fcbd987dae75a16c03f5b7dfcc8c6d3285716fd0309dd57739805390bd9f98ce23b1b7d8849a3b52de8d56
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.13":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"ignore@npm:^5.0.5, ignore@npm:^5.2.0, ignore@npm:^5.2.4, ignore@npm:^5.3.1":
  version: 5.3.1
  resolution: "ignore@npm:5.3.1"
  checksum: 71d7bb4c1dbe020f915fd881108cbe85a0db3d636a0ea3ba911393c53946711d13a9b1143c7e70db06d571a5822c0a324a6bcde5c9904e7ca5047f01f1bf8cd3
  languageName: node
  linkType: hard

"image-size@npm:^1.0.2":
  version: 1.1.1
  resolution: "image-size@npm:1.1.1"
  dependencies:
    queue: 6.0.2
  bin:
    image-size: bin/image-size.js
  checksum: 23b3a515dded89e7f967d52b885b430d6a5a903da954fce703130bfb6069d738d80e6588efd29acfaf5b6933424a56535aa7bf06867e4ebd0250c2ee51f19a4a
  languageName: node
  linkType: hard

"import-fresh@npm:^2.0.0":
  version: 2.0.0
  resolution: "import-fresh@npm:2.0.0"
  dependencies:
    caller-path: ^2.0.0
    resolve-from: ^3.0.0
  checksum: 610255f9753cc6775df00be08e9f43691aa39f7703e3636c45afe22346b8b545e600ccfe100c554607546fc8e861fa149a0d1da078c8adedeea30fff326eef79
  languageName: node
  linkType: hard

"import-fresh@npm:^3.2.1, import-fresh@npm:^3.3.0":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.2.0
  resolution: "import-local@npm:3.2.0"
  dependencies:
    pkg-dir: ^4.2.0
    resolve-cwd: ^3.0.0
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: 0b0b0b412b2521739fbb85eeed834a3c34de9bc67e670b3d0b86248fc460d990a7b116ad056c084b87a693ef73d1f17268d6a5be626bb43c998a8b1c8a230004
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"ini@npm:~1.3.0":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"inline-style-prefixer@npm:^6.0.1":
  version: 6.0.4
  resolution: "inline-style-prefixer@npm:6.0.4"
  dependencies:
    css-in-js-utils: ^3.1.0
    fast-loops: ^1.1.3
  checksum: caf7a75d18acbedc7e3b8bfac17563082becd2df6b65accad964a6afdf490329b42315c37fe65ba0177cc10fd32809eb40d62aba23a0118c74d87d4fc58defa2
  languageName: node
  linkType: hard

"internal-slot@npm:^1.0.7":
  version: 1.0.7
  resolution: "internal-slot@npm:1.0.7"
  dependencies:
    es-errors: ^1.3.0
    hasown: ^2.0.0
    side-channel: ^1.0.4
  checksum: cadc5eea5d7d9bc2342e93aae9f31f04c196afebb11bde97448327049f492cd7081e18623ae71388aac9cd237b692ca3a105be9c68ac39c1dec679d7409e33eb
  languageName: node
  linkType: hard

"invariant@npm:^2.2.4":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: ^1.0.0
  checksum: cc3182d793aad82a8d1f0af697b462939cb46066ec48bbf1707c150ad5fad6406137e91a262022c269702e01621f35ef60269f6c0d7fd178487959809acdfb14
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: 1.1.0
    sprintf-js: ^1.1.3
  checksum: aa15f12cfd0ef5e38349744e3654bae649a34c3b10c77a674a167e99925d1549486c5b14730eebce9fea26f6db9d5e42097b00aa4f9f612e68c79121c71652dc
  languageName: node
  linkType: hard

"is-array-buffer@npm:^3.0.4":
  version: 3.0.4
  resolution: "is-array-buffer@npm:3.0.4"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.2.1
  checksum: e4e3e6ef0ff2239e75371d221f74bc3c26a03564a22efb39f6bb02609b598917ddeecef4e8c877df2a25888f247a98198959842a5e73236bc7f22cabdf6351a7
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 977e64f54d91c8f169b59afcd80ff19227e9f5c791fa28fa2e5bce355cbaf6c2c356711b734656e80c9dd4a854dd7efcf7894402f1031dfc5de5d620775b4d5f
  languageName: node
  linkType: hard

"is-async-function@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-async-function@npm:2.0.0"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: e3471d95e6c014bf37cad8a93f2f4b6aac962178e0a5041e8903147166964fdc1c5c1d2ef87e86d77322c370ca18f2ea004fa7420581fa747bcaf7c223069dbd
  languageName: node
  linkType: hard

"is-bigint@npm:^1.0.1":
  version: 1.0.4
  resolution: "is-bigint@npm:1.0.4"
  dependencies:
    has-bigints: ^1.0.1
  checksum: c56edfe09b1154f8668e53ebe8252b6f185ee852a50f9b41e8d921cb2bed425652049fbe438723f6cb48a63ca1aa051e948e7e401e093477c99c84eba244f666
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.1.0":
  version: 1.1.2
  resolution: "is-boolean-object@npm:1.1.2"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: c03b23dbaacadc18940defb12c1c0e3aaece7553ef58b162a0f6bba0c2a7e1551b59f365b91e00d2dbac0522392d576ef322628cb1d036a0fe51eb466db67222
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3, is-callable@npm:^1.1.4, is-callable@npm:^1.2.7":
  version: 1.2.7
  resolution: "is-callable@npm:1.2.7"
  checksum: 61fd57d03b0d984e2ed3720fb1c7a897827ea174bd44402878e059542ea8c4aeedee0ea0985998aa5cc2736b2fa6e271c08587addb5b3959ac52cf665173d1ac
  languageName: node
  linkType: hard

"is-core-module@npm:^2.13.0":
  version: 2.15.0
  resolution: "is-core-module@npm:2.15.0"
  dependencies:
    hasown: ^2.0.2
  checksum: a9f7a52707c9b59d7164094d183bda892514fc3ba3139f245219c7abe7f6e8d3e2cdcf861f52a891a467f785f1dfa5d549f73b0ee715f4ba56e8882d335ea585
  languageName: node
  linkType: hard

"is-data-view@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-data-view@npm:1.0.1"
  dependencies:
    is-typed-array: ^1.1.13
  checksum: 4ba4562ac2b2ec005fefe48269d6bd0152785458cd253c746154ffb8a8ab506a29d0cfb3b74af87513843776a88e4981ae25c89457bf640a33748eab1a7216b5
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.1, is-date-object@npm:^1.0.5":
  version: 1.0.5
  resolution: "is-date-object@npm:1.0.5"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: baa9077cdf15eb7b58c79398604ca57379b2fc4cf9aa7a9b9e295278648f628c9b201400c01c5e0f7afae56507d741185730307cbe7cad3b9f90a77e5ee342fc
  languageName: node
  linkType: hard

"is-directory@npm:^0.3.1":
  version: 0.3.1
  resolution: "is-directory@npm:0.3.1"
  checksum: dce9a9d3981e38f2ded2a80848734824c50ee8680cd09aa477bef617949715cfc987197a2ca0176c58a9fb192a1a0d69b535c397140d241996a609d5906ae524
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-extendable@npm:^0.1.1":
  version: 0.1.1
  resolution: "is-extendable@npm:0.1.1"
  checksum: 3875571d20a7563772ecc7a5f36cb03167e9be31ad259041b4a8f73f33f885441f778cee1f1fe0085eb4bc71679b9d8c923690003a36a6a5fdf8023e6e3f0672
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finalizationregistry@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-finalizationregistry@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 4f243a8e06228cd45bdab8608d2cb7abfc20f6f0189c8ac21ea8d603f1f196eabd531ce0bb8e08cbab047e9845ef2c191a3761c9a17ad5cabf8b35499c4ad35d
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-fullwidth-code-point@npm:2.0.0"
  checksum: eef9c6e15f68085fec19ff6a978a6f1b8f48018fd1265035552078ee945573594933b09bbd6f562553e2a241561439f1ef5339276eba68d272001343084cfab8
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-fn@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-generator-fn@npm:2.1.0"
  checksum: a6ad5492cf9d1746f73b6744e0c43c0020510b59d56ddcb78a91cbc173f09b5e6beff53d75c9c5a29feb618bfef2bf458e025ecf3a57ad2268e2fb2569f56215
  languageName: node
  linkType: hard

"is-generator-function@npm:^1.0.10":
  version: 1.0.10
  resolution: "is-generator-function@npm:1.0.10"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: d54644e7dbaccef15ceb1e5d91d680eb5068c9ee9f9eb0a9e04173eb5542c9b51b5ab52c5537f5703e48d5fddfd376817c1ca07a84a407b7115b769d4bdde72b
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:^4.0.3":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-interactive@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-interactive@npm:1.0.0"
  checksum: 824808776e2d468b2916cdd6c16acacebce060d844c35ca6d82267da692e92c3a16fdba624c50b54a63f38bdc4016055b6f443ce57d7147240de4f8cdabaf6f9
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-map@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-map@npm:2.0.3"
  checksum: e6ce5f6380f32b141b3153e6ba9074892bbbbd655e92e7ba5ff195239777e767a976dcd4e22f864accaf30e53ebf961ab1995424aef91af68788f0591b7396cc
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-negative-zero@npm:2.0.3"
  checksum: c1e6b23d2070c0539d7b36022d5a94407132411d01aba39ec549af824231f3804b1aea90b5e4e58e807a65d23ceb538ed6e355ce76b267bdd86edb757ffcbdcd
  languageName: node
  linkType: hard

"is-number-object@npm:^1.0.4":
  version: 1.0.7
  resolution: "is-number-object@npm:1.0.7"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: d1e8d01bb0a7134c74649c4e62da0c6118a0bfc6771ea3c560914d52a627873e6920dd0fd0ebc0e12ad2ff4687eac4c308f7e80320b973b2c8a2c8f97a7524f7
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-path-inside@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-path-inside@npm:3.0.3"
  checksum: abd50f06186a052b349c15e55b182326f1936c89a78bf6c8f2b707412517c097ce04bc49a0ca221787bc44e1049f51f09a2ffb63d22899051988d3a618ba13e9
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: ^3.0.1
  checksum: 2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-regex@npm:^1.1.4":
  version: 1.1.4
  resolution: "is-regex@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 362399b33535bc8f386d96c45c9feb04cf7f8b41c182f54174c1a45c9abbbe5e31290bbad09a458583ff6bf3b2048672cdb1881b13289569a7c548370856a652
  languageName: node
  linkType: hard

"is-set@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-set@npm:2.0.3"
  checksum: 36e3f8c44bdbe9496c9689762cc4110f6a6a12b767c5d74c0398176aa2678d4467e3bf07595556f2dba897751bde1422480212b97d973c7b08a343100b0c0dfe
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.2, is-shared-array-buffer@npm:^1.0.3":
  version: 1.0.3
  resolution: "is-shared-array-buffer@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.7
  checksum: a4fff602c309e64ccaa83b859255a43bb011145a42d3f56f67d9268b55bc7e6d98a5981a1d834186ad3105d6739d21547083fe7259c76c0468483fc538e716d8
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: b8e05ccdf96ac330ea83c12450304d4a591f9958c11fd17bed240af8d5ffe08aedafa4c0f4cfccd4d28dc9d4d129daca1023633d5c11601a6cbc77521f6fae66
  languageName: node
  linkType: hard

"is-string@npm:^1.0.5, is-string@npm:^1.0.7":
  version: 1.0.7
  resolution: "is-string@npm:1.0.7"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: 323b3d04622f78d45077cf89aab783b2f49d24dc641aa89b5ad1a72114cfeff2585efc8c12ef42466dff32bde93d839ad321b26884cf75e5a7892a938b089989
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.2, is-symbol@npm:^1.0.3":
  version: 1.0.4
  resolution: "is-symbol@npm:1.0.4"
  dependencies:
    has-symbols: ^1.0.2
  checksum: 92805812ef590738d9de49d677cd17dfd486794773fb6fa0032d16452af46e9b91bb43ffe82c983570f015b37136f4b53b28b8523bfb10b0ece7a66c31a54510
  languageName: node
  linkType: hard

"is-typed-array@npm:^1.1.13":
  version: 1.1.13
  resolution: "is-typed-array@npm:1.1.13"
  dependencies:
    which-typed-array: ^1.1.14
  checksum: 150f9ada183a61554c91e1c4290086d2c100b0dff45f60b028519be72a8db964da403c48760723bf5253979b8dffe7b544246e0e5351dcd05c5fdb1dcc1dc0f0
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^0.1.0":
  version: 0.1.0
  resolution: "is-unicode-supported@npm:0.1.0"
  checksum: a2aab86ee7712f5c2f999180daaba5f361bdad1efadc9610ff5b8ab5495b86e4f627839d085c6530363c6d6d4ecbde340fb8e54bdb83da4ba8e0865ed5513c52
  languageName: node
  linkType: hard

"is-weakmap@npm:^2.0.2":
  version: 2.0.2
  resolution: "is-weakmap@npm:2.0.2"
  checksum: f36aef758b46990e0d3c37269619c0a08c5b29428c0bb11ecba7f75203442d6c7801239c2f31314bc79199217ef08263787f3837d9e22610ad1da62970d6616d
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-weakref@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 95bd9a57cdcb58c63b1c401c60a474b0f45b94719c30f548c891860f051bc2231575c290a6b420c6bc6e7ed99459d424c652bd5bf9a1d5259505dc35b4bf83de
  languageName: node
  linkType: hard

"is-weakset@npm:^2.0.3":
  version: 2.0.3
  resolution: "is-weakset@npm:2.0.3"
  dependencies:
    call-bind: ^1.0.7
    get-intrinsic: ^1.2.4
  checksum: 8b6a20ee9f844613ff8f10962cfee49d981d584525f2357fee0a04dfbcde9fd607ed60cb6dab626dbcc470018ae6392e1ff74c0c1aced2d487271411ad9d85ae
  languageName: node
  linkType: hard

"is-wsl@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-wsl@npm:1.1.0"
  checksum: ea157d232351e68c92bd62fc541771096942fe72f69dff452dd26dcc31466258c570a3b04b8cda2e01cd2968255b02951b8670d08ea4ed76d6b1a646061ac4fe
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1, is-wsl@npm:^2.2.0":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: ^2.0.0
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"isarray@npm:^2.0.5":
  version: 2.0.5
  resolution: "isarray@npm:2.0.5"
  checksum: bd5bbe4104438c4196ba58a54650116007fa0262eccef13a4c55b2e09a5b36b59f1e75b9fcc49883dd9d4953892e6fc007eef9e9155648ceea036e184b0f930a
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 7fe1931ee4e88eb5aa524cd3ceb8c882537bc3a81b02e438b240e47012eef49c86904d0f0e593ea7c3a9996d18d0f1f3be8d3eaa92333977b0c3a9d353d5563e
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 2367407a8d13982d8f7a859a35e7f8dd5d8f75aae4bb5484ede3a9ea1b426dc245aff28b976a2af48ee759fdd9be374ce2bd2669b644f31e76c5f46a2e29a831
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.0.4":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": ^7.12.3
    "@babel/parser": ^7.14.7
    "@istanbuljs/schema": ^0.1.2
    istanbul-lib-coverage: ^3.2.0
    semver: ^6.3.0
  checksum: bf16f1803ba5e51b28bbd49ed955a736488381e09375d830e42ddeb403855b2006f850711d95ad726f2ba3f1ae8e7366de7e51d2b9ac67dc4d80191ef7ddf272
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^6.0.0":
  version: 6.0.3
  resolution: "istanbul-lib-instrument@npm:6.0.3"
  dependencies:
    "@babel/core": ^7.23.9
    "@babel/parser": ^7.23.9
    "@istanbuljs/schema": ^0.1.3
    istanbul-lib-coverage: ^3.2.0
    semver: ^7.5.4
  checksum: 74104c60c65c4fa0e97cc76f039226c356123893929f067bfad5f86fe839e08f5d680354a68fead3bc9c1e2f3fa6f3f53cded70778e821d911e851d349f3545a
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: ^3.0.0
    make-dir: ^4.0.0
    supports-color: ^7.1.0
  checksum: fd17a1b879e7faf9bb1dc8f80b2a16e9f5b7b8498fe6ed580a618c34df0bfe53d2abd35bf8a0a00e628fb7405462576427c7df20bbe4148d19c14b431c974b21
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^4.0.0":
  version: 4.0.1
  resolution: "istanbul-lib-source-maps@npm:4.0.1"
  dependencies:
    debug: ^4.1.1
    istanbul-lib-coverage: ^3.0.0
    source-map: ^0.6.1
  checksum: 21ad3df45db4b81852b662b8d4161f6446cd250c1ddc70ef96a585e2e85c26ed7cd9c2a396a71533cfb981d1a645508bc9618cae431e55d01a0628e7dec62ef2
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.3":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: ^2.0.0
    istanbul-lib-report: ^3.0.0
  checksum: 2072db6e07bfbb4d0eb30e2700250636182398c1af811aea5032acb219d2080f7586923c09fa194029efd6b92361afb3dcbe1ebcc3ee6651d13340f7c6c4ed95
  languageName: node
  linkType: hard

"iterator.prototype@npm:^1.1.2":
  version: 1.1.2
  resolution: "iterator.prototype@npm:1.1.2"
  dependencies:
    define-properties: ^1.2.1
    get-intrinsic: ^1.2.1
    has-symbols: ^1.0.3
    reflect.getprototypeof: ^1.0.4
    set-function-name: ^2.0.1
  checksum: d8a507e2ccdc2ce762e8a1d3f4438c5669160ac72b88b648e59a688eec6bc4e64b22338e74000518418d9e693faf2a092d2af21b9ec7dbf7763b037a54701168
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": ^8.0.2
    "@pkgjs/parseargs": ^0.11.0
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: be31027fc72e7cc726206b9f560395604b82e0fddb46c4cbf9f97d049bcef607491a5afc0699612eaa4213ca5be8fd3e1e7cd187b3040988b65c9489838a7c00
  languageName: node
  linkType: hard

"jest-changed-files@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-changed-files@npm:29.7.0"
  dependencies:
    execa: ^5.0.0
    jest-util: ^29.7.0
    p-limit: ^3.1.0
  checksum: 963e203893c396c5dfc75e00a49426688efea7361b0f0e040035809cecd2d46b3c01c02be2d9e8d38b1138357d2de7719ea5b5be21f66c10f2e9685a5a73bb99
  languageName: node
  linkType: hard

"jest-circus@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-circus@npm:29.7.0"
  dependencies:
    "@jest/environment": ^29.7.0
    "@jest/expect": ^29.7.0
    "@jest/test-result": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    chalk: ^4.0.0
    co: ^4.6.0
    dedent: ^1.0.0
    is-generator-fn: ^2.0.0
    jest-each: ^29.7.0
    jest-matcher-utils: ^29.7.0
    jest-message-util: ^29.7.0
    jest-runtime: ^29.7.0
    jest-snapshot: ^29.7.0
    jest-util: ^29.7.0
    p-limit: ^3.1.0
    pretty-format: ^29.7.0
    pure-rand: ^6.0.0
    slash: ^3.0.0
    stack-utils: ^2.0.3
  checksum: 349437148924a5a109c9b8aad6d393a9591b4dac1918fc97d81b7fc515bc905af9918495055071404af1fab4e48e4b04ac3593477b1d5dcf48c4e71b527c70a7
  languageName: node
  linkType: hard

"jest-cli@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-cli@npm:29.7.0"
  dependencies:
    "@jest/core": ^29.7.0
    "@jest/test-result": ^29.7.0
    "@jest/types": ^29.6.3
    chalk: ^4.0.0
    create-jest: ^29.7.0
    exit: ^0.1.2
    import-local: ^3.0.2
    jest-config: ^29.7.0
    jest-util: ^29.7.0
    jest-validate: ^29.7.0
    yargs: ^17.3.1
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 664901277a3f5007ea4870632ed6e7889db9da35b2434e7cb488443e6bf5513889b344b7fddf15112135495b9875892b156faeb2d7391ddb9e2a849dcb7b6c36
  languageName: node
  linkType: hard

"jest-config@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-config@npm:29.7.0"
  dependencies:
    "@babel/core": ^7.11.6
    "@jest/test-sequencer": ^29.7.0
    "@jest/types": ^29.6.3
    babel-jest: ^29.7.0
    chalk: ^4.0.0
    ci-info: ^3.2.0
    deepmerge: ^4.2.2
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    jest-circus: ^29.7.0
    jest-environment-node: ^29.7.0
    jest-get-type: ^29.6.3
    jest-regex-util: ^29.6.3
    jest-resolve: ^29.7.0
    jest-runner: ^29.7.0
    jest-util: ^29.7.0
    jest-validate: ^29.7.0
    micromatch: ^4.0.4
    parse-json: ^5.2.0
    pretty-format: ^29.7.0
    slash: ^3.0.0
    strip-json-comments: ^3.1.1
  peerDependencies:
    "@types/node": "*"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    "@types/node":
      optional: true
    ts-node:
      optional: true
  checksum: 4cabf8f894c180cac80b7df1038912a3fc88f96f2622de33832f4b3314f83e22b08fb751da570c0ab2b7988f21604bdabade95e3c0c041068ac578c085cf7dff
  languageName: node
  linkType: hard

"jest-diff@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-diff@npm:29.7.0"
  dependencies:
    chalk: ^4.0.0
    diff-sequences: ^29.6.3
    jest-get-type: ^29.6.3
    pretty-format: ^29.7.0
  checksum: 08e24a9dd43bfba1ef07a6374e5af138f53137b79ec3d5cc71a2303515335898888fa5409959172e1e05de966c9e714368d15e8994b0af7441f0721ee8e1bb77
  languageName: node
  linkType: hard

"jest-docblock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-docblock@npm:29.7.0"
  dependencies:
    detect-newline: ^3.0.0
  checksum: 66390c3e9451f8d96c5da62f577a1dad701180cfa9b071c5025acab2f94d7a3efc2515cfa1654ebe707213241541ce9c5530232cdc8017c91ed64eea1bd3b192
  languageName: node
  linkType: hard

"jest-each@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-each@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    chalk: ^4.0.0
    jest-get-type: ^29.6.3
    jest-util: ^29.7.0
    pretty-format: ^29.7.0
  checksum: e88f99f0184000fc8813f2a0aa79e29deeb63700a3b9b7928b8a418d7d93cd24933608591dbbdea732b473eb2021c72991b5cc51a17966842841c6e28e6f691c
  languageName: node
  linkType: hard

"jest-environment-node@npm:^29.6.3, jest-environment-node@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-environment-node@npm:29.7.0"
  dependencies:
    "@jest/environment": ^29.7.0
    "@jest/fake-timers": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    jest-mock: ^29.7.0
    jest-util: ^29.7.0
  checksum: 501a9966292cbe0ca3f40057a37587cb6def25e1e0c5e39ac6c650fe78d3c70a2428304341d084ac0cced5041483acef41c477abac47e9a290d5545fd2f15646
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-get-type@npm:29.6.3"
  checksum: 88ac9102d4679d768accae29f1e75f592b760b44277df288ad76ce5bf038c3f5ce3719dea8aa0f035dac30e9eb034b848ce716b9183ad7cc222d029f03e92205
  languageName: node
  linkType: hard

"jest-haste-map@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-haste-map@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    "@types/graceful-fs": ^4.1.3
    "@types/node": "*"
    anymatch: ^3.0.3
    fb-watchman: ^2.0.0
    fsevents: ^2.3.2
    graceful-fs: ^4.2.9
    jest-regex-util: ^29.6.3
    jest-util: ^29.7.0
    jest-worker: ^29.7.0
    micromatch: ^4.0.4
    walker: ^1.0.8
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: c2c8f2d3e792a963940fbdfa563ce14ef9e14d4d86da645b96d3cd346b8d35c5ce0b992ee08593939b5f718cf0a1f5a90011a056548a1dbf58397d4356786f01
  languageName: node
  linkType: hard

"jest-leak-detector@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-leak-detector@npm:29.7.0"
  dependencies:
    jest-get-type: ^29.6.3
    pretty-format: ^29.7.0
  checksum: e3950e3ddd71e1d0c22924c51a300a1c2db6cf69ec1e51f95ccf424bcc070f78664813bef7aed4b16b96dfbdeea53fe358f8aeaaea84346ae15c3735758f1605
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-matcher-utils@npm:29.7.0"
  dependencies:
    chalk: ^4.0.0
    jest-diff: ^29.7.0
    jest-get-type: ^29.6.3
    pretty-format: ^29.7.0
  checksum: d7259e5f995d915e8a37a8fd494cb7d6af24cd2a287b200f831717ba0d015190375f9f5dc35393b8ba2aae9b2ebd60984635269c7f8cff7d85b077543b7744cd
  languageName: node
  linkType: hard

"jest-message-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-message-util@npm:29.7.0"
  dependencies:
    "@babel/code-frame": ^7.12.13
    "@jest/types": ^29.6.3
    "@types/stack-utils": ^2.0.0
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    micromatch: ^4.0.4
    pretty-format: ^29.7.0
    slash: ^3.0.0
    stack-utils: ^2.0.3
  checksum: a9d025b1c6726a2ff17d54cc694de088b0489456c69106be6b615db7a51b7beb66788bea7a59991a019d924fbf20f67d085a445aedb9a4d6760363f4d7d09930
  languageName: node
  linkType: hard

"jest-mock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-mock@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    "@types/node": "*"
    jest-util: ^29.7.0
  checksum: 81ba9b68689a60be1482212878973700347cb72833c5e5af09895882b9eb5c4e02843a1bbdf23f94c52d42708bab53a30c45a3482952c9eec173d1eaac5b86c5
  languageName: node
  linkType: hard

"jest-pnp-resolver@npm:^1.2.2":
  version: 1.2.3
  resolution: "jest-pnp-resolver@npm:1.2.3"
  peerDependencies:
    jest-resolve: "*"
  peerDependenciesMeta:
    jest-resolve:
      optional: true
  checksum: db1a8ab2cb97ca19c01b1cfa9a9c8c69a143fde833c14df1fab0766f411b1148ff0df878adea09007ac6a2085ec116ba9a996a6ad104b1e58c20adbf88eed9b2
  languageName: node
  linkType: hard

"jest-regex-util@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-regex-util@npm:29.6.3"
  checksum: 0518beeb9bf1228261695e54f0feaad3606df26a19764bc19541e0fc6e2a3737191904607fb72f3f2ce85d9c16b28df79b7b1ec9443aa08c3ef0e9efda6f8f2a
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve-dependencies@npm:29.7.0"
  dependencies:
    jest-regex-util: ^29.6.3
    jest-snapshot: ^29.7.0
  checksum: aeb75d8150aaae60ca2bb345a0d198f23496494677cd6aefa26fc005faf354061f073982175daaf32b4b9d86b26ca928586344516e3e6969aa614cb13b883984
  languageName: node
  linkType: hard

"jest-resolve@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve@npm:29.7.0"
  dependencies:
    chalk: ^4.0.0
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.7.0
    jest-pnp-resolver: ^1.2.2
    jest-util: ^29.7.0
    jest-validate: ^29.7.0
    resolve: ^1.20.0
    resolve.exports: ^2.0.0
    slash: ^3.0.0
  checksum: 0ca218e10731aa17920526ec39deaec59ab9b966237905ffc4545444481112cd422f01581230eceb7e82d86f44a543d520a71391ec66e1b4ef1a578bd5c73487
  languageName: node
  linkType: hard

"jest-runner@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runner@npm:29.7.0"
  dependencies:
    "@jest/console": ^29.7.0
    "@jest/environment": ^29.7.0
    "@jest/test-result": ^29.7.0
    "@jest/transform": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    chalk: ^4.0.0
    emittery: ^0.13.1
    graceful-fs: ^4.2.9
    jest-docblock: ^29.7.0
    jest-environment-node: ^29.7.0
    jest-haste-map: ^29.7.0
    jest-leak-detector: ^29.7.0
    jest-message-util: ^29.7.0
    jest-resolve: ^29.7.0
    jest-runtime: ^29.7.0
    jest-util: ^29.7.0
    jest-watcher: ^29.7.0
    jest-worker: ^29.7.0
    p-limit: ^3.1.0
    source-map-support: 0.5.13
  checksum: f0405778ea64812bf9b5c50b598850d94ccf95d7ba21f090c64827b41decd680ee19fcbb494007cdd7f5d0d8906bfc9eceddd8fa583e753e736ecd462d4682fb
  languageName: node
  linkType: hard

"jest-runtime@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runtime@npm:29.7.0"
  dependencies:
    "@jest/environment": ^29.7.0
    "@jest/fake-timers": ^29.7.0
    "@jest/globals": ^29.7.0
    "@jest/source-map": ^29.6.3
    "@jest/test-result": ^29.7.0
    "@jest/transform": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    chalk: ^4.0.0
    cjs-module-lexer: ^1.0.0
    collect-v8-coverage: ^1.0.0
    glob: ^7.1.3
    graceful-fs: ^4.2.9
    jest-haste-map: ^29.7.0
    jest-message-util: ^29.7.0
    jest-mock: ^29.7.0
    jest-regex-util: ^29.6.3
    jest-resolve: ^29.7.0
    jest-snapshot: ^29.7.0
    jest-util: ^29.7.0
    slash: ^3.0.0
    strip-bom: ^4.0.0
  checksum: d19f113d013e80691e07047f68e1e3448ef024ff2c6b586ce4f90cd7d4c62a2cd1d460110491019719f3c59bfebe16f0e201ed005ef9f80e2cf798c374eed54e
  languageName: node
  linkType: hard

"jest-snapshot@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-snapshot@npm:29.7.0"
  dependencies:
    "@babel/core": ^7.11.6
    "@babel/generator": ^7.7.2
    "@babel/plugin-syntax-jsx": ^7.7.2
    "@babel/plugin-syntax-typescript": ^7.7.2
    "@babel/types": ^7.3.3
    "@jest/expect-utils": ^29.7.0
    "@jest/transform": ^29.7.0
    "@jest/types": ^29.6.3
    babel-preset-current-node-syntax: ^1.0.0
    chalk: ^4.0.0
    expect: ^29.7.0
    graceful-fs: ^4.2.9
    jest-diff: ^29.7.0
    jest-get-type: ^29.6.3
    jest-matcher-utils: ^29.7.0
    jest-message-util: ^29.7.0
    jest-util: ^29.7.0
    natural-compare: ^1.4.0
    pretty-format: ^29.7.0
    semver: ^7.5.3
  checksum: 86821c3ad0b6899521ce75ee1ae7b01b17e6dfeff9166f2cf17f012e0c5d8c798f30f9e4f8f7f5bed01ea7b55a6bc159f5eda778311162cbfa48785447c237ad
  languageName: node
  linkType: hard

"jest-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-util@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    "@types/node": "*"
    chalk: ^4.0.0
    ci-info: ^3.2.0
    graceful-fs: ^4.2.9
    picomatch: ^2.2.3
  checksum: 042ab4980f4ccd4d50226e01e5c7376a8556b472442ca6091a8f102488c0f22e6e8b89ea874111d2328a2080083bf3225c86f3788c52af0bd0345a00eb57a3ca
  languageName: node
  linkType: hard

"jest-validate@npm:^29.6.3, jest-validate@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-validate@npm:29.7.0"
  dependencies:
    "@jest/types": ^29.6.3
    camelcase: ^6.2.0
    chalk: ^4.0.0
    jest-get-type: ^29.6.3
    leven: ^3.1.0
    pretty-format: ^29.7.0
  checksum: 191fcdc980f8a0de4dbdd879fa276435d00eb157a48683af7b3b1b98b0f7d9de7ffe12689b617779097ff1ed77601b9f7126b0871bba4f776e222c40f62e9dae
  languageName: node
  linkType: hard

"jest-watcher@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-watcher@npm:29.7.0"
  dependencies:
    "@jest/test-result": ^29.7.0
    "@jest/types": ^29.6.3
    "@types/node": "*"
    ansi-escapes: ^4.2.1
    chalk: ^4.0.0
    emittery: ^0.13.1
    jest-util: ^29.7.0
    string-length: ^4.0.1
  checksum: 67e6e7fe695416deff96b93a14a561a6db69389a0667e9489f24485bb85e5b54e12f3b2ba511ec0b777eca1e727235b073e3ebcdd473d68888650489f88df92f
  languageName: node
  linkType: hard

"jest-worker@npm:^29.6.3, jest-worker@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-worker@npm:29.7.0"
  dependencies:
    "@types/node": "*"
    jest-util: ^29.7.0
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: 30fff60af49675273644d408b650fc2eb4b5dcafc5a0a455f238322a8f9d8a98d847baca9d51ff197b6747f54c7901daa2287799230b856a0f48287d131f8c13
  languageName: node
  linkType: hard

"jest@npm:^29.6.3":
  version: 29.7.0
  resolution: "jest@npm:29.7.0"
  dependencies:
    "@jest/core": ^29.7.0
    "@jest/types": ^29.6.3
    import-local: ^3.0.2
    jest-cli: ^29.7.0
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 17ca8d67504a7dbb1998cf3c3077ec9031ba3eb512da8d71cb91bcabb2b8995c4e4b292b740cb9bf1cbff5ce3e110b3f7c777b0cefb6f41ab05445f248d0ee0b
  languageName: node
  linkType: hard

"joi@npm:^17.2.1":
  version: 17.13.3
  resolution: "joi@npm:17.13.3"
  dependencies:
    "@hapi/hoek": ^9.3.0
    "@hapi/topo": ^5.1.0
    "@sideway/address": ^4.1.5
    "@sideway/formula": ^3.0.1
    "@sideway/pinpoint": ^2.0.0
  checksum: 66ed454fee3d8e8da1ce21657fd2c7d565d98f3e539d2c5c028767e5f38cbd6297ce54df8312d1d094e62eb38f9452ebb43da4ce87321df66cf5e3f128cbc400
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: ^2.0.1
  bin:
    js-yaml: bin/js-yaml.js
  checksum: c7830dfd456c3ef2c6e355cc5a92e6700ceafa1d14bba54497b34a99f0376cecbb3e9ac14d3e5849b426d5a5140709a66237a8c991c675431271c4ce5504151a
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 944f924f2bd67ad533b3850eee47603eed0f6ae425fd1ee8c760f477e8c34a05f144c1bd4f5a5dd1963141dc79a2c55f89ccc5ab77d039e7077f3ad196b64965
  languageName: node
  linkType: hard

"jsc-android@npm:^250231.0.0":
  version: 250231.0.0
  resolution: "jsc-android@npm:250231.0.0"
  checksum: 6c3f0f6f02fa37a19935b2fbe651e9d6ecc370eb30f2ecee76379337bbf084abb568a1ef1133fe622c5b76f43cf54bb7716f92a94dca010985da38edc48841e2
  languageName: node
  linkType: hard

"jsc-safe-url@npm:^0.2.2":
  version: 0.2.4
  resolution: "jsc-safe-url@npm:0.2.4"
  checksum: 53b5741ba2c0a54da1722929dc80becb2c6fcc9525124fb6c2aec1a00f48e79afffd26816c278111e7b938e37ace029e33cbb8cdaa4ac1f528a87e58022284af
  languageName: node
  linkType: hard

"jscodeshift@npm:^0.14.0":
  version: 0.14.0
  resolution: "jscodeshift@npm:0.14.0"
  dependencies:
    "@babel/core": ^7.13.16
    "@babel/parser": ^7.13.16
    "@babel/plugin-proposal-class-properties": ^7.13.0
    "@babel/plugin-proposal-nullish-coalescing-operator": ^7.13.8
    "@babel/plugin-proposal-optional-chaining": ^7.13.12
    "@babel/plugin-transform-modules-commonjs": ^7.13.8
    "@babel/preset-flow": ^7.13.13
    "@babel/preset-typescript": ^7.13.0
    "@babel/register": ^7.13.16
    babel-core: ^7.0.0-bridge.0
    chalk: ^4.1.2
    flow-parser: 0.*
    graceful-fs: ^4.2.4
    micromatch: ^4.0.4
    neo-async: ^2.5.0
    node-dir: ^0.1.17
    recast: ^0.21.0
    temp: ^0.8.4
    write-file-atomic: ^2.3.0
  peerDependencies:
    "@babel/preset-env": ^7.1.6
  bin:
    jscodeshift: bin/jscodeshift.js
  checksum: 54ea6d639455883336f80b38a70648821c88b7942315dc0fbab01bc34a9ad0f0f78e3bd69304b5ab167e4262d6ed7e6284c6d32525ab01c89d9118df89b3e2a0
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 4dc190771129e12023f729ce20e1e0bfceac84d73a85bc3119f7f938843fe25a4aeccb54b6494dce26fcf263d815f5f31acdefac7cc9329efb8422a4f4d9fa9d
  languageName: node
  linkType: hard

"jsesc@npm:~0.5.0":
  version: 0.5.0
  resolution: "jsesc@npm:0.5.0"
  bin:
    jsesc: bin/jsesc
  checksum: b8b44cbfc92f198ad972fba706ee6a1dfa7485321ee8c0b25f5cedd538dcb20cde3197de16a7265430fce8277a12db066219369e3d51055038946039f6e20e17
  languageName: node
  linkType: hard

"json-buffer@npm:3.0.1":
  version: 3.0.1
  resolution: "json-buffer@npm:3.0.1"
  checksum: 9026b03edc2847eefa2e37646c579300a1f3a4586cfb62bf857832b60c852042d0d6ae55d1afb8926163fa54c2b01d83ae24705f34990348bdac6273a29d4581
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.1":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: ff2b5ba2a70e88fd97a3cb28c1840144c5ce8fae9cbeeddba15afa333a5c407cf0e42300cd0a2885dbb055227fe68d405070faad941beeffbfde9cf3b2c78c5d
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json5@npm:^2.2.2, json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 2a7436a93393830bce797d4626275152e37e877b265e94ca69c99e3d20c2b9dab021279146a39cdb700e71b2dd32a4cebd1514cd57cee102b1af906ce5040349
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: ^4.1.6
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 6447d6224f0d31623eef9b51185af03ac328a7553efcee30fa423d98a9e276ca08db87d71e17f2310b0263fd3ffa6c2a90a6308367f661dc21580f9469897c9e
  languageName: node
  linkType: hard

"jsx-ast-utils@npm:^2.4.1 || ^3.0.0":
  version: 3.3.5
  resolution: "jsx-ast-utils@npm:3.3.5"
  dependencies:
    array-includes: ^3.1.6
    array.prototype.flat: ^1.3.1
    object.assign: ^4.1.4
    object.values: ^1.1.6
  checksum: f4b05fa4d7b5234230c905cfa88d36dc8a58a6666975a3891429b1a8cdc8a140bca76c297225cb7a499fad25a2c052ac93934449a2c31a44fc9edd06c773780a
  languageName: node
  linkType: hard

"jwt-decode@npm:^4.0.0":
  version: 4.0.0
  resolution: "jwt-decode@npm:4.0.0"
  checksum: 390e2edcb31a92e86c8cbdd1edeea4c0d62acd371f8a8f0a8878e499390c0ecf4c658b365c4e941e4ef37d0170e4ca650aaa49f99a45c0b9695a235b210154b0
  languageName: node
  linkType: hard

"keyv@npm:^4.5.4":
  version: 4.5.4
  resolution: "keyv@npm:4.5.4"
  dependencies:
    json-buffer: 3.0.1
  checksum: 74a24395b1c34bd44ad5cb2b49140d087553e170625240b86755a6604cd65aa16efdbdeae5cdb17ba1284a0fbb25ad06263755dbc71b8d8b06f74232ce3cdd72
  languageName: node
  linkType: hard

"kind-of@npm:^5.0.0":
  version: 5.1.0
  resolution: "kind-of@npm:5.1.0"
  checksum: f2a0102ae0cf19c4a953397e552571bad2b588b53282874f25fca7236396e650e2db50d41f9f516bd402536e4df968dbb51b8e69e4d5d4a7173def78448f7bab
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.0, kind-of@npm:^6.0.1, kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: df82cd1e172f957bae9c536286265a5cdbd5eeca487cb0a3b2a7b41ef959fc61f8e7c0e9aeea9c114ccf2c166b6a8dd45a46fd619c1c569d210ecd2765ad5169
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 638401d534585261b6003db9d99afd244dfe82d75ddb6db5c0df412842d5ab30b2ef18de471aaec70fe69a46f17b4ae3c7f01d8a4e6580ef7adb9f4273ad1e55
  languageName: node
  linkType: hard

"levn@npm:^0.4.1":
  version: 0.4.1
  resolution: "levn@npm:0.4.1"
  dependencies:
    prelude-ls: ^1.2.1
    type-check: ~0.4.0
  checksum: 12c5021c859bd0f5248561bf139121f0358285ec545ebf48bb3d346820d5c61a4309535c7f387ed7d84361cf821e124ce346c6b7cef8ee09a67c1473b46d0fc4
  languageName: node
  linkType: hard

"lighthouse-logger@npm:^1.0.0":
  version: 1.4.2
  resolution: "lighthouse-logger@npm:1.4.2"
  dependencies:
    debug: ^2.6.9
    marky: ^1.2.2
  checksum: ba6b73d93424318fab58b4e07c9ed246e3e969a3313f26b69515ed4c06457dd9a0b11bc706948398fdaef26aa4ba5e65cb848c37ce59f470d3c6c450b9b79a33
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"locate-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "locate-path@npm:3.0.0"
  dependencies:
    p-locate: ^3.0.0
    path-exists: ^3.0.0
  checksum: 53db3996672f21f8b0bf2a2c645ae2c13ffdae1eeecfcd399a583bce8516c0b88dcb4222ca6efbbbeb6949df7e46860895be2c02e8d3219abd373ace3bfb4e11
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"locate-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "locate-path@npm:6.0.0"
  dependencies:
    p-locate: ^5.0.0
  checksum: 72eb661788a0368c099a184c59d2fee760b3831c9c1c33955e8a19ae4a21b4116e53fa736dc086cdeb9fce9f7cc508f2f92d2d3aae516f133e16a2bb59a39f5a
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: cb9227612f71b83e42de93eccf1232feeb25e705bdb19ba26c04f91e885bfd3dd5c517c4a97137658190581d3493ea3973072ca010aab7e301046d90740393d1
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: a3f527d22c548f43ae31c861ada88b2637eb48ac6aa3eb56e82d44917971b8aa96fbb37aa60efea674dc4ee8c42074f90f7b1f772e9db375435f6c83a19b3bc6
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: ad580b4bdbb7ca1f7abf7e1bce63a9a0b98e370cf40194b03380a46b4ed799c9573029599caebc1b14e3f24b111aef72b96674a56cfa105e0f5ac70546cdc005
  languageName: node
  linkType: hard

"lodash.throttle@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.throttle@npm:4.1.1"
  checksum: 129c0a28cee48b348aef146f638ef8a8b197944d4e9ec26c1890c19d9bf5a5690fe11b655c77a4551268819b32d27f4206343e30c78961f60b561b8608c8c805
  languageName: node
  linkType: hard

"lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-symbols@npm:^4.1.0":
  version: 4.1.0
  resolution: "log-symbols@npm:4.1.0"
  dependencies:
    chalk: ^4.1.0
    is-unicode-supported: ^0.1.0
  checksum: fce1497b3135a0198803f9f07464165e9eb83ed02ceb2273930a6f8a508951178d8cf4f0378e9d28300a2ed2bc49050995d2bd5f53ab716bb15ac84d58c6ef74
  languageName: node
  linkType: hard

"logkitty@npm:^0.7.1":
  version: 0.7.1
  resolution: "logkitty@npm:0.7.1"
  dependencies:
    ansi-fragments: ^0.2.1
    dayjs: ^1.8.15
    yargs: ^15.1.0
  bin:
    logkitty: bin/logkitty.js
  checksum: f1af990ff09564ef5122597a52bba6d233302c49865e6ddea1343d2a0e2efe3005127e58e93e25c98b6b1f192731fc5c52e3204876a15fc9a52abc8b4f1af931
  languageName: node
  linkType: hard

"long@npm:^5.0.0":
  version: 5.2.3
  resolution: "long@npm:5.2.3"
  checksum: 885ede7c3de4facccbd2cacc6168bae3a02c3e836159ea4252c87b6e34d40af819824b2d4edce330bfb5c4d6e8ce3ec5864bdcf9473fa1f53a4f8225860e5897
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0, loose-envify@npm:^1.1.0, loose-envify@npm:^1.4.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lottie-react-native@npm:^6.7.2":
  version: 6.7.2
  resolution: "lottie-react-native@npm:6.7.2"
  peerDependencies:
    "@dotlottie/react-player": ^1.6.1
    "@lottiefiles/react-lottie-player": ^3.5.3
    react: "*"
    react-native: ">=0.46"
    react-native-windows: ">=0.63.x"
  peerDependenciesMeta:
    "@dotlottie/react-player":
      optional: true
    "@lottiefiles/react-lottie-player":
      optional: true
    react-native-windows:
      optional: true
  checksum: 8b14ca10f4f0e75505ee5485b47c31db2146baea1634e66f0d485abb8a44ba2019930239970015cedaad627e306828be977b4797256aa2cc143b476ed31585fa
  languageName: node
  linkType: hard

"lower-case@npm:^2.0.2":
  version: 2.0.2
  resolution: "lower-case@npm:2.0.2"
  dependencies:
    tslib: ^2.0.3
  checksum: 83a0a5f159ad7614bee8bf976b96275f3954335a84fad2696927f609ddae902802c4f3312d86668722e668bef41400254807e1d3a7f2e8c3eede79691aa1f010
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 6476138d2125387a6d20f100608c2583d415a4f64a0fecf30c9e2dda976614f09cad4baa0842447bd37dd459a7bd27f57d9d8f8ce558805abd487c583f3d774a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"make-dir@npm:^2.0.0, make-dir@npm:^2.1.0":
  version: 2.1.0
  resolution: "make-dir@npm:2.1.0"
  dependencies:
    pify: ^4.0.1
    semver: ^5.6.0
  checksum: 043548886bfaf1820323c6a2997e6d2fa51ccc2586ac14e6f14634f7458b4db2daf15f8c310e2a0abd3e0cddc64df1890d8fc7263033602c47bb12cbfcf86aab
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: ^7.5.3
  checksum: bf0731a2dd3aab4db6f3de1585cea0b746bb73eb5a02e3d8d72757e376e64e6ada190b1eddcde5b2f24a81b688a9897efd5018737d05e02e2a671dda9cff8a8a
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^13.0.0":
  version: 13.0.1
  resolution: "make-fetch-happen@npm:13.0.1"
  dependencies:
    "@npmcli/agent": ^2.0.0
    cacache: ^18.0.0
    http-cache-semantics: ^4.1.1
    is-lambda: ^1.0.1
    minipass: ^7.0.2
    minipass-fetch: ^3.0.0
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.3
    proc-log: ^4.2.0
    promise-retry: ^2.0.1
    ssri: ^10.0.0
  checksum: 5c9fad695579b79488fa100da05777213dd9365222f85e4757630f8dd2a21a79ddd3206c78cfd6f9b37346819681782b67900ac847a57cf04190f52dda5343fd
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: 1.0.5
  checksum: b38a025a12c8146d6eeea5a7f2bf27d51d8ad6064da8ca9405fcf7bf9b54acd43e3b30ddd7abb9b1bfa4ddb266019133313482570ddb207de568f71ecfcf6060
  languageName: node
  linkType: hard

"marky@npm:^1.2.2":
  version: 1.2.5
  resolution: "marky@npm:1.2.5"
  checksum: 823b946677749551cdfc3b5221685478b5d1b9cc0dc03eff977c6f9a615fb05c67559f9556cb3c0fcb941a9ea0e195e37befd83026443396ccee8b724f54f4c5
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 9d0128ed425a89f4cba8f787dca27ad9408b5cb1b220af2d938e2a0629d17d879a34d2cb19318bdb26c3f14c77dd5dfbae67211f5caaf07b61b1f2c5c8c7dc16
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.28":
  version: 2.0.28
  resolution: "mdn-data@npm:2.0.28"
  checksum: f51d587a6ebe8e426c3376c74ea6df3e19ec8241ed8e2466c9c8a3904d5d04397199ea4f15b8d34d14524b5de926d8724ae85207984be47e165817c26e49e0aa
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.30":
  version: 2.0.30
  resolution: "mdn-data@npm:2.0.30"
  checksum: d6ac5ac7439a1607df44b22738ecf83f48e66a0874e4482d6424a61c52da5cde5750f1d1229b6f5fa1b80a492be89465390da685b11f97d62b8adcc6e88189aa
  languageName: node
  linkType: hard

"memoize-one@npm:^5.0.0":
  version: 5.2.1
  resolution: "memoize-one@npm:5.2.1"
  checksum: a3cba7b824ebcf24cdfcd234aa7f86f3ad6394b8d9be4c96ff756dafb8b51c7f71320785fbc2304f1af48a0467cbbd2a409efc9333025700ed523f254cb52e3d
  languageName: node
  linkType: hard

"memoize-one@npm:^6.0.0":
  version: 6.0.0
  resolution: "memoize-one@npm:6.0.0"
  checksum: f185ea69f7cceae5d1cb596266dcffccf545e8e7b4106ec6aa93b71ab9d16460dd118ac8b12982c55f6d6322fcc1485de139df07eacffaae94888b9b3ad7675f
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0, merge2@npm:^1.4.1":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"metro-babel-transformer@npm:0.80.9":
  version: 0.80.9
  resolution: "metro-babel-transformer@npm:0.80.9"
  dependencies:
    "@babel/core": ^7.20.0
    hermes-parser: 0.20.1
    nullthrows: ^1.1.1
  checksum: 0fd9b7f3c6807163a4537939ead7d4a033b6233ba489bbc84c843dc1de7b6cddd185fee0a1c1791d05334cd8efebf434cbff486a42506843739088f3bb3c6358
  languageName: node
  linkType: hard

"metro-cache-key@npm:0.80.9":
  version: 0.80.9
  resolution: "metro-cache-key@npm:0.80.9"
  checksum: 9c8547dcf6207c45ac726bcb35be43405515940eff8f9bacec354895f50e5cf2787fbb4860be7b1e10856228fd6eb0bbf8bf7065fabbaf90aa3cf9755d32ffe2
  languageName: node
  linkType: hard

"metro-cache@npm:0.80.9":
  version: 0.80.9
  resolution: "metro-cache@npm:0.80.9"
  dependencies:
    metro-core: 0.80.9
    rimraf: ^3.0.2
  checksum: 269d2f17cd82d5a4c7ea39227c3ae4e03982ca7f6dc4a84353bc99ee5b63a8fa42a485addbadea47c91ecbea836595033913ae3c7c309c0a1caae41d4e3799df
  languageName: node
  linkType: hard

"metro-config@npm:0.80.9, metro-config@npm:^0.80.3":
  version: 0.80.9
  resolution: "metro-config@npm:0.80.9"
  dependencies:
    connect: ^3.6.5
    cosmiconfig: ^5.0.5
    jest-validate: ^29.6.3
    metro: 0.80.9
    metro-cache: 0.80.9
    metro-core: 0.80.9
    metro-runtime: 0.80.9
  checksum: 9822a2de858f4ad2d714cb2f70e51552a660ae059a490e4e7728b7b061367f6c6dce90bc4b49144e152e6dbece922a401183570b289dd6f8d595d5fcf3dfa781
  languageName: node
  linkType: hard

"metro-core@npm:0.80.9, metro-core@npm:^0.80.3":
  version: 0.80.9
  resolution: "metro-core@npm:0.80.9"
  dependencies:
    lodash.throttle: ^4.1.1
    metro-resolver: 0.80.9
  checksum: c39c4660e974bda81dae43233f7857ffb60a429bf1b5426b4ea9a3d28ce7951543d56ec5a299a3abf87149a2e8b6faeef955344e351312d70ca6d9b910db2b28
  languageName: node
  linkType: hard

"metro-file-map@npm:0.80.9":
  version: 0.80.9
  resolution: "metro-file-map@npm:0.80.9"
  dependencies:
    anymatch: ^3.0.3
    debug: ^2.2.0
    fb-watchman: ^2.0.0
    fsevents: ^2.3.2
    graceful-fs: ^4.2.4
    invariant: ^2.2.4
    jest-worker: ^29.6.3
    micromatch: ^4.0.4
    node-abort-controller: ^3.1.1
    nullthrows: ^1.1.1
    walker: ^1.0.7
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: e233b25f34b01cb6e9ae6ab868f74d0a7013e52a8ad47619d6ebe2c00b3df228df87fcedb0b7e3d9a0de54ee93a725df1356ee705eb5cac80076703a2e4799e4
  languageName: node
  linkType: hard

"metro-minify-terser@npm:0.80.9":
  version: 0.80.9
  resolution: "metro-minify-terser@npm:0.80.9"
  dependencies:
    terser: ^5.15.0
  checksum: 8aaea147f45332920eb5f70514ee25f65a9e091351ced0ca72ffa6c82c3478d68f962472a4e92d96cb64712bb81f69a072495e9fb7e78173b502d7c32a2a44fc
  languageName: node
  linkType: hard

"metro-resolver@npm:0.80.9":
  version: 0.80.9
  resolution: "metro-resolver@npm:0.80.9"
  checksum: a24f6b8ecc5edf38886080e714eddb4c1cd93345e8052997a194210b42b3c453353a95652e33770a294805cb5fae67620bfcb8432ba866b60479bebb34a6958a
  languageName: node
  linkType: hard

"metro-runtime@npm:0.80.9, metro-runtime@npm:^0.80.3":
  version: 0.80.9
  resolution: "metro-runtime@npm:0.80.9"
  dependencies:
    "@babel/runtime": ^7.0.0
  checksum: 2d087ebc82de0796741cd77bc4af0c20117eb0dc4fc91dfad3be44eb3389bbf6caef7b1605b7907e59ef0c5532617e0b2fb6c5b64df24d03c14748173427b1d4
  languageName: node
  linkType: hard

"metro-source-map@npm:0.80.9, metro-source-map@npm:^0.80.3":
  version: 0.80.9
  resolution: "metro-source-map@npm:0.80.9"
  dependencies:
    "@babel/traverse": ^7.20.0
    "@babel/types": ^7.20.0
    invariant: ^2.2.4
    metro-symbolicate: 0.80.9
    nullthrows: ^1.1.1
    ob1: 0.80.9
    source-map: ^0.5.6
    vlq: ^1.0.0
  checksum: d6423cbe4c861eead953e24bb97d774772afa6f10c75c473d4d35965300a38259ad769b54a62b6d4a73ecaaef8ad2806455bf1fc2e89d8d7839915b30a6344d6
  languageName: node
  linkType: hard

"metro-symbolicate@npm:0.80.9":
  version: 0.80.9
  resolution: "metro-symbolicate@npm:0.80.9"
  dependencies:
    invariant: ^2.2.4
    metro-source-map: 0.80.9
    nullthrows: ^1.1.1
    source-map: ^0.5.6
    through2: ^2.0.1
    vlq: ^1.0.0
  bin:
    metro-symbolicate: src/index.js
  checksum: 070c4a48632e6137e8715c234f31e9c36b8e6c0a7b8e560168c042af00c7764cd5ba0a431ea7071f193d42d73cace0a500fd4b181a296f15e49866b221288d83
  languageName: node
  linkType: hard

"metro-transform-plugins@npm:0.80.9":
  version: 0.80.9
  resolution: "metro-transform-plugins@npm:0.80.9"
  dependencies:
    "@babel/core": ^7.20.0
    "@babel/generator": ^7.20.0
    "@babel/template": ^7.0.0
    "@babel/traverse": ^7.20.0
    nullthrows: ^1.1.1
  checksum: 3179138b38385bfd20553237a8e3d5243b26c2b3cab3742217b1dd81a69a5dfffdd71d5017d1a26b6f8282e73680879c47c143ed8fa3f71d6dabddfd3b154f8b
  languageName: node
  linkType: hard

"metro-transform-worker@npm:0.80.9":
  version: 0.80.9
  resolution: "metro-transform-worker@npm:0.80.9"
  dependencies:
    "@babel/core": ^7.20.0
    "@babel/generator": ^7.20.0
    "@babel/parser": ^7.20.0
    "@babel/types": ^7.20.0
    metro: 0.80.9
    metro-babel-transformer: 0.80.9
    metro-cache: 0.80.9
    metro-cache-key: 0.80.9
    metro-minify-terser: 0.80.9
    metro-source-map: 0.80.9
    metro-transform-plugins: 0.80.9
    nullthrows: ^1.1.1
  checksum: 77b108e5a150b88007631c0c7312fdafdf8525214df3f9a185f8023caef3a8f8d9c695ab75f4686ed4abfce6a0c5ea80ab117fafdc4a21de24413ef491f74acd
  languageName: node
  linkType: hard

"metro@npm:0.80.9, metro@npm:^0.80.3":
  version: 0.80.9
  resolution: "metro@npm:0.80.9"
  dependencies:
    "@babel/code-frame": ^7.0.0
    "@babel/core": ^7.20.0
    "@babel/generator": ^7.20.0
    "@babel/parser": ^7.20.0
    "@babel/template": ^7.0.0
    "@babel/traverse": ^7.20.0
    "@babel/types": ^7.20.0
    accepts: ^1.3.7
    chalk: ^4.0.0
    ci-info: ^2.0.0
    connect: ^3.6.5
    debug: ^2.2.0
    denodeify: ^1.2.1
    error-stack-parser: ^2.0.6
    graceful-fs: ^4.2.4
    hermes-parser: 0.20.1
    image-size: ^1.0.2
    invariant: ^2.2.4
    jest-worker: ^29.6.3
    jsc-safe-url: ^0.2.2
    lodash.throttle: ^4.1.1
    metro-babel-transformer: 0.80.9
    metro-cache: 0.80.9
    metro-cache-key: 0.80.9
    metro-config: 0.80.9
    metro-core: 0.80.9
    metro-file-map: 0.80.9
    metro-resolver: 0.80.9
    metro-runtime: 0.80.9
    metro-source-map: 0.80.9
    metro-symbolicate: 0.80.9
    metro-transform-plugins: 0.80.9
    metro-transform-worker: 0.80.9
    mime-types: ^2.1.27
    node-fetch: ^2.2.0
    nullthrows: ^1.1.1
    rimraf: ^3.0.2
    serialize-error: ^2.1.0
    source-map: ^0.5.6
    strip-ansi: ^6.0.0
    throat: ^5.0.0
    ws: ^7.5.1
    yargs: ^17.6.2
  bin:
    metro: src/cli.js
  checksum: 085191ea2a1d599ff99a4e97d9387f22d41bc0225bc579e3a708b4a735339163706ba7807711629550d6a54039009615528f685f6669034b6e701fe73657aa7c
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4":
  version: 4.0.7
  resolution: "micromatch@npm:4.0.7"
  dependencies:
    braces: ^3.0.3
    picomatch: ^2.3.1
  checksum: 3cde047d70ad80cf60c787b77198d680db3b8c25b23feb01de5e2652205d9c19f43bd81882f69a0fd1f0cde6a7a122d774998aad3271ddb1b8accf8a0f480cf7
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-db@npm:>= 1.43.0 < 2":
  version: 1.53.0
  resolution: "mime-db@npm:1.53.0"
  checksum: 3fd9380bdc0b085d0b56b580e4f89ca4fc3b823722310d795c248f0806b9a80afd5d8f4347f015ad943b9ecfa7cc0b71dffa0db96fa776d01a13474821a2c7fb
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.27, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: fef25e39263e6d207580bdc629f8872a3f9772c923c7f8c7e793175cee22777bbe8bba95e5d509a40aaa292d8974514ce634ae35769faa45f22d17edda5e8557
  languageName: node
  linkType: hard

"mime@npm:^2.4.1":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 1497ba7b9f6960694268a557eae24b743fd2923da46ec392b042469f4b901721ba0adcf8b0d3c2677839d0e243b209d76e5edcbd09cfdeffa2dfb6bb4df4b862
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mimic-response@npm:^3.1.0":
  version: 3.1.0
  resolution: "mimic-response@npm:3.1.0"
  checksum: 25739fee32c17f433626bf19f016df9036b75b3d84a3046c7d156e72ec963dd29d7fc8a302f55a3d6c5a4ff24259676b15d915aad6480815a969ff2ec0836867
  languageName: node
  linkType: hard

"minimatch@npm:9.0.3":
  version: 9.0.3
  resolution: "minimatch@npm:9.0.3"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 253487976bf485b612f16bf57463520a14f512662e592e95c571afdab1442a6a6864b6c88f248ce6fc4ff0b6de04ac7aa6c8bb51e868e99d1d65eb0658a708b5
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.2, minimatch@npm:^3.0.4, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: c154e566406683e7bcb746e000b84d74465b3a832c45d59912b9b55cd50dee66e5c4b1e5566dba26154040e51672f9aa450a9aef0c97cfc7336b78b7afb9540a
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: ^2.0.1
  checksum: 2c035575eda1e50623c731ec6c14f65a85296268f749b9337005210bb2b34e2705f8ef1a358b188f69892286ab99dc42c8fb98a57bde55c8d81b3023c19cea28
  languageName: node
  linkType: hard

"minimist@npm:^1.2.0, minimist@npm:^1.2.3, minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 75a6d645fb122dad29c06a7597bddea977258957ed88d7a6df59b5cd3fe4a527e253e9bbf2e783e4b73657f9098b96a5fe96ab8a113655d4109108577ecf85b0
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: ^7.0.3
  checksum: b251bceea62090f67a6cced7a446a36f4cd61ee2d5cea9aee7fff79ba8030e416327a1c5aa2908dc22629d06214b46d88fdab8c51ac76bacbf5703851b5ad342
  languageName: node
  linkType: hard

"minipass-fetch@npm:^3.0.0":
  version: 3.0.5
  resolution: "minipass-fetch@npm:3.0.5"
  dependencies:
    encoding: ^0.1.13
    minipass: ^7.0.3
    minipass-sized: ^1.0.3
    minizlib: ^2.1.2
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 8047d273236157aab27ab7cd8eab7ea79e6ecd63e8f80c3366ec076cb9a0fed550a6935bab51764369027c414647fd8256c2a20c5445fb250c483de43350de83
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: ^4.0.0
  checksum: a30d083c8054cee83cdcdc97f97e4641a3f58ae743970457b1489ce38ee1167b3aaf7d815cd39ec7a99b9c40397fd4f686e83750e73e652b21cb516f6d845e48
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0":
  version: 5.0.0
  resolution: "minipass@npm:5.0.0"
  checksum: 425dab288738853fded43da3314a0b5c035844d6f3097a8e3b5b29b328da8f3c1af6fc70618b32c29ff906284cf6406b6841376f21caaadd0793c1d5a6a620ea
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 2bfd325b95c555f2b4d2814d49325691c7bee937d753814861b0b49d5edcda55cbbf22b6b6a60bb91eddac8668771f03c5ff647dcd9d0f798e9548b9cdc46ee3
  languageName: node
  linkType: hard

"minizlib@npm:^2.1.1, minizlib@npm:^2.1.2":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"mixin-object@npm:^2.0.1":
  version: 2.0.1
  resolution: "mixin-object@npm:2.0.1"
  dependencies:
    for-in: ^0.1.3
    is-extendable: ^0.1.1
  checksum: 7d0eb7c2f06435fcc01d132824b4c973a0df689a117d8199d79911b506363b6f4f86a84458a63f3acfa7388f3052612cfe27105400b4932678452925a9739a4c
  languageName: node
  linkType: hard

"mkdirp-classic@npm:^0.5.2, mkdirp-classic@npm:^0.5.3":
  version: 0.5.3
  resolution: "mkdirp-classic@npm:0.5.3"
  checksum: 3f4e088208270bbcc148d53b73e9a5bd9eef05ad2cbf3b3d0ff8795278d50dd1d11a8ef1875ff5aea3fa888931f95bfcb2ad5b7c1061cfefd6284d199e6776ac
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.1":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: ^1.2.6
  bin:
    mkdirp: bin/cmd.js
  checksum: 0c91b721bb12c3f9af4b77ebf73604baf350e64d80df91754dc509491ae93bf238581e59c7188360cec7cb62fc4100959245a42cfe01834efedc5e9d068376c2
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3, mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"moti@npm:^0.29.0":
  version: 0.29.0
  resolution: "moti@npm:0.29.0"
  dependencies:
    framer-motion: ^6.5.1
  peerDependencies:
    react-native-reanimated: "*"
  checksum: 1feea59c1dcfa694b37afada036b6efccd908b370c71a545fac0e077386e5907b6d96375406c0a1b3de3b33386b0e1bcc949fe148067adb90a0c9511f63f3cb4
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"n2chat@workspace:.":
  version: 0.0.0-use.local
  resolution: "n2chat@workspace:."
  dependencies:
    "@babel/core": ^7.20.0
    "@babel/preset-env": ^7.20.0
    "@babel/runtime": ^7.20.0
    "@eslint/compat": ^1.1.0
    "@eslint/js": ^9.5.0
    "@hookform/resolvers": ^3.6.0
    "@notifee/react-native": ^9.1.8
    "@react-native-community/blur": ^4.4.0
    "@react-native-community/eslint-config": ^3.2.0
    "@react-native-community/netinfo": 6.0.6
    "@react-native-firebase/app": ^20.3.0
    "@react-native-firebase/messaging": ^20.3.0
    "@react-native/babel-preset": 0.74.83
    "@react-native/eslint-config": 0.74.83
    "@react-native/metro-config": 0.74.83
    "@react-native/typescript-config": 0.74.83
    "@react-navigation/bottom-tabs": ^6.5.20
    "@react-navigation/drawer": ^6.6.15
    "@react-navigation/native": ^6.1.17
    "@react-navigation/native-stack": ^6.9.26
    "@types/react": ^18.2.6
    "@types/react-test-renderer": ^18.0.0
    babel-jest: ^29.6.3
    eslint: 9.x
    eslint-plugin-prettier: ^5.1.3
    eslint-plugin-react: ^7.34.3
    eslint-plugin-react-hooks: ^4.6.2
    eslint-plugin-react-native: ^4.1.0
    globals: ^15.6.0
    husky: ^9.1.7
    jest: ^29.6.3
    jwt-decode: ^4.0.0
    lottie-react-native: ^6.7.2
    moti: ^0.29.0
    prettier: 2.8.8
    react: 18.2.0
    react-hook-form: ^7.51.5
    react-native: 0.74.1
    react-native-audio-recorder-player: ^3.6.11
    react-native-biometrics: ^3.0.1
    react-native-bootsplash: ^5.5.3
    react-native-compressor: ^1.8.25
    react-native-document-picker: ^9.3.0
    react-native-dotenv: ^3.4.11
    react-native-emoji-selector: ^0.2.0
    react-native-fs: ^2.20.0
    react-native-gesture-handler: ^2.16.2
    react-native-incall-manager: ^4.2.0
    react-native-linear-gradient: ^2.8.3
    react-native-mmkv: ^2.12.2
    react-native-permissions: ^4.1.5
    react-native-reanimated: ^3.11.0
    react-native-reanimated-carousel: ^3.5.1
    react-native-responsive-screen: ^1.4.2
    react-native-safe-area-context: ^4.10.1
    react-native-screens: ^3.31.1
    react-native-sound: ^0.11.2
    react-native-svg: ^15.3.0
    react-native-svg-transformer: ^1.4.0
    react-native-video: ^6.4.3
    react-native-vision-camera: ^4.5.0
    react-native-web: ^0.19.12
    react-native-webrtc: ^124.0.4
    react-test-renderer: 18.2.0
    typescript: 5.0.4
    typescript-eslint: ^7.13.1
    uri-scheme: ^1.3.1
    zod: ^3.23.8
    zustand: ^4.5.2
  languageName: unknown
  linkType: soft

"nanoid@npm:^3.1.23":
  version: 3.3.7
  resolution: "nanoid@npm:3.3.7"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: d36c427e530713e4ac6567d488b489a36582ef89da1d6d4e3b87eded11eb10d7042a877958c6f104929809b2ab0bafa17652b076cdf84324aa75b30b722204f2
  languageName: node
  linkType: hard

"napi-build-utils@npm:^1.0.1":
  version: 1.0.2
  resolution: "napi-build-utils@npm:1.0.2"
  checksum: 06c14271ee966e108d55ae109f340976a9556c8603e888037145d6522726aebe89dd0c861b4b83947feaf6d39e79e08817559e8693deedc2c94e82c5cbd090c7
  languageName: node
  linkType: hard

"natural-compare-lite@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare-lite@npm:1.4.0"
  checksum: 5222ac3986a2b78dd6069ac62cbb52a7bf8ffc90d972ab76dfe7b01892485d229530ed20d0c62e79a6b363a663b273db3bde195a1358ce9e5f779d4453887225
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3, negotiator@npm:^0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"neo-async@npm:^2.5.0":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"no-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "no-case@npm:3.0.4"
  dependencies:
    lower-case: ^2.0.2
    tslib: ^2.0.3
  checksum: 0b2ebc113dfcf737d48dde49cfebf3ad2d82a8c3188e7100c6f375e30eafbef9e9124aadc3becef237b042fd5eb0aad2fd78669c20972d045bbe7fea8ba0be5c
  languageName: node
  linkType: hard

"nocache@npm:^3.0.1":
  version: 3.0.4
  resolution: "nocache@npm:3.0.4"
  checksum: 6be9ee67eb561ecedc56d805c024c0fda55b9836ecba659c720073b067929aa4fe04bb7121480e004c9cf52989e62d8720f29a7fe0269f1a4941221a1e4be1c2
  languageName: node
  linkType: hard

"node-abi@npm:^3.3.0":
  version: 3.65.0
  resolution: "node-abi@npm:3.65.0"
  dependencies:
    semver: ^7.3.5
  checksum: 5a60f2b0c73fe0a1123e581bd99e43729f4aa3f4b9b19f1915567128d52540e8f812474410a446cd77d708a3a1139e0b2abf1d0823ba6b5f5d47aa4345931706
  languageName: node
  linkType: hard

"node-abort-controller@npm:^3.1.1":
  version: 3.1.1
  resolution: "node-abort-controller@npm:3.1.1"
  checksum: 2c340916af9710328b11c0828223fc65ba320e0d082214a211311bf64c2891028e42ef276b9799188c4ada9e6e1c54cf7a0b7c05dd9d59fcdc8cd633304c8047
  languageName: node
  linkType: hard

"node-addon-api@npm:^6.1.0":
  version: 6.1.0
  resolution: "node-addon-api@npm:6.1.0"
  dependencies:
    node-gyp: latest
  checksum: 3a539510e677cfa3a833aca5397300e36141aca064cdc487554f2017110709a03a95da937e98c2a14ec3c626af7b2d1b6dabe629a481f9883143d0d5bff07bf2
  languageName: node
  linkType: hard

"node-dir@npm:^0.1.17":
  version: 0.1.17
  resolution: "node-dir@npm:0.1.17"
  dependencies:
    minimatch: ^3.0.2
  checksum: 29de9560e52cdac8d3f794d38d782f6799e13d4d11aaf96d3da8c28458e1c5e33bb5f8edfb42dc34172ec5516c50c5b8850c9e1526542616757a969267263328
  languageName: node
  linkType: hard

"node-fetch@npm:^2.2.0, node-fetch@npm:^2.6.0, node-fetch@npm:^2.6.12":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: ^5.0.0
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: d76d2f5edb451a3f05b15115ec89fc6be39de37c6089f1b6368df03b91e1633fd379a7e01b7ab05089a25034b2023d959b47e59759cb38d88341b2459e89d6e5
  languageName: node
  linkType: hard

"node-forge@npm:^1":
  version: 1.3.1
  resolution: "node-forge@npm:1.3.1"
  checksum: 08fb072d3d670599c89a1704b3e9c649ff1b998256737f0e06fbd1a5bf41cae4457ccaee32d95052d80bbafd9ffe01284e078c8071f0267dc9744e51c5ed42a9
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 10.2.0
  resolution: "node-gyp@npm:10.2.0"
  dependencies:
    env-paths: ^2.2.0
    exponential-backoff: ^3.1.1
    glob: ^10.3.10
    graceful-fs: ^4.2.6
    make-fetch-happen: ^13.0.0
    nopt: ^7.0.0
    proc-log: ^4.1.0
    semver: ^7.3.5
    tar: ^6.2.1
    which: ^4.0.0
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 0233759d8c19765f7fdc259a35eb046ad86c3d09e22f7384613ae2b89647dd27fcf833fdf5293d9335041e91f9b1c539494225959cdb312a5c8080b7534b926f
  languageName: node
  linkType: hard

"node-html-parser@npm:^6.1.12":
  version: 6.1.13
  resolution: "node-html-parser@npm:6.1.13"
  dependencies:
    css-select: ^5.1.0
    he: 1.2.0
  checksum: bf172147f5bee7ab3dbef4dce0308a2c02264bac178ebd6375cd460e0a120e916451b93601aabdd59331c467bed0e3f9bb5b362a74050254846135d5cd5dd66d
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: d0b30b1ee6d961851c60d5eaa745d30b5c95d94bc0e74b81e5292f7c42a49e3af87f1eb9e89f59456f80645d679202537de751b7d72e9e40ceea40c5e449057e
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.18":
  version: 2.0.18
  resolution: "node-releases@npm:2.0.18"
  checksum: ef55a3d853e1269a6d6279b7692cd6ff3e40bc74947945101138745bfdc9a5edabfe72cb19a31a8e45752e1910c4c65c77d931866af6357f242b172b7283f5b3
  languageName: node
  linkType: hard

"node-stream-zip@npm:^1.9.1":
  version: 1.15.0
  resolution: "node-stream-zip@npm:1.15.0"
  checksum: 0b73ffbb09490e479c8f47038d7cba803e6242618fbc1b71c26782009d388742ed6fb5ce6e9d31f528b410249e7eb1c6e7534e9d3792a0cafd99813ac5a35107
  languageName: node
  linkType: hard

"nopt@npm:^7.0.0":
  version: 7.2.1
  resolution: "nopt@npm:7.2.1"
  dependencies:
    abbrev: ^2.0.0
  bin:
    nopt: bin/nopt.js
  checksum: 6fa729cc77ce4162cfad8abbc9ba31d4a0ff6850c3af61d59b505653bef4781ec059f8890ecfe93ee8aa0c511093369cca88bfc998101616a2904e715bbbb7c9
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: ^3.0.0
  checksum: 5374c0cea4b0bbfdfae62da7bbdf1e1558d338335f4cacf2515c282ff358ff27b2ecb91ffa5330a8b14390ac66a1e146e10700440c1ab868208430f56b5f4d23
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.1.1
  resolution: "nth-check@npm:2.1.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5afc3dafcd1573b08877ca8e6148c52abd565f1d06b1eb08caf982e3fa289a82f2cae697ffb55b5021e146d60443f1590a5d6b944844e944714a5b549675bcd3
  languageName: node
  linkType: hard

"nullthrows@npm:^1.1.1":
  version: 1.1.1
  resolution: "nullthrows@npm:1.1.1"
  checksum: 10806b92121253eb1b08ecf707d92480f5331ba8ae5b23fa3eb0548ad24196eb797ed47606153006568a5733ea9e528a3579f21421f7828e09e7756f4bdd386f
  languageName: node
  linkType: hard

"ob1@npm:0.80.9":
  version: 0.80.9
  resolution: "ob1@npm:0.80.9"
  checksum: 50730f4c4fd043e1d3e713a40e6c6ee04882b56abf57bc0afbfe18982ad4e64f0d7cfd0b8fc37377af37f0a0dbf1bb46eb3c1625eacff0cd834717703028cfb2
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.0, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.1":
  version: 1.13.2
  resolution: "object-inspect@npm:1.13.2"
  checksum: 9f850b3c045db60e0e97746e809ee4090d6ce62195af17dd1e9438ac761394a7d8ec4f7906559aea5424eaf61e35d3e53feded2ccd5f62fcc7d9670d3c8eb353
  languageName: node
  linkType: hard

"object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.4, object.assign@npm:^4.1.5":
  version: 4.1.5
  resolution: "object.assign@npm:4.1.5"
  dependencies:
    call-bind: ^1.0.5
    define-properties: ^1.2.1
    has-symbols: ^1.0.3
    object-keys: ^1.1.1
  checksum: f9aeac0541661370a1fc86e6a8065eb1668d3e771f7dbb33ee54578201336c057b21ee61207a186dd42db0c62201d91aac703d20d12a79fc79c353eed44d4e25
  languageName: node
  linkType: hard

"object.entries@npm:^1.1.8":
  version: 1.1.8
  resolution: "object.entries@npm:1.1.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: 5314877cb637ef3437a30bba61d9bacdb3ce74bf73ac101518be0633c37840c8cc67407edb341f766e8093b3d7516d5c3358f25adfee4a2c697c0ec4c8491907
  languageName: node
  linkType: hard

"object.fromentries@npm:^2.0.8":
  version: 2.0.8
  resolution: "object.fromentries@npm:2.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-object-atoms: ^1.0.0
  checksum: 29b2207a2db2782d7ced83f93b3ff5d425f901945f3665ffda1821e30a7253cd1fd6b891a64279976098137ddfa883d748787a6fea53ecdb51f8df8b8cec0ae1
  languageName: node
  linkType: hard

"object.values@npm:^1.1.6, object.values@npm:^1.2.0":
  version: 1.2.0
  resolution: "object.values@npm:1.2.0"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: 51fef456c2a544275cb1766897f34ded968b22adfc13ba13b5e4815fdaf4304a90d42a3aee114b1f1ede048a4890381d47a5594d84296f2767c6a0364b9da8fa
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: 1.1.1
  checksum: d20929a25e7f0bb62f937a425b5edeb4e4cde0540d77ba146ec9357f00b0d497cdb3b9b05b9c8e46222407d1548d08166bff69cc56dfa55ba0e4469228920ff0
  languageName: node
  linkType: hard

"on-finished@npm:~2.3.0":
  version: 2.3.0
  resolution: "on-finished@npm:2.3.0"
  dependencies:
    ee-first: 1.1.1
  checksum: 1db595bd963b0124d6fa261d18320422407b8f01dc65863840f3ddaaf7bcad5b28ff6847286703ca53f4ec19595bd67a2f1253db79fc4094911ec6aa8df1671b
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 2bf13467215d1e540a62a75021e8b318a6cfc5d4fc53af8e8f84ad98dbcea02d506c6d24180cd62e1d769c44721ba542f3154effc1f7579a8288c9f7873ed8e5
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^5.1.0, onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: ^2.1.0
  checksum: 2478859ef817fc5d4e9c2f9e5728512ddd1dbc9fb7829ad263765bb6d3b91ce699d6e2332eef6b7dff183c2f490bd3349f1666427eaba4469fba0ac38dfd0d34
  languageName: node
  linkType: hard

"open@npm:^6.2.0":
  version: 6.4.0
  resolution: "open@npm:6.4.0"
  dependencies:
    is-wsl: ^1.1.0
  checksum: e5037facf3e03ed777537db3e2511ada37f351c4394e1dadccf9cac11d63b28447ae8b495b7b138659910fd78d918bafed546e47163673c4a4e43dbb5ac53c5d
  languageName: node
  linkType: hard

"open@npm:^7.0.3":
  version: 7.4.2
  resolution: "open@npm:7.4.2"
  dependencies:
    is-docker: ^2.0.0
    is-wsl: ^2.1.1
  checksum: 3333900ec0e420d64c23b831bc3467e57031461d843c801f569b2204a1acc3cd7b3ec3c7897afc9dde86491dfa289708eb92bba164093d8bd88fb2c231843c91
  languageName: node
  linkType: hard

"optionator@npm:^0.9.3":
  version: 0.9.4
  resolution: "optionator@npm:0.9.4"
  dependencies:
    deep-is: ^0.1.3
    fast-levenshtein: ^2.0.6
    levn: ^0.4.1
    prelude-ls: ^1.2.1
    type-check: ^0.4.0
    word-wrap: ^1.2.5
  checksum: ecbd010e3dc73e05d239976422d9ef54a82a13f37c11ca5911dff41c98a6c7f0f163b27f922c37e7f8340af9d36febd3b6e9cef508f3339d4c393d7276d716bb
  languageName: node
  linkType: hard

"ora@npm:^5.4.1":
  version: 5.4.1
  resolution: "ora@npm:5.4.1"
  dependencies:
    bl: ^4.1.0
    chalk: ^4.1.0
    cli-cursor: ^3.1.0
    cli-spinners: ^2.5.0
    is-interactive: ^1.0.0
    is-unicode-supported: ^0.1.0
    log-symbols: ^4.1.0
    strip-ansi: ^6.0.0
    wcwidth: ^1.0.1
  checksum: 28d476ee6c1049d68368c0dc922e7225e3b5600c3ede88fade8052837f9ed342625fdaa84a6209302587c8ddd9b664f71f0759833cbdb3a4cf81344057e63c63
  languageName: node
  linkType: hard

"p-limit@npm:^2.0.0, p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-limit@npm:^3.0.2, p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: ^0.1.0
  checksum: 7c3690c4dbf62ef625671e20b7bdf1cbc9534e83352a2780f165b0d3ceba21907e77ad63401708145ca4e25bfc51636588d89a8c0aeb715e6c37d1c066430360
  languageName: node
  linkType: hard

"p-locate@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-locate@npm:3.0.0"
  dependencies:
    p-limit: ^2.0.0
  checksum: 83991734a9854a05fe9dbb29f707ea8a0599391f52daac32b86f08e21415e857ffa60f0e120bfe7ce0cc4faf9274a50239c7895fc0d0579d08411e513b83a4ae
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-locate@npm:^5.0.0":
  version: 5.0.0
  resolution: "p-locate@npm:5.0.0"
  dependencies:
    p-limit: ^3.0.2
  checksum: 1623088f36cf1cbca58e9b61c4e62bf0c60a07af5ae1ca99a720837356b5b6c5ba3eb1b2127e47a06865fee59dd0453cad7cc844cda9d5a62ac1a5a51b7c86d3
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.0
  resolution: "package-json-from-dist@npm:1.0.0"
  checksum: ac706ec856a5a03f5261e4e48fa974f24feb044d51f84f8332e2af0af04fbdbdd5bbbfb9cbbe354190409bc8307c83a9e38c6672c3c8855f709afb0006a009ea
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-json@npm:4.0.0"
  dependencies:
    error-ex: ^1.3.1
    json-parse-better-errors: ^1.0.1
  checksum: 0fe227d410a61090c247e34fa210552b834613c006c2c64d9a05cfe9e89cf8b4246d1246b1a99524b53b313e9ac024438d0680f67e33eaed7e6f38db64cfe7b5
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"path-dirname@npm:^1.0.2":
  version: 1.0.2
  resolution: "path-dirname@npm:1.0.2"
  checksum: 0d2f6604ae05a252a0025318685f290e2764ecf9c5436f203cdacfc8c0b17c24cdedaa449d766beb94ab88cc7fc70a09ec21e7933f31abc2b719180883e5e33f
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 96e92643aa34b4b28d0de1cd2eba52a1c5313a90c6542d03f62750d82480e20bfa62bc865d5cfc6165f5fcd5aeb0851043c40a39be5989646f223300021bae0a
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: ^10.2.0
    minipass: ^5.0.0 || ^6.0.2 || ^7.0.0
  checksum: 890d5abcd593a7912dcce7cf7c6bf7a0b5648e3dee6caf0712c126ca0a65c7f3d7b9d769072a4d1baf370f61ce493ab5b038d59988688e0c5f3f646ee3c69023
  languageName: node
  linkType: hard

"path-type@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-type@npm:4.0.0"
  checksum: 5b1e2daa247062061325b8fdbfd1fb56dde0a448fb1455453276ea18c60685bdad23a445dc148cf87bc216be1573357509b7d4060494a6fd768c7efad833ee45
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.0.1":
  version: 1.0.1
  resolution: "picocolors@npm:1.0.1"
  checksum: fa68166d1f56009fc02a34cdfd112b0dd3cf1ef57667ac57281f714065558c01828cdf4f18600ad6851cbe0093952ed0660b1e0156bddf2184b6aaf5817553a5
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 9c4e34278cb09987685fa5ef81499c82546c033713518f6441778fbec623fc708777fe8ac633097c72d88470d5963094076c7305cafc7ad340aae27cfacd856b
  languageName: node
  linkType: hard

"pirates@npm:^4.0.4, pirates@npm:^4.0.6":
  version: 4.0.6
  resolution: "pirates@npm:4.0.6"
  checksum: 46a65fefaf19c6f57460388a5af9ab81e3d7fd0e7bc44ca59d753cb5c4d0df97c6c6e583674869762101836d68675f027d60f841c105d72734df9dfca97cbcc6
  languageName: node
  linkType: hard

"pkg-dir@npm:^3.0.0":
  version: 3.0.0
  resolution: "pkg-dir@npm:3.0.0"
  dependencies:
    find-up: ^3.0.0
  checksum: 70c9476ffefc77552cc6b1880176b71ad70bfac4f367604b2b04efd19337309a4eec985e94823271c7c0e83946fa5aeb18cd360d15d10a5d7533e19344bfa808
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: 9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"plist@npm:^3.0.5":
  version: 3.1.0
  resolution: "plist@npm:3.1.0"
  dependencies:
    "@xmldom/xmldom": ^0.8.8
    base64-js: ^1.5.1
    xmlbuilder: ^15.1.1
  checksum: c8ea013da8646d4c50dff82f9be39488054621cc229957621bb00add42b5d4ce3657cf58d4b10c50f7dea1a81118f825838f838baeb4e6f17fab453ecf91d424
  languageName: node
  linkType: hard

"popmotion@npm:11.0.3":
  version: 11.0.3
  resolution: "popmotion@npm:11.0.3"
  dependencies:
    framesync: 6.0.1
    hey-listen: ^1.0.8
    style-value-types: 5.0.0
    tslib: ^2.1.0
  checksum: 9fe7d03b4ec0e85bfb9dadc23b745147bfe42e16f466ba06e6327197d0e38b72015afc2f918a8051dedc3680310417f346ffdc463be6518e2e92e98f48e30268
  languageName: node
  linkType: hard

"possible-typed-array-names@npm:^1.0.0":
  version: 1.0.0
  resolution: "possible-typed-array-names@npm:1.0.0"
  checksum: b32d403ece71e042385cc7856385cecf1cd8e144fa74d2f1de40d1e16035dba097bc189715925e79b67bdd1472796ff168d3a90d296356c9c94d272d5b95f3ae
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.2.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"prebuild-install@npm:^7.1.1":
  version: 7.1.2
  resolution: "prebuild-install@npm:7.1.2"
  dependencies:
    detect-libc: ^2.0.0
    expand-template: ^2.0.3
    github-from-package: 0.0.0
    minimist: ^1.2.3
    mkdirp-classic: ^0.5.3
    napi-build-utils: ^1.0.1
    node-abi: ^3.3.0
    pump: ^3.0.0
    rc: ^1.2.7
    simple-get: ^4.0.0
    tar-fs: ^2.0.0
    tunnel-agent: ^0.6.0
  bin:
    prebuild-install: bin.js
  checksum: 543dadf8c60e004ae9529e6013ca0cbeac8ef38b5f5ba5518cb0b622fe7f8758b34e4b5cb1a791db3cdc9d2281766302df6088bd1a225f206925d6fee17d6c5c
  languageName: node
  linkType: hard

"prelude-ls@npm:^1.2.1":
  version: 1.2.1
  resolution: "prelude-ls@npm:1.2.1"
  checksum: cd192ec0d0a8e4c6da3bb80e4f62afe336df3f76271ac6deb0e6a36187133b6073a19e9727a1ff108cd8b9982e4768850d413baa71214dd80c7979617dca827a
  languageName: node
  linkType: hard

"prettier-linter-helpers@npm:^1.0.0":
  version: 1.0.0
  resolution: "prettier-linter-helpers@npm:1.0.0"
  dependencies:
    fast-diff: ^1.1.2
  checksum: 00ce8011cf6430158d27f9c92cfea0a7699405633f7f1d4a45f07e21bf78e99895911cbcdc3853db3a824201a7c745bd49bfea8abd5fb9883e765a90f74f8392
  languageName: node
  linkType: hard

"prettier@npm:2.8.8":
  version: 2.8.8
  resolution: "prettier@npm:2.8.8"
  bin:
    prettier: bin-prettier.js
  checksum: b49e409431bf129dd89238d64299ba80717b57ff5a6d1c1a8b1a28b590d998a34e083fa13573bc732bb8d2305becb4c9a4407f8486c81fa7d55100eb08263cf8
  languageName: node
  linkType: hard

"prettier@npm:^3.1.1":
  version: 3.3.3
  resolution: "prettier@npm:3.3.3"
  bin:
    prettier: bin/prettier.cjs
  checksum: bc8604354805acfdde6106852d14b045bb20827ad76a5ffc2455b71a8257f94de93f17f14e463fe844808d2ccc87248364a5691488a3304f1031326e62d9276e
  languageName: node
  linkType: hard

"pretty-format@npm:^26.5.2, pretty-format@npm:^26.6.2":
  version: 26.6.2
  resolution: "pretty-format@npm:26.6.2"
  dependencies:
    "@jest/types": ^26.6.2
    ansi-regex: ^5.0.0
    ansi-styles: ^4.0.0
    react-is: ^17.0.1
  checksum: e3b808404d7e1519f0df1aa1f25cee0054ab475775c6b2b8c5568ff23194a92d54bf93274139b6f584ca70fd773be4eaa754b0e03f12bb0a8d1426b07f079976
  languageName: node
  linkType: hard

"pretty-format@npm:^29.7.0":
  version: 29.7.0
  resolution: "pretty-format@npm:29.7.0"
  dependencies:
    "@jest/schemas": ^29.6.3
    ansi-styles: ^5.0.0
    react-is: ^18.0.0
  checksum: 032c1602383e71e9c0c02a01bbd25d6759d60e9c7cf21937dde8357aa753da348fcec5def5d1002c9678a8524d5fe099ad98861286550ef44de8808cc61e43b6
  languageName: node
  linkType: hard

"proc-log@npm:^4.1.0, proc-log@npm:^4.2.0":
  version: 4.2.0
  resolution: "proc-log@npm:4.2.0"
  checksum: 98f6cd012d54b5334144c5255ecb941ee171744f45fca8b43b58ae5a0c1af07352475f481cadd9848e7f0250376ee584f6aa0951a856ff8f021bdfbff4eb33fc
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"promise@npm:^7.1.1":
  version: 7.3.1
  resolution: "promise@npm:7.3.1"
  dependencies:
    asap: ~2.0.3
  checksum: 475bb069130179fbd27ed2ab45f26d8862376a137a57314cf53310bdd85cc986a826fd585829be97ebc0aaf10e9d8e68be1bfe5a4a0364144b1f9eedfa940cf1
  languageName: node
  linkType: hard

"promise@npm:^8.3.0":
  version: 8.3.0
  resolution: "promise@npm:8.3.0"
  dependencies:
    asap: ~2.0.6
  checksum: a69f0ddbddf78ffc529cffee7ad950d307347615970564b17988ce43fbe767af5c738a9439660b24a9a8cbea106c0dcbb6c2b20e23b7e96a8e89e5c2679e94d5
  languageName: node
  linkType: hard

"prompts@npm:^2.0.1, prompts@npm:^2.4.2":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: ^3.0.3
    sisteransi: ^1.0.5
  checksum: d8fd1fe63820be2412c13bfc5d0a01909acc1f0367e32396962e737cb2fc52d004f3302475d5ce7d18a1e8a79985f93ff04ee03007d091029c3f9104bffc007d
  languageName: node
  linkType: hard

"prop-types@npm:^15.7.2, prop-types@npm:^15.8.1":
  version: 15.8.1
  resolution: "prop-types@npm:15.8.1"
  dependencies:
    loose-envify: ^1.4.0
    object-assign: ^4.1.1
    react-is: ^16.13.1
  checksum: c056d3f1c057cb7ff8344c645450e14f088a915d078dcda795041765047fa080d38e5d626560ccaac94a4e16e3aa15f3557c1a9a8d1174530955e992c675e459
  languageName: node
  linkType: hard

"protobufjs@npm:^7.2.5":
  version: 7.3.2
  resolution: "protobufjs@npm:7.3.2"
  dependencies:
    "@protobufjs/aspromise": ^1.1.2
    "@protobufjs/base64": ^1.1.2
    "@protobufjs/codegen": ^2.0.4
    "@protobufjs/eventemitter": ^1.1.0
    "@protobufjs/fetch": ^1.1.0
    "@protobufjs/float": ^1.0.2
    "@protobufjs/inquire": ^1.1.0
    "@protobufjs/path": ^1.1.2
    "@protobufjs/pool": ^1.1.0
    "@protobufjs/utf8": ^1.1.0
    "@types/node": ">=13.7.0"
    long: ^5.0.0
  checksum: cfb2a744787f26ee7c82f3e7c4b72cfc000e9bb4c07828ed78eb414db0ea97a340c0cc3264d0e88606592f847b12c0351411f10e9af255b7ba864eec44d7705f
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.0
  resolution: "pump@npm:3.0.0"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: e42e9229fba14732593a718b04cb5e1cfef8254544870997e0ecd9732b189a48e1256e4e5478148ecb47c8511dca2b09eae56b4d0aad8009e6fac8072923cfc9
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: bb0a0ceedca4c3c57a9b981b90601579058903c62be23c5e8e843d2c2d4148a3ecf029d5133486fb0e1822b098ba8bba09e89d6b21742d02fa26bda6441a6fb2
  languageName: node
  linkType: hard

"pure-rand@npm:^6.0.0":
  version: 6.1.0
  resolution: "pure-rand@npm:6.1.0"
  checksum: 8d53bc02bed99eca0b65b505090152ee7e9bd67dd74f8ff32ba1c883b87234067c5bf68d2614759fb217d82594d7a92919e6df80f97885e7b12b42af4bd3316a
  languageName: node
  linkType: hard

"query-string@npm:^7.1.3":
  version: 7.1.3
  resolution: "query-string@npm:7.1.3"
  dependencies:
    decode-uri-component: ^0.2.2
    filter-obj: ^1.1.0
    split-on-first: ^1.0.0
    strict-uri-encode: ^2.0.0
  checksum: 91af02dcd9cc9227a052841d5c2eecb80a0d6489d05625df506a097ef1c59037cfb5e907f39b84643cbfd535c955abec3e553d0130a7b510120c37d06e0f4346
  languageName: node
  linkType: hard

"querystring@npm:^0.2.1":
  version: 0.2.1
  resolution: "querystring@npm:0.2.1"
  checksum: 7b83b45d641e75fd39cd6625ddfd44e7618e741c61e95281b57bbae8fde0afcc12cf851924559e5cc1ef9baa3b1e06e22b164ea1397d65dd94b801f678d9c8ce
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: b676f8c040cdc5b12723ad2f91414d267605b26419d5c821ff03befa817ddd10e238d22b25d604920340fd73efd8ba795465a0377c4adf45a4a41e4234e42dc4
  languageName: node
  linkType: hard

"queue-tick@npm:^1.0.1":
  version: 1.0.1
  resolution: "queue-tick@npm:1.0.1"
  checksum: 57c3292814b297f87f792fbeb99ce982813e4e54d7a8bdff65cf53d5c084113913289d4a48ec8bbc964927a74b847554f9f4579df43c969a6c8e0f026457ad01
  languageName: node
  linkType: hard

"queue@npm:6.0.2":
  version: 6.0.2
  resolution: "queue@npm:6.0.2"
  dependencies:
    inherits: ~2.0.3
  checksum: ebc23639248e4fe40a789f713c20548e513e053b3dc4924b6cb0ad741e3f264dcff948225c8737834dd4f9ec286dbc06a1a7c13858ea382d9379f4303bcc0916
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 0a268d4fea508661cf5743dfe3d5f47ce214fd6b7dec1de0da4d669dd4ef3d2144468ebe4179049eff253d9d27e719c88dae55be64f954e80135a0cada804ec9
  languageName: node
  linkType: hard

"rc@npm:^1.2.7":
  version: 1.2.8
  resolution: "rc@npm:1.2.8"
  dependencies:
    deep-extend: ^0.6.0
    ini: ~1.3.0
    minimist: ^1.2.0
    strip-json-comments: ~2.0.1
  bin:
    rc: ./cli.js
  checksum: 2e26e052f8be2abd64e6d1dabfbd7be03f80ec18ccbc49562d31f617d0015fbdbcf0f9eed30346ea6ab789e0fdfe4337f033f8016efdbee0df5354751842080e
  languageName: node
  linkType: hard

"react-devtools-core@npm:^5.0.0":
  version: 5.3.1
  resolution: "react-devtools-core@npm:5.3.1"
  dependencies:
    shell-quote: ^1.6.1
    ws: ^7
  checksum: a68434a6af8261f5eb7defd823ebc77cc86f42a93521755bc58e5925956af579a312e109f9b27f652d016c2d580ef28f6e8d1643502624c0fe7913c93c743170
  languageName: node
  linkType: hard

"react-freeze@npm:^1.0.0":
  version: 1.0.4
  resolution: "react-freeze@npm:1.0.4"
  peerDependencies:
    react: ">=17.0.0"
  checksum: 6b4d93209dff04a1f25d9f8e0c56a9a8a80e7889e8e8267299f34449c7e41a9ab90cad24569d03dd7173b56b7496576dba68f71f1d4e5c8be72f0633023668bc
  languageName: node
  linkType: hard

"react-hook-form@npm:^7.51.5":
  version: 7.52.1
  resolution: "react-hook-form@npm:7.52.1"
  peerDependencies:
    react: ^16.8.0 || ^17 || ^18 || ^19
  checksum: 224fec214c5c7093b6949bc0a4fce3cf9b7a567a2f36dc3c7feeb1e721c5cccbd21f0f0ab19aa1f5f912014264f9c2224181370007609693b6c5ef6778f59ca5
  languageName: node
  linkType: hard

"react-is@npm:^16.12.0 || ^17.0.0 || ^18.0.0, react-is@npm:^18.0.0, react-is@npm:^18.2.0":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: e20fe84c86ff172fc8d898251b7cc2c43645d108bf96d0b8edf39b98f9a2cae97b40520ee7ed8ee0085ccc94736c4886294456033304151c3f94978cec03df21
  languageName: node
  linkType: hard

"react-is@npm:^16.13.0, react-is@npm:^16.13.1, react-is@npm:^16.7.0":
  version: 16.13.1
  resolution: "react-is@npm:16.13.1"
  checksum: f7a19ac3496de32ca9ae12aa030f00f14a3d45374f1ceca0af707c831b2a6098ef0d6bdae51bd437b0a306d7f01d4677fcc8de7c0d331eb47ad0f46130e53c5f
  languageName: node
  linkType: hard

"react-is@npm:^17.0.1":
  version: 17.0.2
  resolution: "react-is@npm:17.0.2"
  checksum: 9d6d111d8990dc98bc5402c1266a808b0459b5d54830bbea24c12d908b536df7883f268a7868cfaedde3dd9d4e0d574db456f84d2e6df9c4526f99bb4b5344d8
  languageName: node
  linkType: hard

"react-native-audio-recorder-player@npm:^3.6.11":
  version: 3.6.11
  resolution: "react-native-audio-recorder-player@npm:3.6.11"
  dependencies:
    dooboolab-welcome: ^1.3.2
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: d554ca87f85612cfdf4b1c8cb8438540766fa4e791c5bd8eb6815f9bda5af9cdd7006211f154a9ea6b2665095ab0c787241ec7c8794ecc7a33903a88cba977a0
  languageName: node
  linkType: hard

"react-native-biometrics@npm:^3.0.1":
  version: 3.0.1
  resolution: "react-native-biometrics@npm:3.0.1"
  peerDependencies:
    react-native: ">=0.60.0"
  checksum: 3864fea4bbf3dee1d360daf048e4f9980083c4e919e95082a5e4646e3b22475bb2b592cae18cccd7fb55b50498dca29e5346cf98d958646cbfc26fed5654bc53
  languageName: node
  linkType: hard

"react-native-bootsplash@npm:^5.5.3":
  version: 5.5.3
  resolution: "react-native-bootsplash@npm:5.5.3"
  dependencies:
    "@emotion/hash": ^0.9.1
    "@expo/config-plugins": ^7.8.4
    detect-indent: ^6.1.0
    node-html-parser: ^6.1.12
    picocolors: ^1.0.0
    prettier: ^3.1.1
    sharp: ^0.32.6
    ts-dedent: ^2.2.0
    xml-formatter: ^3.6.0
  peerDependencies:
    react: ">=18.1.0"
    react-native: ">=0.70.0"
  checksum: 2a5a81078c04d0bd16b5c7ac513b44bb3bbd2090dfc86ebe351324a6eef8d40fa5bc4cf3f0477a7b9b63d804f9a997851d3cb72b219fc506c89b9752cd0cf646
  languageName: node
  linkType: hard

"react-native-compressor@npm:^1.8.25":
  version: 1.8.25
  resolution: "react-native-compressor@npm:1.8.25"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: ff77edba14be2439f82d74e5a0304dc9f149cd492de356db67d6e33a39737f95bb040c78cb63609b81b43350017bf1c6199d3c0535bc3d8cd62861b1e6baf054
  languageName: node
  linkType: hard

"react-native-document-picker@npm:^9.3.0":
  version: 9.3.0
  resolution: "react-native-document-picker@npm:9.3.0"
  dependencies:
    invariant: ^2.2.4
  peerDependencies:
    react: "*"
    react-native: "*"
    react-native-windows: "*"
  peerDependenciesMeta:
    react-native-windows:
      optional: true
  checksum: 622c9bd3a17b7b92ff6b13571ccf2a916dd2477f2aa8c22b79e4b204156742ba95680dea912ee5ee56718f13c537107f5937e5bcf88d93cdf2142d508dc16351
  languageName: node
  linkType: hard

"react-native-dotenv@npm:^3.4.11":
  version: 3.4.11
  resolution: "react-native-dotenv@npm:3.4.11"
  dependencies:
    dotenv: ^16.4.5
  peerDependencies:
    "@babel/runtime": ^7.20.6
  checksum: 3ebac2c2ed79dd7e4920fd3fc2da9187413b7190231618e4858b46c47833677838b96d531afe7bd5c4b0a60454dba40cb8708722210df5d522e30aefbf41da05
  languageName: node
  linkType: hard

"react-native-emoji-selector@npm:^0.2.0":
  version: 0.2.0
  resolution: "react-native-emoji-selector@npm:0.2.0"
  dependencies:
    emoji-datasource: ^6.0.0
  checksum: 682dfa91a0d1d81e4af38994346c80713bd7075c1459633b3204d08d33227c59d3a93a995bd3a32a893e734429662795b8b3566471ad81b67e500508748e0129
  languageName: node
  linkType: hard

"react-native-fs@npm:^2.20.0":
  version: 2.20.0
  resolution: "react-native-fs@npm:2.20.0"
  dependencies:
    base-64: ^0.1.0
    utf8: ^3.0.0
  peerDependencies:
    react-native: "*"
    react-native-windows: "*"
  peerDependenciesMeta:
    react-native-windows:
      optional: true
  checksum: 0be9bb9a5c13b501d0a3006efc3aa5c0b5b211456ee04718297f4e522532f3527f1daa220bd67d3b82d819ed8fdab8f64b7d6e0d7b768c1fd1d8ec9122d94316
  languageName: node
  linkType: hard

"react-native-gesture-handler@npm:^2.16.2":
  version: 2.18.1
  resolution: "react-native-gesture-handler@npm:2.18.1"
  dependencies:
    "@egjs/hammerjs": ^2.0.17
    hoist-non-react-statics: ^3.3.0
    invariant: ^2.2.4
    prop-types: ^15.7.2
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 49b5b9a232fc60ddc38124a804410150070af5b20efbba1550831221560d71df704b7a7edfa2fb70d84fde32faf8675ad6118aea8b40f50c2a9d80eb7a08eb1c
  languageName: node
  linkType: hard

"react-native-incall-manager@npm:^4.2.0":
  version: 4.2.0
  resolution: "react-native-incall-manager@npm:4.2.0"
  peerDependencies:
    react-native: ">=0.40.0"
  checksum: 96a8d48f2fca4a56a04650be54efe48db21c52a1ef5dff7ccd05d529cef939123f0a1c54ec2e05597aaad173425a5ef992dfd715c7244759004606e944bf6f15
  languageName: node
  linkType: hard

"react-native-linear-gradient@npm:^2.8.3":
  version: 2.8.3
  resolution: "react-native-linear-gradient@npm:2.8.3"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: f980d324e551bbc475c6406bdc5250a08242020cbe653412fa169bbdf51e28a502e225de105b4d696d6a1a1b733d44782469020f4936d8b3ce0e2c78e51cf58f
  languageName: node
  linkType: hard

"react-native-mmkv@npm:^2.12.2":
  version: 2.12.2
  resolution: "react-native-mmkv@npm:2.12.2"
  peerDependencies:
    react: "*"
    react-native: ">=0.71.0"
  checksum: 8c79504a89fefd7f9c290a4a6bb24f1f9ef9e2a69d20d0f7d882b89f3943556e9c919c9d46583e44ab698f25213489277fccb6d4c856cce310267c5f907388f1
  languageName: node
  linkType: hard

"react-native-permissions@npm:^4.1.5":
  version: 4.1.5
  resolution: "react-native-permissions@npm:4.1.5"
  peerDependencies:
    react: ">=18.1.0"
    react-native: ">=0.70.0"
    react-native-windows: ">=0.70.0"
  peerDependenciesMeta:
    react-native-windows:
      optional: true
  checksum: dc4b366a0f561a47f032c027eb703a2be45e801788b915ed583e38f617eea23656eb63a77b17fb3a663fe3199b2ec25788f799e751421634e35a64f5258feed9
  languageName: node
  linkType: hard

"react-native-reanimated-carousel@npm:^3.5.1":
  version: 3.5.1
  resolution: "react-native-reanimated-carousel@npm:3.5.1"
  peerDependencies:
    react: ">=16.8.0"
    react-native: ">=0.6.0"
    react-native-gesture-handler: ">=2.0.0"
    react-native-reanimated: ">=3.0.0"
  checksum: a2eb640453fa35f659c1fb5575f903364d513fb7f621104afa14fee2284132674dfaf0d87cb29e8c119f7513094a5967d5193f3dfa24043af167c6f26abcfe73
  languageName: node
  linkType: hard

"react-native-reanimated@npm:^3.11.0":
  version: 3.14.0
  resolution: "react-native-reanimated@npm:3.14.0"
  dependencies:
    "@babel/plugin-transform-arrow-functions": ^7.0.0-0
    "@babel/plugin-transform-nullish-coalescing-operator": ^7.0.0-0
    "@babel/plugin-transform-optional-chaining": ^7.0.0-0
    "@babel/plugin-transform-shorthand-properties": ^7.0.0-0
    "@babel/plugin-transform-template-literals": ^7.0.0-0
    "@babel/preset-typescript": ^7.16.7
    convert-source-map: ^2.0.0
    invariant: ^2.2.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
    react: "*"
    react-native: "*"
  checksum: 31eb58d1c7fbd233ce789de2f63779f485f07708e0a4168866ab9dfce555e970079a84ca34eef9cfb06ee48cfa9e1b0e452407a7938c066393034c046642a855
  languageName: node
  linkType: hard

"react-native-responsive-screen@npm:^1.4.2":
  version: 1.4.2
  resolution: "react-native-responsive-screen@npm:1.4.2"
  peerDependencies:
    react-native: ">=0.35"
  checksum: f854d42e2fdfde80f31b9d3eab3bc9665cf312e5b3953b4a14dc36dc27b2dbe03b8520880ea267376eea7b664af7eb900ae88ef4004a65820b48ad9ac2fc5feb
  languageName: node
  linkType: hard

"react-native-safe-area-context@npm:^4.10.1":
  version: 4.10.8
  resolution: "react-native-safe-area-context@npm:4.10.8"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: eced388ae7cc712f75e43cba302b612c8fecceb8ec8b39cff21b6bc29debe2fdc24423f67609af244d919c3ed871dd1d36c6adc97a8960a938984d333490e653
  languageName: node
  linkType: hard

"react-native-screens@npm:^3.31.1":
  version: 3.33.0
  resolution: "react-native-screens@npm:3.33.0"
  dependencies:
    react-freeze: ^1.0.0
    warn-once: ^0.1.0
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 01b8ee9246a54b10298ecb4c269958d9b2b90c52f1e5151e310dbfe80017990c45ab1d18abcf3dacbc6d0458d1afa82bb52f2e645423ac0e42857460b15d32eb
  languageName: node
  linkType: hard

"react-native-sound@npm:^0.11.2":
  version: 0.11.2
  resolution: "react-native-sound@npm:0.11.2"
  peerDependencies:
    react-native: ">=0.8.0"
  checksum: d019de415c5f7bc8af51bc50adff7b9c307cb990aad3ea193933f89df743bbcdbfaa33c4a12bb1143a223137cea39f8b69baf5e1c29f77cf80476616bccc6f91
  languageName: node
  linkType: hard

"react-native-svg-transformer@npm:^1.4.0":
  version: 1.5.0
  resolution: "react-native-svg-transformer@npm:1.5.0"
  dependencies:
    "@svgr/core": ^8.1.0
    "@svgr/plugin-jsx": ^8.1.0
    "@svgr/plugin-svgo": ^8.1.0
    path-dirname: ^1.0.2
  peerDependencies:
    react-native: ">=0.59.0"
    react-native-svg: ">=12.0.0"
  checksum: 6c2544ef095b098de68c45a1698bc79acea10935391009c6322699e0df96a48953949b75309fc6fc7bba32ce5e8c7df632f817b116dd05456e26a5938108a8e6
  languageName: node
  linkType: hard

"react-native-svg@npm:^15.3.0":
  version: 15.4.0
  resolution: "react-native-svg@npm:15.4.0"
  dependencies:
    css-select: ^5.1.0
    css-tree: ^1.1.3
    warn-once: 0.1.1
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 04f5aafa88e5d641df998c195988904760984004ae3d5b16c3313941f2027d85b4d5f9c35ac9d696ee533ccff33f4f3c9b86249ca6fec787ca4895075ee4d9d0
  languageName: node
  linkType: hard

"react-native-video@npm:^6.4.3":
  version: 6.4.3
  resolution: "react-native-video@npm:6.4.3"
  peerDependencies:
    react: "*"
    react-native: "*"
  checksum: 94ae488505a74c247b847b4fc6afcbfaf0ec0b9adc2a1e19b8637e8c240d4c37e6584ebf368b9dcc6d308c27f6e4e06848181a9dd05ee3a71abb8f3ac786b1f4
  languageName: node
  linkType: hard

"react-native-vision-camera@npm:^4.5.0":
  version: 4.5.1
  resolution: "react-native-vision-camera@npm:4.5.1"
  peerDependencies:
    "@shopify/react-native-skia": "*"
    react: "*"
    react-native: "*"
    react-native-reanimated: "*"
    react-native-worklets-core: "*"
  peerDependenciesMeta:
    "@shopify/react-native-skia":
      optional: true
    react-native-reanimated:
      optional: true
    react-native-worklets-core:
      optional: true
  checksum: 8629652123de083b7d88be620687f366ad14d67b6a93136bf3d059498669cffad0d12052580c13ebe6c185d3001bb7c36d4fcf315a547ad7be11753f5f586d6e
  languageName: node
  linkType: hard

"react-native-web@npm:^0.19.12":
  version: 0.19.12
  resolution: "react-native-web@npm:0.19.12"
  dependencies:
    "@babel/runtime": ^7.18.6
    "@react-native/normalize-colors": ^0.74.1
    fbjs: ^3.0.4
    inline-style-prefixer: ^6.0.1
    memoize-one: ^6.0.0
    nullthrows: ^1.1.1
    postcss-value-parser: ^4.2.0
    styleq: ^0.1.3
  peerDependencies:
    react: ^18.0.0
    react-dom: ^18.0.0
  checksum: 676b1ba510c92e01dc69cb3102080f83976d2d209647323fb0a3a14113a455a6a506cf78d3e392c3fa33015135b61e2d6a3eed837a6876665064a6eb87516781
  languageName: node
  linkType: hard

"react-native-webrtc@npm:^124.0.4":
  version: 124.0.4
  resolution: "react-native-webrtc@npm:124.0.4"
  dependencies:
    base64-js: 1.5.1
    debug: 4.3.4
    event-target-shim: 6.0.2
  peerDependencies:
    react-native: ">=0.60.0"
  checksum: 9b6e9fb5c149b63bc7f7668ac7e20a756a4943df5de0757121c35c0d87a5fe04327c396ac2c95a56e731710392d60e8b8534a31880bdab68550179d6cbb964bc
  languageName: node
  linkType: hard

"react-native@npm:0.74.1":
  version: 0.74.1
  resolution: "react-native@npm:0.74.1"
  dependencies:
    "@jest/create-cache-key-function": ^29.6.3
    "@react-native-community/cli": 13.6.6
    "@react-native-community/cli-platform-android": 13.6.6
    "@react-native-community/cli-platform-ios": 13.6.6
    "@react-native/assets-registry": 0.74.83
    "@react-native/codegen": 0.74.83
    "@react-native/community-cli-plugin": 0.74.83
    "@react-native/gradle-plugin": 0.74.83
    "@react-native/js-polyfills": 0.74.83
    "@react-native/normalize-colors": 0.74.83
    "@react-native/virtualized-lists": 0.74.83
    abort-controller: ^3.0.0
    anser: ^1.4.9
    ansi-regex: ^5.0.0
    base64-js: ^1.5.1
    chalk: ^4.0.0
    event-target-shim: ^5.0.1
    flow-enums-runtime: ^0.0.6
    invariant: ^2.2.4
    jest-environment-node: ^29.6.3
    jsc-android: ^250231.0.0
    memoize-one: ^5.0.0
    metro-runtime: ^0.80.3
    metro-source-map: ^0.80.3
    mkdirp: ^0.5.1
    nullthrows: ^1.1.1
    pretty-format: ^26.5.2
    promise: ^8.3.0
    react-devtools-core: ^5.0.0
    react-refresh: ^0.14.0
    react-shallow-renderer: ^16.15.0
    regenerator-runtime: ^0.13.2
    scheduler: 0.24.0-canary-efb381bbf-20230505
    stacktrace-parser: ^0.1.10
    whatwg-fetch: ^3.0.0
    ws: ^6.2.2
    yargs: ^17.6.2
  peerDependencies:
    "@types/react": ^18.2.6
    react: 18.2.0
  peerDependenciesMeta:
    "@types/react":
      optional: true
  bin:
    react-native: cli.js
  checksum: f89c54da0b3f475bf1a90cfce62a062606ecdfa9974b264f2b69c823dd1e43c866e89d83d20d1af06018ce8301ca32a270c2f8eb06f6b3f3dcd816e9d0995cc3
  languageName: node
  linkType: hard

"react-refresh@npm:^0.14.0":
  version: 0.14.2
  resolution: "react-refresh@npm:0.14.2"
  checksum: d80db4bd40a36dab79010dc8aa317a5b931f960c0d83c4f3b81f0552cbcf7f29e115b84bb7908ec6a1eb67720fff7023084eff73ece8a7ddc694882478464382
  languageName: node
  linkType: hard

"react-shallow-renderer@npm:^16.15.0":
  version: 16.15.0
  resolution: "react-shallow-renderer@npm:16.15.0"
  dependencies:
    object-assign: ^4.1.1
    react-is: ^16.12.0 || ^17.0.0 || ^18.0.0
  peerDependencies:
    react: ^16.0.0 || ^17.0.0 || ^18.0.0
  checksum: 6052c7e3e9627485120ebd8257f128aad8f56386fe8d42374b7743eac1be457c33506d153c7886b4e32923c0c352d402ab805ef9ca02dbcd8393b2bdeb6e5af8
  languageName: node
  linkType: hard

"react-test-renderer@npm:18.2.0":
  version: 18.2.0
  resolution: "react-test-renderer@npm:18.2.0"
  dependencies:
    react-is: ^18.2.0
    react-shallow-renderer: ^16.15.0
    scheduler: ^0.23.0
  peerDependencies:
    react: ^18.2.0
  checksum: 6b6980ced93fa2b72662d5e4ab3b4896833586940047ce52ca9aca801e5432adf05fcbe28289b0af3ce6a2a7c590974e25dcc8aa43d0de658bfe8bbcd686f958
  languageName: node
  linkType: hard

"react@npm:18.2.0":
  version: 18.2.0
  resolution: "react@npm:18.2.0"
  dependencies:
    loose-envify: ^1.1.0
  checksum: 88e38092da8839b830cda6feef2e8505dec8ace60579e46aa5490fc3dc9bba0bd50336507dc166f43e3afc1c42939c09fe33b25fae889d6f402721dcd78fca1b
  languageName: node
  linkType: hard

"readable-stream@npm:^3.1.1, readable-stream@npm:^3.4.0":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: bdcbe6c22e846b6af075e32cf8f4751c2576238c5043169a1c221c92ee2878458a816a4ea33f4c67623c0b6827c8a400409bfb3cf0bf3381392d0b1dfb52ac8d
  languageName: node
  linkType: hard

"readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: 65645467038704f0c8aaf026a72fbb588a9e2ef7a75cd57a01702ee9db1c4a1e4b03aaad36861a6a0926546a74d174149c8c207527963e0c2d3eee2f37678a42
  languageName: node
  linkType: hard

"readline@npm:^1.3.0":
  version: 1.3.0
  resolution: "readline@npm:1.3.0"
  checksum: dfaf8e6ac20408ea00d650e95f7bb47f77c4c62dd12ed7fb51731ee84532a2f3675fcdc4cab4923dc1eef227520a2e082a093215190907758bea9f585b19438e
  languageName: node
  linkType: hard

"recast@npm:^0.21.0":
  version: 0.21.5
  resolution: "recast@npm:0.21.5"
  dependencies:
    ast-types: 0.15.2
    esprima: ~4.0.0
    source-map: ~0.6.1
    tslib: ^2.0.1
  checksum: 03cc7f57562238ba258d468be67bf7446ce7a707bc87a087891dad15afead46c36e9aaeedf2130e2ab5a465244a9c62bfd4127849761cf8f4085abe2f3e5f485
  languageName: node
  linkType: hard

"reflect.getprototypeof@npm:^1.0.4":
  version: 1.0.6
  resolution: "reflect.getprototypeof@npm:1.0.6"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.1
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.4
    globalthis: ^1.0.3
    which-builtin-type: ^1.1.3
  checksum: 88e9e65a7eaa0bf8e9a8bbf8ac07571363bc333ba8b6769ed5e013e0042ed7c385e97fae9049510b3b5fe4b42472d8f32de9ce8ce84902bc4297d4bbe3777dba
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.1.0":
  version: 10.1.1
  resolution: "regenerate-unicode-properties@npm:10.1.1"
  dependencies:
    regenerate: ^1.4.2
  checksum: b80958ef40f125275824c2c47d5081dfaefebd80bff26c76761e9236767c748a4a95a69c053fe29d2df881177f2ca85df4a71fe70a82360388b31159ef19adcf
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 3317a09b2f802da8db09aa276e469b57a6c0dd818347e05b8862959c6193408242f150db5de83c12c3fa99091ad95fb42a6db2c3329bfaa12a0ea4cbbeb30cb0
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.2":
  version: 0.13.11
  resolution: "regenerator-runtime@npm:0.13.11"
  checksum: 27481628d22a1c4e3ff551096a683b424242a216fee44685467307f14d58020af1e19660bf2e26064de946bad7eff28950eae9f8209d55723e2d9351e632bbb4
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.14.0":
  version: 0.14.1
  resolution: "regenerator-runtime@npm:0.14.1"
  checksum: 9f57c93277b5585d3c83b0cf76be47b473ae8c6d9142a46ce8b0291a04bb2cf902059f0f8445dcabb3fb7378e5fe4bb4ea1e008876343d42e46d3b484534ce38
  languageName: node
  linkType: hard

"regenerator-transform@npm:^0.15.2":
  version: 0.15.2
  resolution: "regenerator-transform@npm:0.15.2"
  dependencies:
    "@babel/runtime": ^7.8.4
  checksum: 20b6f9377d65954980fe044cfdd160de98df415b4bff38fbade67b3337efaf078308c4fed943067cd759827cc8cfeca9cb28ccda1f08333b85d6a2acbd022c27
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.5.2":
  version: 1.5.2
  resolution: "regexp.prototype.flags@npm:1.5.2"
  dependencies:
    call-bind: ^1.0.6
    define-properties: ^1.2.1
    es-errors: ^1.3.0
    set-function-name: ^2.0.1
  checksum: d7f333667d5c564e2d7a97c56c3075d64c722c9bb51b2b4df6822b2e8096d623a5e63088fb4c83df919b6951ef8113841de8b47de7224872fa6838bc5d8a7d64
  languageName: node
  linkType: hard

"regexpu-core@npm:^5.3.1":
  version: 5.3.2
  resolution: "regexpu-core@npm:5.3.2"
  dependencies:
    "@babel/regjsgen": ^0.8.0
    regenerate: ^1.4.2
    regenerate-unicode-properties: ^10.1.0
    regjsparser: ^0.9.1
    unicode-match-property-ecmascript: ^2.0.0
    unicode-match-property-value-ecmascript: ^2.1.0
  checksum: 95bb97088419f5396e07769b7de96f995f58137ad75fac5811fb5fe53737766dfff35d66a0ee66babb1eb55386ef981feaef392f9df6d671f3c124812ba24da2
  languageName: node
  linkType: hard

"regjsparser@npm:^0.9.1":
  version: 0.9.1
  resolution: "regjsparser@npm:0.9.1"
  dependencies:
    jsesc: ~0.5.0
  bin:
    regjsparser: bin/parser
  checksum: 5e1b76afe8f1d03c3beaf9e0d935dd467589c3625f6d65fb8ffa14f224d783a0fed4bf49c2c1b8211043ef92b6117313419edf055a098ed8342e340586741afc
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"require-main-filename@npm:^2.0.0":
  version: 2.0.0
  resolution: "require-main-filename@npm:2.0.0"
  checksum: e9e294695fea08b076457e9ddff854e81bffbe248ed34c1eec348b7abbd22a0d02e8d75506559e2265e96978f3c4720bd77a6dad84755de8162b357eb6c778c7
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: ^5.0.0
  checksum: 546e0816012d65778e580ad62b29e975a642989108d9a3c5beabfb2304192fa3c9f9146fbdfe213563c6ff51975ae41bac1d3c6e047dd9572c94863a057b4d81
  languageName: node
  linkType: hard

"resolve-from@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-from@npm:3.0.0"
  checksum: fff9819254d2d62b57f74e5c2ca9c0bdd425ca47287c4d801bc15f947533148d858229ded7793b0f59e61e49e782fffd6722048add12996e1bd4333c29669062
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 4ceeb9113e1b1372d0cd969f3468fa042daa1dd9527b1b6bb88acb6ab55d8b9cd65dbf18819f9f9ddf0db804990901dcdaade80a215e7b2c23daae38e64f5bdf
  languageName: node
  linkType: hard

"resolve.exports@npm:^2.0.0":
  version: 2.0.2
  resolution: "resolve.exports@npm:2.0.2"
  checksum: 1c7778ca1b86a94f8ab4055d196c7d87d1874b96df4d7c3e67bbf793140f0717fd506dcafd62785b079cd6086b9264424ad634fb904409764c3509c3df1653f2
  languageName: node
  linkType: hard

"resolve@npm:^1.14.2, resolve@npm:^1.20.0":
  version: 1.22.8
  resolution: "resolve@npm:1.22.8"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: f8a26958aa572c9b064562750b52131a37c29d072478ea32e129063e2da7f83e31f7f11e7087a18225a8561cfe8d2f0df9dbea7c9d331a897571c0a2527dbb4c
  languageName: node
  linkType: hard

"resolve@npm:^2.0.0-next.5":
  version: 2.0.0-next.5
  resolution: "resolve@npm:2.0.0-next.5"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: a73ac69a1c4bd34c56b213d91f5b17ce390688fdb4a1a96ed3025cc7e08e7bfb90b3a06fcce461780cb0b589c958afcb0080ab802c71c01a7ecc8c64feafc89f
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.14.2#~builtin<compat/resolve>, resolve@patch:resolve@^1.20.0#~builtin<compat/resolve>":
  version: 1.22.8
  resolution: "resolve@patch:resolve@npm%3A1.22.8#~builtin<compat/resolve>::version=1.22.8&hash=c3c19d"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 5479b7d431cacd5185f8db64bfcb7286ae5e31eb299f4c4f404ad8aa6098b77599563ac4257cb2c37a42f59dfc06a1bec2bcf283bb448f319e37f0feb9a09847
  languageName: node
  linkType: hard

"resolve@patch:resolve@^2.0.0-next.5#~builtin<compat/resolve>":
  version: 2.0.0-next.5
  resolution: "resolve@patch:resolve@npm%3A2.0.0-next.5#~builtin<compat/resolve>::version=2.0.0-next.5&hash=c3c19d"
  dependencies:
    is-core-module: ^2.13.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 064d09c1808d0c51b3d90b5d27e198e6d0c5dad0eb57065fd40803d6a20553e5398b07f76739d69cbabc12547058bec6b32106ea66622375fb0d7e8fca6a846c
  languageName: node
  linkType: hard

"restore-cursor@npm:^3.1.0":
  version: 3.1.0
  resolution: "restore-cursor@npm:3.1.0"
  dependencies:
    onetime: ^5.1.0
    signal-exit: ^3.0.2
  checksum: f877dd8741796b909f2a82454ec111afb84eb45890eb49ac947d87991379406b3b83ff9673a46012fca0d7844bb989f45cc5b788254cf1a39b6b5a9659de0630
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.0.4
  resolution: "reusify@npm:1.0.4"
  checksum: c3076ebcc22a6bc252cb0b9c77561795256c22b757f40c0d8110b1300723f15ec0fc8685e8d4ea6d7666f36c79ccc793b1939c748bf36f18f542744a4e379fcc
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"rimraf@npm:~2.6.2":
  version: 2.6.3
  resolution: "rimraf@npm:2.6.3"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: ./bin.js
  checksum: 3ea587b981a19016297edb96d1ffe48af7e6af69660e3b371dbfc73722a73a0b0e9be5c88089fbeeb866c389c1098e07f64929c7414290504b855f54f901ab10
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: ^1.2.2
  checksum: cb4f97ad25a75ebc11a8ef4e33bb962f8af8516bb2001082ceabd8902e15b98f4b84b4f8a9b222e5d57fc3bd1379c483886ed4619367a7680dad65316993021d
  languageName: node
  linkType: hard

"safe-array-concat@npm:^1.1.2":
  version: 1.1.2
  resolution: "safe-array-concat@npm:1.1.2"
  dependencies:
    call-bind: ^1.0.7
    get-intrinsic: ^1.2.4
    has-symbols: ^1.0.3
    isarray: ^2.0.5
  checksum: a3b259694754ddfb73ae0663829e396977b99ff21cbe8607f35a469655656da8e271753497e59da8a7575baa94d2e684bea3e10ddd74ba046c0c9b4418ffa0c4
  languageName: node
  linkType: hard

"safe-buffer@npm:5.1.2, safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-buffer@npm:>=5.1.0, safe-buffer@npm:^5.0.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-regex-test@npm:^1.0.3":
  version: 1.0.3
  resolution: "safe-regex-test@npm:1.0.3"
  dependencies:
    call-bind: ^1.0.6
    es-errors: ^1.3.0
    is-regex: ^1.1.4
  checksum: 6c7d392ff1ae7a3ae85273450ed02d1d131f1d2c76e177d6b03eb88e6df8fa062639070e7d311802c1615f351f18dc58f9454501c58e28d5ffd9b8f502ba6489
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sax@npm:>=0.6.0":
  version: 1.4.1
  resolution: "sax@npm:1.4.1"
  checksum: 3ad64df16b743f0f2eb7c38ced9692a6d924f1cd07bbe45c39576c2cf50de8290d9d04e7b2228f924c7d05fecc4ec5cf651423278e0c7b63d260c387ef3af84a
  languageName: node
  linkType: hard

"scheduler@npm:0.24.0-canary-efb381bbf-20230505":
  version: 0.24.0-canary-efb381bbf-20230505
  resolution: "scheduler@npm:0.24.0-canary-efb381bbf-20230505"
  dependencies:
    loose-envify: ^1.1.0
  checksum: 232149125c10f10193b1340ec4bbf14a8e6a845152790d6fd6f58207642db801abdb5a21227561a0a93871b98ba47539a6233b4e6155aae72d6db6db9f9f09b3
  languageName: node
  linkType: hard

"scheduler@npm:^0.23.0":
  version: 0.23.2
  resolution: "scheduler@npm:0.23.2"
  dependencies:
    loose-envify: ^1.1.0
  checksum: 3e82d1f419e240ef6219d794ff29c7ee415fbdc19e038f680a10c067108e06284f1847450a210b29bbaf97b9d8a97ced5f624c31c681248ac84c80d56ad5a2c4
  languageName: node
  linkType: hard

"selfsigned@npm:^2.4.1":
  version: 2.4.1
  resolution: "selfsigned@npm:2.4.1"
  dependencies:
    "@types/node-forge": ^1.3.0
    node-forge: ^1
  checksum: 38b91c56f1d7949c0b77f9bbe4545b19518475cae15e7d7f0043f87b1626710b011ce89879a88969651f650a19d213bb15b7d5b4c2877df9eeeff7ba8f8b9bfa
  languageName: node
  linkType: hard

"semver@npm:^5.6.0":
  version: 5.7.2
  resolution: "semver@npm:5.7.2"
  bin:
    semver: bin/semver
  checksum: fb4ab5e0dd1c22ce0c937ea390b4a822147a9c53dbd2a9a0132f12fe382902beef4fbf12cf51bb955248d8d15874ce8cd89532569756384f994309825f10b686
  languageName: node
  linkType: hard

"semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: ae47d06de28836adb9d3e25f22a92943477371292d9b665fb023fae278d345d508ca1958232af086d85e0155aee22e313e100971898bbb8d5d89b8b1d4054ca2
  languageName: node
  linkType: hard

"semver@npm:^7.3.5, semver@npm:^7.3.7, semver@npm:^7.5.2, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.6.0":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 4110ec5d015c9438f322257b1c51fe30276e5f766a3f64c09edd1d7ea7118ecbc3f379f3b69032bacf13116dc7abc4ad8ce0d7e2bd642e26b0d271b56b61a7d8
  languageName: node
  linkType: hard

"send@npm:0.18.0":
  version: 0.18.0
  resolution: "send@npm:0.18.0"
  dependencies:
    debug: 2.6.9
    depd: 2.0.0
    destroy: 1.2.0
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    fresh: 0.5.2
    http-errors: 2.0.0
    mime: 1.6.0
    ms: 2.1.3
    on-finished: 2.4.1
    range-parser: ~1.2.1
    statuses: 2.0.1
  checksum: 74fc07ebb58566b87b078ec63e5a3e41ecd987e4272ba67b7467e86c6ad51bc6b0b0154133b6d8b08a2ddda360464f71382f7ef864700f34844a76c8027817a8
  languageName: node
  linkType: hard

"serialize-error@npm:^2.1.0":
  version: 2.1.0
  resolution: "serialize-error@npm:2.1.0"
  checksum: 28464a6f65e6becd6e49fb782aff06573fdbf3d19f161a20228179842fed05c75a34110e54c3ee020b00240f9e11d8bee9b9fee5d04e0bc0bef1fdbf2baa297e
  languageName: node
  linkType: hard

"serve-static@npm:^1.13.1":
  version: 1.15.0
  resolution: "serve-static@npm:1.15.0"
  dependencies:
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    parseurl: ~1.3.3
    send: 0.18.0
  checksum: af57fc13be40d90a12562e98c0b7855cf6e8bd4c107fe9a45c212bf023058d54a1871b1c89511c3958f70626fff47faeb795f5d83f8cf88514dbaeb2b724464d
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"set-function-length@npm:^1.2.1":
  version: 1.2.2
  resolution: "set-function-length@npm:1.2.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    function-bind: ^1.1.2
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-property-descriptors: ^1.0.2
  checksum: a8248bdacdf84cb0fab4637774d9fb3c7a8e6089866d04c817583ff48e14149c87044ce683d7f50759a8c50fb87c7a7e173535b06169c87ef76f5fb276dfff72
  languageName: node
  linkType: hard

"set-function-name@npm:^2.0.1, set-function-name@npm:^2.0.2":
  version: 2.0.2
  resolution: "set-function-name@npm:2.0.2"
  dependencies:
    define-data-property: ^1.1.4
    es-errors: ^1.3.0
    functions-have-names: ^1.2.3
    has-property-descriptors: ^1.0.2
  checksum: d6229a71527fd0404399fc6227e0ff0652800362510822a291925c9d7b48a1ca1a468b11b281471c34cd5a2da0db4f5d7ff315a61d26655e77f6e971e6d0c80f
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: c9a6f2c5b51a2dabdc0247db9c46460152ffc62ee139f3157440bd48e7c59425093f42719ac1d7931f054f153e2d26cf37dfeb8da17a794a58198a2705e527fd
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"shallow-clone@npm:^1.0.0":
  version: 1.0.0
  resolution: "shallow-clone@npm:1.0.0"
  dependencies:
    is-extendable: ^0.1.1
    kind-of: ^5.0.0
    mixin-object: ^2.0.1
  checksum: d4fd93b82d9fdd135027510f4beb04479de96c50b5392a17b022cf0942d17d43bff92aa83dfc30ddf962c0ed015e7c686c2ce677b85760ca5a8db9ebd5f1d791
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: ^6.0.2
  checksum: 39b3dd9630a774aba288a680e7d2901f5c0eae7b8387fc5c8ea559918b29b3da144b7bdb990d7ccd9e11be05508ac9e459ce51d01fd65e583282f6ffafcba2e7
  languageName: node
  linkType: hard

"sharp@npm:^0.32.6":
  version: 0.32.6
  resolution: "sharp@npm:0.32.6"
  dependencies:
    color: ^4.2.3
    detect-libc: ^2.0.2
    node-addon-api: ^6.1.0
    node-gyp: latest
    prebuild-install: ^7.1.1
    semver: ^7.5.4
    simple-get: ^4.0.1
    tar-fs: ^3.0.4
    tunnel-agent: ^0.6.0
  checksum: 0cca1d16b1920800c0e22d27bc6305f4c67c9ebe44f67daceb30bf645ae39e7fb7dfbd7f5d6cd9f9eebfddd87ac3f7e2695f4eb906d19b7a775286238e6a29fc
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"shell-quote@npm:^1.6.1, shell-quote@npm:^1.7.3":
  version: 1.8.1
  resolution: "shell-quote@npm:1.8.1"
  checksum: 5f01201f4ef504d4c6a9d0d283fa17075f6770bfbe4c5850b074974c68062f37929ca61700d95ad2ac8822e14e8c4b990ca0e6e9272e64befd74ce5e19f0736b
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4, side-channel@npm:^1.0.6":
  version: 1.0.6
  resolution: "side-channel@npm:1.0.6"
  dependencies:
    call-bind: ^1.0.7
    es-errors: ^1.3.0
    get-intrinsic: ^1.2.4
    object-inspect: ^1.13.1
  checksum: bfc1afc1827d712271453e91b7cd3878ac0efd767495fd4e594c4c2afaa7963b7b510e249572bfd54b0527e66e4a12b61b80c061389e129755f34c493aad9b97
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.2, signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 64c757b498cb8629ffa5f75485340594d2f8189e9b08700e69199069c8e3070fb3e255f7ab873c05dc0b3cec412aea7402e10a5990cb6a050bd33ba062a6c549
  languageName: node
  linkType: hard

"simple-concat@npm:^1.0.0":
  version: 1.0.1
  resolution: "simple-concat@npm:1.0.1"
  checksum: 4d211042cc3d73a718c21ac6c4e7d7a0363e184be6a5ad25c8a1502e49df6d0a0253979e3d50dbdd3f60ef6c6c58d756b5d66ac1e05cda9cacd2e9fc59e3876a
  languageName: node
  linkType: hard

"simple-get@npm:^4.0.0, simple-get@npm:^4.0.1":
  version: 4.0.1
  resolution: "simple-get@npm:4.0.1"
  dependencies:
    decompress-response: ^6.0.0
    once: ^1.3.1
    simple-concat: ^1.0.0
  checksum: e4132fd27cf7af230d853fa45c1b8ce900cb430dd0a3c6d3829649fe4f2b26574c803698076c4006450efb0fad2ba8c5455fbb5755d4b0a5ec42d4f12b31d27e
  languageName: node
  linkType: hard

"simple-plist@npm:^1.1.0":
  version: 1.4.0
  resolution: "simple-plist@npm:1.4.0"
  dependencies:
    bplist-creator: 0.1.1
    bplist-parser: 0.3.2
    plist: ^3.0.5
  checksum: fa8086f6b781c289f1abad21306481dda4af6373b32a5d998a70e53c2b7218a1d21ebb5ae3e736baae704c21d311d3d39d01d0e6a2387eda01b4020b9ebd909e
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: ^0.3.1
  checksum: a7f3f2ab5c76c4472d5c578df892e857323e452d9f392e1b5cf74b74db66e6294a1e1b8b390b519fa1b96b5b613f2a37db6cffef52c3f1f8f3c5ea64eb2d54c0
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: aba6438f46d2bfcef94cf112c835ab395172c75f67453fe05c340c770d3c402363018ae1ab4172a1026a90c47eaccf3af7b6ff6fa749a680c2929bd7fa2b37a4
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 94a93fff615f25a999ad4b83c9d5e257a7280c90a32a7cb8b4a87996e4babf322e469c42b7f649fd5796edd8687652f3fb452a86dc97a816f01113183393f11c
  languageName: node
  linkType: hard

"slice-ansi@npm:^2.0.0":
  version: 2.1.0
  resolution: "slice-ansi@npm:2.1.0"
  dependencies:
    ansi-styles: ^3.2.0
    astral-regex: ^1.0.0
    is-fullwidth-code-point: ^2.0.0
  checksum: 4e82995aa59cef7eb03ef232d73c2239a15efa0ace87a01f3012ebb942e963fbb05d448ce7391efcd52ab9c32724164aba2086f5143e0445c969221dde3b6b1e
  languageName: node
  linkType: hard

"slugify@npm:^1.6.6":
  version: 1.6.6
  resolution: "slugify@npm:1.6.6"
  checksum: 04773c2d3b7aea8d2a61fa47cc7e5d29ce04e1a96cbaec409da57139df906acb3a449fac30b167d203212c806e73690abd4ff94fbad0a9a7b7ea109a2a638ae9
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"snake-case@npm:^3.0.4":
  version: 3.0.4
  resolution: "snake-case@npm:3.0.4"
  dependencies:
    dot-case: ^3.0.4
    tslib: ^2.0.3
  checksum: 0a7a79900bbb36f8aaa922cf111702a3647ac6165736d5dc96d3ef367efc50465cac70c53cd172c382b022dac72ec91710608e5393de71f76d7142e6fd80e8a3
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.4
  resolution: "socks-proxy-agent@npm:8.0.4"
  dependencies:
    agent-base: ^7.1.1
    debug: ^4.3.4
    socks: ^2.8.3
  checksum: b2ec5051d85fe49072f9a250c427e0e9571fd09d5db133819192d078fd291276e1f0f50f6dbc04329b207738b1071314cee8bdbb4b12e27de42dbcf1d4233c67
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.3
  resolution: "socks@npm:2.8.3"
  dependencies:
    ip-address: ^9.0.5
    smart-buffer: ^4.2.0
  checksum: 7a6b7f6eedf7482b9e4597d9a20e09505824208006ea8f2c49b71657427f3c137ca2ae662089baa73e1971c62322d535d9d0cf1c9235cf6f55e315c18203eadd
  languageName: node
  linkType: hard

"source-map-js@npm:^1.0.1":
  version: 1.2.0
  resolution: "source-map-js@npm:1.2.0"
  checksum: 791a43306d9223792e84293b00458bf102a8946e7188f3db0e4e22d8d530b5f80a4ce468eb5ec0bf585443ad55ebbd630bf379c98db0b1f317fd902500217f97
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.13":
  version: 0.5.13
  resolution: "source-map-support@npm:0.5.13"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 933550047b6c1a2328599a21d8b7666507427c0f5ef5eaadd56b5da0fd9505e239053c66fe181bf1df469a3b7af9d775778eee283cbb7ae16b902ddc09e93a97
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.16, source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.5.6":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 5dc2043b93d2f194142c7f38f74a24670cd7a0063acdaf4bf01d2964b402257ae843c2a8fa822ad5b71013b5fcafa55af7421383da919752f22ff488bc553f4d
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"source-map@npm:^0.7.3":
  version: 0.7.4
  resolution: "source-map@npm:0.7.4"
  checksum: 01cc5a74b1f0e1d626a58d36ad6898ea820567e87f18dfc9d24a9843a351aaa2ec09b87422589906d6ff1deed29693e176194dc88bcae7c9a852dc74b311dbf5
  languageName: node
  linkType: hard

"split-on-first@npm:^1.0.0":
  version: 1.1.0
  resolution: "split-on-first@npm:1.1.0"
  checksum: 16ff85b54ddcf17f9147210a4022529b343edbcbea4ce977c8f30e38408b8d6e0f25f92cd35b86a524d4797f455e29ab89eb8db787f3c10708e0b47ebf528d30
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: a3fdac7b49643875b70864a9d9b469d87a40dfeaf5d34d9d0c5b1cda5fd7d065531fcb43c76357d62254c57184a7b151954156563a4d6a747015cfb41021cad0
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"ssri@npm:^10.0.0":
  version: 10.0.6
  resolution: "ssri@npm:10.0.6"
  dependencies:
    minipass: ^7.0.3
  checksum: 4603d53a05bcd44188747d38f1cc43833b9951b5a1ee43ba50535bdfc5fe4a0897472dbe69837570a5417c3c073377ef4f8c1a272683b401857f72738ee57299
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: ^2.0.0
  checksum: 052bf4d25bbf5f78e06c1d5e67de2e088b06871fa04107ca8d3f0e9d9263326e2942c8bedee3545795fc77d787d443a538345eef74db2f8e35db3558c6f91ff7
  languageName: node
  linkType: hard

"stackframe@npm:^1.3.4":
  version: 1.3.4
  resolution: "stackframe@npm:1.3.4"
  checksum: bae1596873595c4610993fa84f86a3387d67586401c1816ea048c0196800c0646c4d2da98c2ee80557fd9eff05877efe33b91ba6cd052658ed96ddc85d19067d
  languageName: node
  linkType: hard

"stacktrace-parser@npm:^0.1.10":
  version: 0.1.10
  resolution: "stacktrace-parser@npm:0.1.10"
  dependencies:
    type-fest: ^0.7.1
  checksum: f4fbddfc09121d91e587b60de4beb4941108e967d71ad3a171812dc839b010ca374d064ad0a296295fed13acd103609d99a4224a25b4e67de13cae131f1901ee
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 18c7623fdb8f646fb213ca4051be4df7efb3484d4ab662937ca6fbef7ced9b9e12842709872eb3020cc3504b93bde88935c9f6417489627a7786f24f8031cbcb
  languageName: node
  linkType: hard

"statuses@npm:~1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: c469b9519de16a4bb19600205cffb39ee471a5f17b82589757ca7bd40a8d92ebb6ed9f98b5a540c5d302ccbc78f15dc03cc0280dd6e00df1335568a5d5758a5c
  languageName: node
  linkType: hard

"stream-buffers@npm:2.2.x":
  version: 2.2.0
  resolution: "stream-buffers@npm:2.2.0"
  checksum: 4587d9e8f050d689fb38b4295e73408401b16de8edecc12026c6f4ae92956705ecfd995ae3845d7fa3ebf19502d5754df9143d91447fd881d86e518f43882c1c
  languageName: node
  linkType: hard

"streamx@npm:^2.15.0, streamx@npm:^2.18.0":
  version: 2.18.0
  resolution: "streamx@npm:2.18.0"
  dependencies:
    bare-events: ^2.2.0
    fast-fifo: ^1.3.2
    queue-tick: ^1.0.1
    text-decoder: ^1.1.0
  dependenciesMeta:
    bare-events:
      optional: true
  checksum: 88193eb37ad194e18cf62a7d6392180a0565017d494e2c96ee09f1e7ff64c16cdf97059e39cab4b16972e812d08d744d1e3c5117f4213e8057c44ad3963f2461
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^2.0.0":
  version: 2.0.0
  resolution: "strict-uri-encode@npm:2.0.0"
  checksum: eaac4cf978b6fbd480f1092cab8b233c9b949bcabfc9b598dd79a758f7243c28765ef7639c876fa72940dac687181b35486ea01ff7df3e65ce3848c64822c581
  languageName: node
  linkType: hard

"string-length@npm:^4.0.1":
  version: 4.0.2
  resolution: "string-length@npm:4.0.2"
  dependencies:
    char-regex: ^1.0.2
    strip-ansi: ^6.0.0
  checksum: ce85533ef5113fcb7e522bcf9e62cb33871aa99b3729cec5595f4447f660b0cefd542ca6df4150c97a677d58b0cb727a3fe09ac1de94071d05526c73579bf505
  languageName: node
  linkType: hard

"string-natural-compare@npm:^3.0.1":
  version: 3.0.1
  resolution: "string-natural-compare@npm:3.0.1"
  checksum: 65910d9995074086e769a68728395effbba9b7186be5b4c16a7fad4f4ef50cae95ca16e3e9086e019cbb636ae8daac9c7b8fe91b5f21865c5c0f26e3c0725406
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: ^0.2.0
    emoji-regex: ^9.2.2
    strip-ansi: ^7.0.1
  checksum: 7369deaa29f21dda9a438686154b62c2c5f661f8dda60449088f9f980196f7908fc39fdd1803e3e01541970287cf5deae336798337e9319a7055af89dafa7193
  languageName: node
  linkType: hard

"string.prototype.matchall@npm:^4.0.11":
  version: 4.0.11
  resolution: "string.prototype.matchall@npm:4.0.11"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.2
    es-errors: ^1.3.0
    es-object-atoms: ^1.0.0
    get-intrinsic: ^1.2.4
    gopd: ^1.0.1
    has-symbols: ^1.0.3
    internal-slot: ^1.0.7
    regexp.prototype.flags: ^1.5.2
    set-function-name: ^2.0.2
    side-channel: ^1.0.6
  checksum: 6ac6566ed065c0c8489c91156078ca077db8ff64d683fda97ae652d00c52dfa5f39aaab0a710d8243031a857fd2c7c511e38b45524796764d25472d10d7075ae
  languageName: node
  linkType: hard

"string.prototype.repeat@npm:^1.0.0":
  version: 1.0.0
  resolution: "string.prototype.repeat@npm:1.0.0"
  dependencies:
    define-properties: ^1.1.3
    es-abstract: ^1.17.5
  checksum: 95dfc514ed7f328d80a066dabbfbbb1615c3e51490351085409db2eb7cbfed7ea29fdadaf277647fbf9f4a1e10e6dd9e95e78c0fd2c4e6bb6723ea6e59401004
  languageName: node
  linkType: hard

"string.prototype.trim@npm:^1.2.9":
  version: 1.2.9
  resolution: "string.prototype.trim@npm:1.2.9"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-abstract: ^1.23.0
    es-object-atoms: ^1.0.0
  checksum: ea2df6ec1e914c9d4e2dc856fa08228e8b1be59b59e50b17578c94a66a176888f417264bb763d4aac638ad3b3dad56e7a03d9317086a178078d131aa293ba193
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimend@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: cc3bd2de08d8968a28787deba9a3cb3f17ca5f9f770c91e7e8fa3e7d47f079bad70fadce16f05dda9f261788be2c6e84a942f618c3bed31e42abc5c1084f8dfd
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.8":
  version: 1.0.8
  resolution: "string.prototype.trimstart@npm:1.0.8"
  dependencies:
    call-bind: ^1.0.7
    define-properties: ^1.2.1
    es-object-atoms: ^1.0.0
  checksum: df1007a7f580a49d692375d996521dc14fd103acda7f3034b3c558a60b82beeed3a64fa91e494e164581793a8ab0ae2f59578a49896a7af6583c1f20472bce96
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-ansi@npm:^5.0.0, strip-ansi@npm:^5.2.0":
  version: 5.2.0
  resolution: "strip-ansi@npm:5.2.0"
  dependencies:
    ansi-regex: ^4.1.0
  checksum: bdb5f76ade97062bd88e7723aa019adbfacdcba42223b19ccb528ffb9fb0b89a5be442c663c4a3fb25268eaa3f6ea19c7c3fbae830bd1562d55adccae1fcec46
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: ^6.0.1
  checksum: 859c73fcf27869c22a4e4d8c6acfe690064659e84bef9458aa6d13719d09ca88dcfd40cbf31fd0be63518ea1a643fe070b4827d353e09533a5b0b9fd4553d64d
  languageName: node
  linkType: hard

"strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 9dbcfbaf503c57c06af15fe2c8176fb1bf3af5ff65003851a102749f875a6dbe0ab3b30115eccf6e805e9d756830d3e40ec508b62b3f1ddf3761a20ebe29d3f3
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 69412b5e25731e1938184b5d489c32e340605bb611d6140344abc3421b7f3c6f9984b21dff296dfcf056681b82caa3bb4cc996a965ce37bcfad663e92eae9c64
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 492f73e27268f9b1c122733f28ecb0e7e8d8a531a6662efbd08e22cccb3f9475e90a1b82cab06a392f6afae6d2de636f977e231296400d0ec5304ba70f166443
  languageName: node
  linkType: hard

"strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 1074ccb63270d32ca28edfb0a281c96b94dc679077828135141f27d52a5a398ef5e78bcf22809d23cadc2b81dfbe345eb5fd8699b385c8b1128907dec4a7d1e1
  languageName: node
  linkType: hard

"strnum@npm:^1.0.5":
  version: 1.0.5
  resolution: "strnum@npm:1.0.5"
  checksum: 651b2031db5da1bf4a77fdd2f116a8ac8055157c5420f5569f64879133825915ad461513e7202a16d7fec63c54fd822410d0962f8ca12385c4334891b9ae6dd2
  languageName: node
  linkType: hard

"style-value-types@npm:5.0.0":
  version: 5.0.0
  resolution: "style-value-types@npm:5.0.0"
  dependencies:
    hey-listen: ^1.0.8
    tslib: ^2.1.0
  checksum: 16d198302cd102edf9dba94e7752a2364c93b1eaa5cc7c32b42b28eef4af4ccb5149a3f16bc2a256adc02616a2404f4612bd15f3081c1e8ca06132cae78be6c0
  languageName: node
  linkType: hard

"styleq@npm:^0.1.3":
  version: 0.1.3
  resolution: "styleq@npm:0.1.3"
  checksum: 14a8d23abd914166a9b4bd04ed753bd91363f0e029ee4a94ec2c7dc37d3213fe01fceee22dc655288da3ae89f5dc01cec42d5e2b58478b0dea33bf5bdf509be1
  languageName: node
  linkType: hard

"sudo-prompt@npm:^9.0.0":
  version: 9.2.1
  resolution: "sudo-prompt@npm:9.2.1"
  checksum: 50a29eec2f264f2b78d891452a64112d839a30bffbff4ec065dba4af691a35b23cdb8f9107d413e25c1a9f1925644a19994c00602495cab033d53f585fdfd665
  languageName: node
  linkType: hard

"superstruct@npm:^0.6.2":
  version: 0.6.2
  resolution: "superstruct@npm:0.6.2"
  dependencies:
    clone-deep: ^2.0.1
    kind-of: ^6.0.1
  checksum: f95e4049becb928baa531fe56256a74b050a2e4da0bee09ccd0031e08fffd521defeb1d37881b93e6861fa1a7c17c48f3834a99af83cb3ad60c9e6079e840a65
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"svg-parser@npm:^2.0.4":
  version: 2.0.4
  resolution: "svg-parser@npm:2.0.4"
  checksum: b3de6653048212f2ae7afe4a423e04a76ec6d2d06e1bf7eacc618a7c5f7df7faa5105561c57b94579ec831fbbdbf5f190ba56a9205ff39ed13eabdf8ab086ddf
  languageName: node
  linkType: hard

"svgo@npm:^3.0.2":
  version: 3.3.2
  resolution: "svgo@npm:3.3.2"
  dependencies:
    "@trysound/sax": 0.2.0
    commander: ^7.2.0
    css-select: ^5.1.0
    css-tree: ^2.3.1
    css-what: ^6.1.0
    csso: ^5.0.5
    picocolors: ^1.0.0
  bin:
    svgo: ./bin/svgo
  checksum: a3f8aad597dec13ab24e679c4c218147048dc1414fe04e99447c5f42a6e077b33d712d306df84674b5253b98c9b84dfbfb41fdd08552443b04946e43d03e054e
  languageName: node
  linkType: hard

"synckit@npm:^0.9.1":
  version: 0.9.1
  resolution: "synckit@npm:0.9.1"
  dependencies:
    "@pkgr/core": ^0.1.0
    tslib: ^2.6.2
  checksum: 4042941a4d939675f1d7b01124b8405b6ac616f3e3f396d00e46c67f38d0d5b7f9a1de05bc7ceea4ce80d967b450cfa2460e5f6aca81f7cea8f1a28be9392985
  languageName: node
  linkType: hard

"tar-fs@npm:^2.0.0":
  version: 2.1.1
  resolution: "tar-fs@npm:2.1.1"
  dependencies:
    chownr: ^1.1.1
    mkdirp-classic: ^0.5.2
    pump: ^3.0.0
    tar-stream: ^2.1.4
  checksum: f5b9a70059f5b2969e65f037b4e4da2daf0fa762d3d232ffd96e819e3f94665dbbbe62f76f084f1acb4dbdcce16c6e4dac08d12ffc6d24b8d76720f4d9cf032d
  languageName: node
  linkType: hard

"tar-fs@npm:^3.0.4":
  version: 3.0.6
  resolution: "tar-fs@npm:3.0.6"
  dependencies:
    bare-fs: ^2.1.1
    bare-path: ^2.1.0
    pump: ^3.0.0
    tar-stream: ^3.1.5
  dependenciesMeta:
    bare-fs:
      optional: true
    bare-path:
      optional: true
  checksum: b4fa09c70f75caf05bf5cf87369cd2862f1ac5fb75c4ddf9d25d55999f7736a94b58ad679d384196cba837c5f5ff14086e060fafccef5474a16e2d3058ffa488
  languageName: node
  linkType: hard

"tar-stream@npm:^2.1.4":
  version: 2.2.0
  resolution: "tar-stream@npm:2.2.0"
  dependencies:
    bl: ^4.0.3
    end-of-stream: ^1.4.1
    fs-constants: ^1.0.0
    inherits: ^2.0.3
    readable-stream: ^3.1.1
  checksum: 699831a8b97666ef50021c767f84924cfee21c142c2eb0e79c63254e140e6408d6d55a065a2992548e72b06de39237ef2b802b99e3ece93ca3904a37622a66f3
  languageName: node
  linkType: hard

"tar-stream@npm:^3.1.5":
  version: 3.1.7
  resolution: "tar-stream@npm:3.1.7"
  dependencies:
    b4a: ^1.6.4
    fast-fifo: ^1.2.0
    streamx: ^2.15.0
  checksum: 6393a6c19082b17b8dcc8e7fd349352bb29b4b8bfe1075912b91b01743ba6bb4298f5ff0b499a3bbaf82121830e96a1a59d4f21a43c0df339e54b01789cb8cc6
  languageName: node
  linkType: hard

"tar@npm:^6.1.11, tar@npm:^6.2.1":
  version: 6.2.1
  resolution: "tar@npm:6.2.1"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^5.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: f1322768c9741a25356c11373bce918483f40fa9a25c69c59410c8a1247632487edef5fe76c5f12ac51a6356d2f1829e96d2bc34098668a2fc34d76050ac2b6c
  languageName: node
  linkType: hard

"temp-dir@npm:^2.0.0":
  version: 2.0.0
  resolution: "temp-dir@npm:2.0.0"
  checksum: cc4f0404bf8d6ae1a166e0e64f3f409b423f4d1274d8c02814a59a5529f07db6cd070a749664141b992b2c1af337fa9bb451a460a43bb9bcddc49f235d3115aa
  languageName: node
  linkType: hard

"temp@npm:^0.8.4":
  version: 0.8.4
  resolution: "temp@npm:0.8.4"
  dependencies:
    rimraf: ~2.6.2
  checksum: f35bed78565355dfdf95f730b7b489728bd6b7e35071bcc6497af7c827fb6c111fbe9063afc7b8cbc19522a072c278679f9a0ee81e684aa2c8617cc0f2e9c191
  languageName: node
  linkType: hard

"terser@npm:^5.15.0":
  version: 5.31.3
  resolution: "terser@npm:5.31.3"
  dependencies:
    "@jridgewell/source-map": ^0.3.3
    acorn: ^8.8.2
    commander: ^2.20.0
    source-map-support: ~0.5.20
  bin:
    terser: bin/terser
  checksum: cb4ccd5cb42c719272959dcae63d41e4696fb304123392943282caa6dfcdc49f94e7c48353af8bcd4fbc34457b240b7f843db7fec21bb2bdc18e01d4f45b035e
  languageName: node
  linkType: hard

"test-exclude@npm:^6.0.0":
  version: 6.0.0
  resolution: "test-exclude@npm:6.0.0"
  dependencies:
    "@istanbuljs/schema": ^0.1.2
    glob: ^7.1.4
    minimatch: ^3.0.4
  checksum: 3b34a3d77165a2cb82b34014b3aba93b1c4637a5011807557dc2f3da826c59975a5ccad765721c4648b39817e3472789f9b0fa98fc854c5c1c7a1e632aacdc28
  languageName: node
  linkType: hard

"text-decoder@npm:^1.1.0":
  version: 1.1.1
  resolution: "text-decoder@npm:1.1.1"
  dependencies:
    b4a: ^1.6.4
  checksum: 6e734c0ad1de0312e7517fd58066859586540e78741454aeb658a1e2b8bad304a600479cecf443ee3f3530505556434c20c0de193f92ea09cc21551898379cee
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: b6937a38c80c7f84d9c11dd75e49d5c44f71d95e810a3250bd1f1797fc7117c57698204adf676b71497acc205d769d65c16ae8fa10afad832ae1322630aef10a
  languageName: node
  linkType: hard

"throat@npm:^5.0.0":
  version: 5.0.0
  resolution: "throat@npm:5.0.0"
  checksum: 031ff7f4431618036c1dedd99c8aa82f5c33077320a8358ed829e84b320783781d1869fe58e8f76e948306803de966f5f7573766a437562c9f5c033297ad2fe2
  languageName: node
  linkType: hard

"through2@npm:^2.0.1":
  version: 2.0.5
  resolution: "through2@npm:2.0.5"
  dependencies:
    readable-stream: ~2.3.6
    xtend: ~4.0.1
  checksum: beb0f338aa2931e5660ec7bf3ad949e6d2e068c31f4737b9525e5201b824ac40cac6a337224856b56bd1ddd866334bbfb92a9f57cd6f66bc3f18d3d86fc0fe50
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: cd922d9b853c00fe414c5a774817be65b058d54a2d01ebb415840960406c669a0fc632f66df885e24cb022ec812739199ccbdb8d1164c3e513f85bfca5ab2873
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 726321c5eaf41b5002e17ffbd1fb7245999a073e8979085dacd47c4b4e8068ff5777142fc6726d6ca1fd2ff16921b48788b87225cbc57c72636f6efa8efbffe3
  languageName: node
  linkType: hard

"ts-api-utils@npm:^1.0.1, ts-api-utils@npm:^1.3.0":
  version: 1.3.0
  resolution: "ts-api-utils@npm:1.3.0"
  peerDependencies:
    typescript: ">=4.2.0"
  checksum: c746ddabfdffbf16cb0b0db32bb287236a19e583057f8649ee7c49995bb776e1d3ef384685181c11a1a480369e022ca97512cb08c517b2d2bd82c83754c97012
  languageName: node
  linkType: hard

"ts-dedent@npm:^2.2.0":
  version: 2.2.0
  resolution: "ts-dedent@npm:2.2.0"
  checksum: 93ed8f7878b6d5ed3c08d99b740010eede6bccfe64bce61c5a4da06a2c17d6ddbb80a8c49c2d15251de7594a4f93ffa21dd10e7be75ef66a4dc9951b4a94e2af
  languageName: node
  linkType: hard

"tslib@npm:^1.8.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"tslib@npm:^2.0.1, tslib@npm:^2.0.3, tslib@npm:^2.1.0, tslib@npm:^2.6.2":
  version: 2.6.3
  resolution: "tslib@npm:2.6.3"
  checksum: 74fce0e100f1ebd95b8995fbbd0e6c91bdd8f4c35c00d4da62e285a3363aaa534de40a80db30ecfd388ed7c313c42d930ee0eaf108e8114214b180eec3dbe6f5
  languageName: node
  linkType: hard

"tslib@npm:^2.3.1":
  version: 2.8.0
  resolution: "tslib@npm:2.8.0"
  checksum: de852ecd81adfdb4870927e250763345f07dc13fe7f395ce261424966bb122a0992ad844c3ec875c9e63e72afe2220a150712984e44dfd1a8a7e538a064e3d46
  languageName: node
  linkType: hard

"tsutils@npm:^3.21.0":
  version: 3.21.0
  resolution: "tsutils@npm:3.21.0"
  dependencies:
    tslib: ^1.8.1
  peerDependencies:
    typescript: ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"
  checksum: 1843f4c1b2e0f975e08c4c21caa4af4f7f65a12ac1b81b3b8489366826259323feb3fc7a243123453d2d1a02314205a7634e048d4a8009921da19f99755cdc48
  languageName: node
  linkType: hard

"tunnel-agent@npm:^0.6.0":
  version: 0.6.0
  resolution: "tunnel-agent@npm:0.6.0"
  dependencies:
    safe-buffer: ^5.0.1
  checksum: 05f6510358f8afc62a057b8b692f05d70c1782b70db86d6a1e0d5e28a32389e52fa6e7707b6c5ecccacc031462e4bc35af85ecfe4bbc341767917b7cf6965711
  languageName: node
  linkType: hard

"type-check@npm:^0.4.0, type-check@npm:~0.4.0":
  version: 0.4.0
  resolution: "type-check@npm:0.4.0"
  dependencies:
    prelude-ls: ^1.2.1
  checksum: ec688ebfc9c45d0c30412e41ca9c0cdbd704580eb3a9ccf07b9b576094d7b86a012baebc95681999dd38f4f444afd28504cb3a89f2ef16b31d4ab61a0739025a
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 62b5628bff67c0eb0b66afa371bd73e230399a8d2ad30d852716efcc4656a7516904570cd8631a49a3ce57c10225adf5d0cbdcb47f6b0255fe6557c453925a15
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: e6b32a3b3877f04339bae01c193b273c62ba7bfc9e325b8703c4ee1b32dc8fe4ef5dfa54bf78265e069f7667d058e360ae0f37be5af9f153b22382cd55a9afe0
  languageName: node
  linkType: hard

"type-fest@npm:^0.7.1":
  version: 0.7.1
  resolution: "type-fest@npm:0.7.1"
  checksum: 5b1b113529d59949d97b76977d545989ddc11b81bb0c766b6d2ccc65473cb4b4a5c7d24f5be2c2bb2de302a5d7a13c1732ea1d34c8c59b7e0ec1f890cf7fc424
  languageName: node
  linkType: hard

"typed-array-buffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "typed-array-buffer@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.7
    es-errors: ^1.3.0
    is-typed-array: ^1.1.13
  checksum: 02ffc185d29c6df07968272b15d5319a1610817916ec8d4cd670ded5d1efe72901541ff2202fcc622730d8a549c76e198a2f74e312eabbfb712ed907d45cbb0b
  languageName: node
  linkType: hard

"typed-array-byte-length@npm:^1.0.1":
  version: 1.0.1
  resolution: "typed-array-byte-length@npm:1.0.1"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-proto: ^1.0.3
    is-typed-array: ^1.1.13
  checksum: f65e5ecd1cf76b1a2d0d6f631f3ea3cdb5e08da106c6703ffe687d583e49954d570cc80434816d3746e18be889ffe53c58bf3e538081ea4077c26a41055b216d
  languageName: node
  linkType: hard

"typed-array-byte-offset@npm:^1.0.2":
  version: 1.0.2
  resolution: "typed-array-byte-offset@npm:1.0.2"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-proto: ^1.0.3
    is-typed-array: ^1.1.13
  checksum: c8645c8794a621a0adcc142e0e2c57b1823bbfa4d590ad2c76b266aa3823895cf7afb9a893bf6685e18454ab1b0241e1a8d885a2d1340948efa4b56add4b5f67
  languageName: node
  linkType: hard

"typed-array-length@npm:^1.0.6":
  version: 1.0.6
  resolution: "typed-array-length@npm:1.0.6"
  dependencies:
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-proto: ^1.0.3
    is-typed-array: ^1.1.13
    possible-typed-array-names: ^1.0.0
  checksum: f0315e5b8f0168c29d390ff410ad13e4d511c78e6006df4a104576844812ee447fcc32daab1f3a76c9ef4f64eff808e134528b5b2439de335586b392e9750e5c
  languageName: node
  linkType: hard

"typescript-eslint@npm:^7.13.1":
  version: 7.18.0
  resolution: "typescript-eslint@npm:7.18.0"
  dependencies:
    "@typescript-eslint/eslint-plugin": 7.18.0
    "@typescript-eslint/parser": 7.18.0
    "@typescript-eslint/utils": 7.18.0
  peerDependencies:
    eslint: ^8.56.0
  peerDependenciesMeta:
    typescript:
      optional: true
  checksum: 68f263821c593d77cd607940a1a411edea6dcc528a0f5047be402c4a8cd612e8c7642b5c41ee6cb89c884ad83676658f7adb9ea688e550415938c84701d8ac93
  languageName: node
  linkType: hard

"typescript@npm:5.0.4":
  version: 5.0.4
  resolution: "typescript@npm:5.0.4"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 82b94da3f4604a8946da585f7d6c3025fff8410779e5bde2855ab130d05e4fd08938b9e593b6ebed165bda6ad9292b230984f10952cf82f0a0ca07bbeaa08172
  languageName: node
  linkType: hard

"typescript@patch:typescript@5.0.4#~builtin<compat/typescript>":
  version: 5.0.4
  resolution: "typescript@patch:typescript@npm%3A5.0.4#~builtin<compat/typescript>::version=5.0.4&hash=b5f058"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: d26b6ba97b6d163c55dbdffd9bbb4c211667ebebc743accfeb2c8c0154aace7afd097b51165a72a5bad2cf65a4612259344ff60f8e642362aa1695c760d303ac
  languageName: node
  linkType: hard

"ua-parser-js@npm:^1.0.35":
  version: 1.0.38
  resolution: "ua-parser-js@npm:1.0.38"
  checksum: d0772b22b027338d806ab17d1ac2896ee7485bdf9217c526028159f3cd6bb10272bb18f6196d2f94dde83e3b36dc9d2533daf08a414764f6f4f1844842383838
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "unbox-primitive@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
    has-bigints: ^1.0.2
    has-symbols: ^1.0.3
    which-boxed-primitive: ^1.0.2
  checksum: b7a1cf5862b5e4b5deb091672ffa579aa274f648410009c81cca63fed3b62b610c4f3b773f912ce545bb4e31edc3138975b5bc777fc6e4817dca51affb6380e9
  languageName: node
  linkType: hard

"undici-types@npm:~5.26.4":
  version: 5.26.5
  resolution: "undici-types@npm:5.26.5"
  checksum: 3192ef6f3fd5df652f2dc1cd782b49d6ff14dc98e5dced492aa8a8c65425227da5da6aafe22523c67f035a272c599bb89cfe803c1db6311e44bed3042fc25487
  languageName: node
  linkType: hard

"undici-types@npm:~6.11.1":
  version: 6.11.1
  resolution: "undici-types@npm:6.11.1"
  checksum: d7fc11bded93bc55ef3b88464e856ab061a747cf50ef2eff5df5ba3be18b9fcafe60e1b36a8c99e28aac2eade12891d32a504f2a32422452c44662e598e3b188
  languageName: node
  linkType: hard

"undici-types@npm:~6.13.0":
  version: 6.13.0
  resolution: "undici-types@npm:6.13.0"
  checksum: 9d0ef6bf58994bebbea6a4ab75f381c69a89a7ed151bfbae0d4ef95450d56502c9eccb323abf17b7d099c1d9c1cbae62e909e4dfeb8d204612d2f1fdada24707
  languageName: node
  linkType: hard

"undici@npm:5.28.4":
  version: 5.28.4
  resolution: "undici@npm:5.28.4"
  dependencies:
    "@fastify/busboy": ^2.0.0
  checksum: a8193132d84540e4dc1895ecc8dbaa176e8a49d26084d6fbe48a292e28397cd19ec5d13bc13e604484e76f94f6e334b2bdc740d5f06a6e50c44072818d0c19f9
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.0"
  checksum: 39be078afd014c14dcd957a7a46a60061bc37c4508ba146517f85f60361acf4c7539552645ece25de840e17e293baa5556268d091ca6762747fdd0c705001a45
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: ^2.0.0
    unicode-property-aliases-ecmascript: ^2.0.0
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.1.0":
  version: 2.1.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.1.0"
  checksum: 8d6f5f586b9ce1ed0e84a37df6b42fdba1317a05b5df0c249962bd5da89528771e2d149837cad11aa26bcb84c35355cb9f58a10c3d41fa3b899181ece6c85220
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.1.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.1.0"
  checksum: 243524431893649b62cc674d877bd64ef292d6071dd2fd01ab4d5ad26efbc104ffcd064f93f8a06b7e4ec54c172bf03f6417921a0d8c3a9994161fe1f88f815b
  languageName: node
  linkType: hard

"unique-filename@npm:^3.0.0":
  version: 3.0.0
  resolution: "unique-filename@npm:3.0.0"
  dependencies:
    unique-slug: ^4.0.0
  checksum: 8e2f59b356cb2e54aab14ff98a51ac6c45781d15ceaab6d4f1c2228b780193dc70fae4463ce9e1df4479cb9d3304d7c2043a3fb905bdeca71cc7e8ce27e063df
  languageName: node
  linkType: hard

"unique-slug@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-slug@npm:4.0.0"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 0884b58365af59f89739e6f71e3feacb5b1b41f2df2d842d0757933620e6de08eff347d27e9d499b43c40476cbaf7988638d3acb2ffbcb9d35fd035591adfd15
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 40cdc60f6e61070fe658ca36016a8f4ec216b29bf04a55dce14e3710cc84c7448538ef4dad3728d0bfe29975ccd7bfb5f414c45e7b78883567fb31b246f02dff
  languageName: node
  linkType: hard

"unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.0":
  version: 1.1.0
  resolution: "update-browserslist-db@npm:1.1.0"
  dependencies:
    escalade: ^3.1.2
    picocolors: ^1.0.1
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 7b74694d96f0c360f01b702e72353dc5a49df4fe6663d3ee4e5c628f061576cddf56af35a3a886238c01dd3d8f231b7a86a8ceaa31e7a9220ae31c1c1238e562
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"uri-scheme@npm:^1.3.1":
  version: 1.3.1
  resolution: "uri-scheme@npm:1.3.1"
  bin:
    uri-scheme: cli.js
  checksum: 934925999c31ae0034f5d8dad61b92d5ffbe38dc502579571c23cd6efb20a7d170f060567f24d1a13663d67c2f06e8489cca338cbf4821cec6a7dcb5c2aee7c9
  languageName: node
  linkType: hard

"use-latest-callback@npm:^0.2.1":
  version: 0.2.1
  resolution: "use-latest-callback@npm:0.2.1"
  peerDependencies:
    react: ">=16.8"
  checksum: da5718eda625738cc7dac8fb502d0f8f2039435eb71203565a72c32e0f5769e7b8ddac074e650066636e7f4b29b45524f751cb18a2b430856d98879bbb10d274
  languageName: node
  linkType: hard

"use-sync-external-store@npm:1.2.0":
  version: 1.2.0
  resolution: "use-sync-external-store@npm:1.2.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0
  checksum: 5c639e0f8da3521d605f59ce5be9e094ca772bd44a4ce7322b055a6f58eeed8dda3c94cabd90c7a41fb6fa852210092008afe48f7038792fd47501f33299116a
  languageName: node
  linkType: hard

"utf8@npm:^3.0.0":
  version: 3.0.0
  resolution: "utf8@npm:3.0.0"
  checksum: cb89a69ad9ab393e3eae9b25305b3ff08bebca9adc839191a34f90777eb2942f86a96369d2839925fea58f8f722f7e27031d697f10f5f39690f8c5047303e62d
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: c81095493225ecfc28add49c106ca4f09cdf56bc66731aa8dabc2edbbccb1e1bfe2de6a115e5c6a380d3ea166d1636410b62ef216bb07b3feb1cfde1d95d5080
  languageName: node
  linkType: hard

"uuid@npm:^7.0.3":
  version: 7.0.3
  resolution: "uuid@npm:7.0.3"
  bin:
    uuid: dist/bin/uuid
  checksum: f5b7b5cc28accac68d5c083fd51cca64896639ebd4cca88c6cfb363801aaa83aa439c86dfc8446ea250a7a98d17afd2ad9e88d9d4958c79a412eccb93bae29de
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^9.0.1":
  version: 9.3.0
  resolution: "v8-to-istanbul@npm:9.3.0"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.12
    "@types/istanbul-lib-coverage": ^2.0.1
    convert-source-map: ^2.0.0
  checksum: ded42cd535d92b7fd09a71c4c67fb067487ef5551cc227bfbf2a1f159a842e4e4acddaef20b955789b8d3b455b9779d036853f4a27ce15007f6364a4d30317ae
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: ae0123222c6df65b437669d63dfa8c36cee20a504101b2fcd97b8bf76f91259c17f9f2b4d70a1e3c6bbcee7f51b28392833adb6b2770b23b01abec84e369660b
  languageName: node
  linkType: hard

"vlq@npm:^1.0.0":
  version: 1.0.1
  resolution: "vlq@npm:1.0.1"
  checksum: 67ab6dd35c787eaa02c0ff1a869dd07a230db08722fb6014adaaf432634808ddb070765f70958b47997e438c331790cfcf20902411b0d6453f1a2a5923522f55
  languageName: node
  linkType: hard

"walker@npm:^1.0.7, walker@npm:^1.0.8":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: 1.0.12
  checksum: ad7a257ea1e662e57ef2e018f97b3c02a7240ad5093c392186ce0bcf1f1a60bbadd520d073b9beb921ed99f64f065efb63dfc8eec689a80e569f93c1c5d5e16c
  languageName: node
  linkType: hard

"warn-once@npm:0.1.1, warn-once@npm:^0.1.0":
  version: 0.1.1
  resolution: "warn-once@npm:0.1.1"
  checksum: e6a5a1f5a8dba7744399743d3cfb571db4c3947897875d4962a7c5b1bf2195ab4518c838cb4cea652e71729f21bba2e98dc75686f5fccde0fabbd894e2ed0c0d
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: ^1.0.3
  checksum: 814e9d1ddcc9798f7377ffa448a5a3892232b9275ebb30a41b529607691c0491de47cba426e917a4d08ded3ee7e9ba2f3fe32e62ee3cd9c7d3bafb7754bd553c
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: c92a0a6ab95314bde9c32e1d0a6dfac83b578f8fa5f21e675bc2706ed6981bc26b7eb7e6a1fab158e5ce4adf9caa4a0aee49a52505d4d13c7be545f15021b17c
  languageName: node
  linkType: hard

"websocket-driver@npm:>=0.5.1":
  version: 0.7.4
  resolution: "websocket-driver@npm:0.7.4"
  dependencies:
    http-parser-js: ">=0.5.1"
    safe-buffer: ">=5.1.0"
    websocket-extensions: ">=0.1.1"
  checksum: fffe5a33fe8eceafd21d2a065661d09e38b93877eae1de6ab5d7d2734c6ed243973beae10ae48c6613cfd675f200e5a058d1e3531bc9e6c5d4f1396ff1f0bfb9
  languageName: node
  linkType: hard

"websocket-extensions@npm:>=0.1.1":
  version: 0.1.4
  resolution: "websocket-extensions@npm:0.1.4"
  checksum: 5976835e68a86afcd64c7a9762ed85f2f27d48c488c707e67ba85e717b90fa066b98ab33c744d64255c9622d349eedecf728e65a5f921da71b58d0e9591b9038
  languageName: node
  linkType: hard

"whatwg-fetch@npm:^3.0.0":
  version: 3.6.20
  resolution: "whatwg-fetch@npm:3.6.20"
  checksum: c58851ea2c4efe5c2235f13450f426824cf0253c1d45da28f45900290ae602a20aff2ab43346f16ec58917d5562e159cd691efa368354b2e82918c2146a519c5
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: ~0.0.3
    webidl-conversions: ^3.0.0
  checksum: b8daed4ad3356cc4899048a15b2c143a9aed0dfae1f611ebd55073310c7b910f522ad75d727346ad64203d7e6c79ef25eafd465f4d12775ca44b90fa82ed9e2c
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-boxed-primitive@npm:1.0.2"
  dependencies:
    is-bigint: ^1.0.1
    is-boolean-object: ^1.1.0
    is-number-object: ^1.0.4
    is-string: ^1.0.5
    is-symbol: ^1.0.3
  checksum: 53ce774c7379071729533922adcca47220228405e1895f26673bbd71bdf7fb09bee38c1d6399395927c6289476b5ae0629863427fd151491b71c4b6cb04f3a5e
  languageName: node
  linkType: hard

"which-builtin-type@npm:^1.1.3":
  version: 1.1.4
  resolution: "which-builtin-type@npm:1.1.4"
  dependencies:
    function.prototype.name: ^1.1.6
    has-tostringtag: ^1.0.2
    is-async-function: ^2.0.0
    is-date-object: ^1.0.5
    is-finalizationregistry: ^1.0.2
    is-generator-function: ^1.0.10
    is-regex: ^1.1.4
    is-weakref: ^1.0.2
    isarray: ^2.0.5
    which-boxed-primitive: ^1.0.2
    which-collection: ^1.0.2
    which-typed-array: ^1.1.15
  checksum: 1f413025250072534de2a2ee25139a24d477512b532b05c85fb9aa05aef04c6e1ca8e2668acf971b777e602721dbdec4b9d6a4f37c6b9ff8f026ad030352707f
  languageName: node
  linkType: hard

"which-collection@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-collection@npm:1.0.2"
  dependencies:
    is-map: ^2.0.3
    is-set: ^2.0.3
    is-weakmap: ^2.0.2
    is-weakset: ^2.0.3
  checksum: c51821a331624c8197916598a738fc5aeb9a857f1e00d89f5e4c03dc7c60b4032822b8ec5696d28268bb83326456a8b8216344fb84270d18ff1d7628051879d9
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.1
  resolution: "which-module@npm:2.0.1"
  checksum: 1967b7ce17a2485544a4fdd9063599f0f773959cca24176dbe8f405e55472d748b7c549cd7920ff6abb8f1ab7db0b0f1b36de1a21c57a8ff741f4f1e792c52be
  languageName: node
  linkType: hard

"which-typed-array@npm:^1.1.14, which-typed-array@npm:^1.1.15":
  version: 1.1.15
  resolution: "which-typed-array@npm:1.1.15"
  dependencies:
    available-typed-arrays: ^1.0.7
    call-bind: ^1.0.7
    for-each: ^0.3.3
    gopd: ^1.0.1
    has-tostringtag: ^1.0.2
  checksum: 65227dcbfadf5677aacc43ec84356d17b5500cb8b8753059bb4397de5cd0c2de681d24e1a7bd575633f976a95f88233abfd6549c2105ef4ebd58af8aa1807c75
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"which@npm:^4.0.0":
  version: 4.0.0
  resolution: "which@npm:4.0.0"
  dependencies:
    isexe: ^3.1.1
  bin:
    node-which: bin/which.js
  checksum: f17e84c042592c21e23c8195108cff18c64050b9efb8459589116999ea9da6dd1509e6a1bac3aeebefd137be00fabbb61b5c2bc0aa0f8526f32b58ee2f545651
  languageName: node
  linkType: hard

"word-wrap@npm:^1.2.5":
  version: 1.2.5
  resolution: "word-wrap@npm:1.2.5"
  checksum: f93ba3586fc181f94afdaff3a6fef27920b4b6d9eaefed0f428f8e07adea2a7f54a5f2830ce59406c8416f033f86902b91eb824072354645eea687dff3691ccb
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: a790b846fd4505de962ba728a21aaeda189b8ee1c7568ca5e817d85930e06ef8d1689d49dbf0e881e8ef84436af3a88bc49115c2e2788d841ff1b8b5b51a608b
  languageName: node
  linkType: hard

"wrap-ansi@npm:^6.2.0":
  version: 6.2.0
  resolution: "wrap-ansi@npm:6.2.0"
  dependencies:
    ansi-styles: ^4.0.0
    string-width: ^4.1.0
    strip-ansi: ^6.0.0
  checksum: 6cd96a410161ff617b63581a08376f0cb9162375adeb7956e10c8cd397821f7eb2a6de24eb22a0b28401300bf228c86e50617cd568209b5f6775b93c97d2fe3a
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: ^6.1.0
    string-width: ^5.0.1
    strip-ansi: ^7.0.1
  checksum: 371733296dc2d616900ce15a0049dca0ef67597d6394c57347ba334393599e800bab03c41d4d45221b6bc967b8c453ec3ae4749eff3894202d16800fdfe0e238
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"write-file-atomic@npm:^2.3.0":
  version: 2.4.3
  resolution: "write-file-atomic@npm:2.4.3"
  dependencies:
    graceful-fs: ^4.1.11
    imurmurhash: ^0.1.4
    signal-exit: ^3.0.2
  checksum: 2db81f92ae974fd87ab4a5e7932feacaca626679a7c98fcc73ad8fcea5a1950eab32fa831f79e9391ac99b562ca091ad49be37a79045bd65f595efbb8f4596ae
  languageName: node
  linkType: hard

"write-file-atomic@npm:^4.0.2":
  version: 4.0.2
  resolution: "write-file-atomic@npm:4.0.2"
  dependencies:
    imurmurhash: ^0.1.4
    signal-exit: ^3.0.7
  checksum: 5da60bd4eeeb935eec97ead3df6e28e5917a6bd317478e4a85a5285e8480b8ed96032bbcc6ecd07b236142a24f3ca871c924ec4a6575e623ec1b11bf8c1c253c
  languageName: node
  linkType: hard

"ws@npm:^6.2.2":
  version: 6.2.3
  resolution: "ws@npm:6.2.3"
  dependencies:
    async-limiter: ~1.0.0
  checksum: bbc96ff5628832d80669a88fd117487bf070492dfaa50df77fa442a2b119792e772f4365521e0a8e025c0d51173c54fa91adab165c11b8e0674685fdd36844a5
  languageName: node
  linkType: hard

"ws@npm:^7, ws@npm:^7.5.1":
  version: 7.5.10
  resolution: "ws@npm:7.5.10"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ^5.0.2
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: f9bb062abf54cc8f02d94ca86dcd349c3945d63851f5d07a3a61c2fcb755b15a88e943a63cf580cbdb5b74436d67ef6b67f745b8f7c0814e411379138e1863cb
  languageName: node
  linkType: hard

"xcode@npm:^3.0.1":
  version: 3.0.1
  resolution: "xcode@npm:3.0.1"
  dependencies:
    simple-plist: ^1.1.0
    uuid: ^7.0.3
  checksum: 908ff85851f81aec6e36ca24427db092e1cc068f052716e14de5e762196858039efabbe053a1abe8920184622501049e74a93618e8692b982f7604a9847db108
  languageName: node
  linkType: hard

"xml-formatter@npm:^3.6.0":
  version: 3.6.3
  resolution: "xml-formatter@npm:3.6.3"
  dependencies:
    xml-parser-xo: ^4.1.2
  checksum: e4c6927d30de9e82f69ea1752d93b9e31e5eb308c2261e039272a04cdf1120a03cf01aea5cbb01c4197a1dee9dc67b04bf91a24acd6a1abe52b4fcb7ac843086
  languageName: node
  linkType: hard

"xml-parser-xo@npm:^4.1.2":
  version: 4.1.2
  resolution: "xml-parser-xo@npm:4.1.2"
  checksum: 732aaeb54e01bf21036a7f711147dceb693a2042b657ffd98166abb4c124946b3e41f2d3e5f3571398cedde7fbd99b346821c4d44d5fed5f86bac506c8a06a2a
  languageName: node
  linkType: hard

"xml2js@npm:0.6.0":
  version: 0.6.0
  resolution: "xml2js@npm:0.6.0"
  dependencies:
    sax: ">=0.6.0"
    xmlbuilder: ~11.0.0
  checksum: 437f353fd66d367bf158e9555a0625df9965d944e499728a5c6bc92a54a2763179b144f14b7e1c725040f56bbd22b0fa6cfcb09ec4faf39c45ce01efe631f40b
  languageName: node
  linkType: hard

"xmlbuilder@npm:^14.0.0":
  version: 14.0.0
  resolution: "xmlbuilder@npm:14.0.0"
  checksum: 9e93d3c73957dbb21acde63afa5d241b19057bdbdca9d53534d8351e70f1d5c9db154e3ca19bd3e9ea84c082539ab6e7845591c8778a663e8b5d3470d5427a8b
  languageName: node
  linkType: hard

"xmlbuilder@npm:^15.1.1":
  version: 15.1.1
  resolution: "xmlbuilder@npm:15.1.1"
  checksum: 14f7302402e28d1f32823583d121594a9dca36408d40320b33f598bd589ca5163a352d076489c9c64d2dc1da19a790926a07bf4191275330d4de2b0d85bb1843
  languageName: node
  linkType: hard

"xmlbuilder@npm:~11.0.0":
  version: 11.0.1
  resolution: "xmlbuilder@npm:11.0.1"
  checksum: 7152695e16f1a9976658215abab27e55d08b1b97bca901d58b048d2b6e106b5af31efccbdecf9b07af37c8377d8e7e821b494af10b3a68b0ff4ae60331b415b0
  languageName: node
  linkType: hard

"xtend@npm:~4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"y18n@npm:^4.0.0":
  version: 4.0.3
  resolution: "y18n@npm:4.0.3"
  checksum: 014dfcd9b5f4105c3bb397c1c8c6429a9df004aa560964fb36732bfb999bfe83d45ae40aeda5b55d21b1ee53d8291580a32a756a443e064317953f08025b1aa4
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 54f0fb95621ee60898a38c572c515659e51cc9d9f787fb109cef6fde4befbe1c4602dc999d30110feee37456ad0f1660fa2edcfde6a9a740f86a290999550d30
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yaml@npm:^2.2.1":
  version: 2.5.0
  resolution: "yaml@npm:2.5.0"
  bin:
    yaml: bin.mjs
  checksum: a116dca5c61641d9bf1f1016c6e71daeb1ed4915f5930ed237d45ab7a605aa5d92c332ff64879a6cd088cabede008c778774e3060ffeb4cd617d28088e4b2d83
  languageName: node
  linkType: hard

"yargs-parser@npm:^18.1.2":
  version: 18.1.3
  resolution: "yargs-parser@npm:18.1.3"
  dependencies:
    camelcase: ^5.0.0
    decamelize: ^1.2.0
  checksum: 60e8c7d1b85814594d3719300ecad4e6ae3796748b0926137bfec1f3042581b8646d67e83c6fc80a692ef08b8390f21ddcacb9464476c39bbdf52e34961dd4d9
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: ed2d96a616a9e3e1cc7d204c62ecc61f7aaab633dcbfab2c6df50f7f87b393993fe6640d017759fe112d0cb1e0119f2b4150a87305cc873fd90831c6a58ccf1c
  languageName: node
  linkType: hard

"yargs@npm:^15.1.0":
  version: 15.4.1
  resolution: "yargs@npm:15.4.1"
  dependencies:
    cliui: ^6.0.0
    decamelize: ^1.2.0
    find-up: ^4.1.0
    get-caller-file: ^2.0.1
    require-directory: ^2.1.1
    require-main-filename: ^2.0.0
    set-blocking: ^2.0.0
    string-width: ^4.2.0
    which-module: ^2.0.0
    y18n: ^4.0.0
    yargs-parser: ^18.1.2
  checksum: 40b974f508d8aed28598087720e086ecd32a5fd3e945e95ea4457da04ee9bdb8bdd17fd91acff36dc5b7f0595a735929c514c40c402416bbb87c03f6fb782373
  languageName: node
  linkType: hard

"yargs@npm:^17.3.1, yargs@npm:^17.6.2, yargs@npm:^17.7.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: ^8.0.1
    escalade: ^3.1.1
    get-caller-file: ^2.0.5
    require-directory: ^2.1.1
    string-width: ^4.2.3
    y18n: ^5.0.5
    yargs-parser: ^21.1.1
  checksum: 73b572e863aa4a8cbef323dd911d79d193b772defd5a51aab0aca2d446655216f5002c42c5306033968193bdbf892a7a4c110b0d77954a7fdf563e653967b56a
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: f77b3d8d00310def622123df93d4ee654fc6a0096182af8bd60679ddcdfb3474c56c6c7190817c84a2785648cdee9d721c0154eb45698c62176c322fb46fc700
  languageName: node
  linkType: hard

"zod@npm:^3.23.8":
  version: 3.23.8
  resolution: "zod@npm:3.23.8"
  checksum: 15949ff82118f59c893dacd9d3c766d02b6fa2e71cf474d5aa888570c469dbf5446ac5ad562bb035bf7ac9650da94f290655c194f4a6de3e766f43febd432c5c
  languageName: node
  linkType: hard

"zustand@npm:^4.5.2":
  version: 4.5.4
  resolution: "zustand@npm:4.5.4"
  dependencies:
    use-sync-external-store: 1.2.0
  peerDependencies:
    "@types/react": ">=16.8"
    immer: ">=9.0.6"
    react: ">=16.8"
  peerDependenciesMeta:
    "@types/react":
      optional: true
    immer:
      optional: true
    react:
      optional: true
  checksum: 8e824aea8b5232f9a719c6d33e016272a0dae8c69a0980a3e2aefbcf2a89379b343af1f2dace340f45f788ec8d8a55a56d037c53498b3b05b91e985d26cae047
  languageName: node
  linkType: hard
