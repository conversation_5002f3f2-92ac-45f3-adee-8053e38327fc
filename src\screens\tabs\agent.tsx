import React, {useState} from 'react';
import {
  BodyCard,
  Dropdown,
  Fab,
  Header,
  MassageCard,
  Spacer,
} from '../../components';
import {FlatList} from 'react-native';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {getAgentChatList, getAgentList} from '../../utils/apis';
import {AgentList, ChatList, RootStack} from '../../utils';
import {useStore} from '../../utils/hooks';
import {
  convertIntoLocalTime,
  findAgentName,
  findUserAvatar,
  findUserRole,
  sortRecentMessage,
} from '../../utils/helpers';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {AddContact} from '../../assets/icons';

const keyExtractor = (item: ChatList) => item.id;

export default function Agent() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {adminPhone, userId} = useStore(state => ({
    adminPhone: state.user?.phone,
    userId: state.user?.id,
  }));

  const [agents, setAgents] = React.useState<AgentList[]>();
  const [chatParticipants, setChatParticipants] = useState<ChatList[]>();
  const [selectedAgentPhone, setSelectedAgentPhone] = useState<string>();
  async function handleSelect(item: AgentList) {
    setSelectedAgentPhone(item?.phone);
    const response = await getAgentChatList(item?.phone);
    const data = await response.json();
    setChatParticipants(sortRecentMessage(data));
  }

  function handleThreeDots() {
    navigation.navigate('settings');
  }

  function navigateToNewAgent() {
    navigation.navigate('newAgent');
  }

  function navigateToChat(
    id: string,
    img: string | null,
    name: string,
    lastSeen?: string,
    remoteUserId?: string,
    agentPhone?: string,
  ) {
    navigation.navigate('chats', {
      id,
      img,
      name,
      chatType: 'user',
      isSuperAdmin: true,
      agentPhone,
    });
  }
  const renderItem = React.useCallback(
    ({item}: {item: ChatList}) => {
      return (
        <MassageCard
          loading={false}
          id={item?.id}
          img={findUserAvatar(item?.participants, userId) ?? null}
          name={findAgentName(item?.participants, selectedAgentPhone)}
          lastSeen={convertIntoLocalTime(item?.messages[0]?.createdAt)}
          lastMsg={item?.messages[0]?.message}
          massageCount={item?.unread_count}
          agent={findUserRole(item?.participants, userId)}
          massageStatus="delivered"
          onPress={navigateToChat}
          bio={'bio'}
          agentPhone={item?.name}
        />
      );
    },
    [selectedAgentPhone],
  );
  useFocusEffect(
    React.useCallback(() => {
      getAgentList()
        .then(res => res.json())
        .then(data => {
          const filtered = data?.filter(
            (item: AgentList) => item.phone !== adminPhone,
          );
          setAgents(filtered);
        });
    }, []),
  );
  return (
    <BodyCard padBottom padTop>
      <Header title="N2 Chat" showLogo onPressThreeDots={handleThreeDots} />

      {agents && (
        <Dropdown
          data={agents}
          onSelect={handleSelect}
          placeholder="select agent number"
          label="phone"
          lable2="name"
          value="id"
        />
      )}
      <Spacer height={12} />
      {chatParticipants && (
        <FlatList
          ItemSeparatorComponent={() => <Spacer height={12} />}
          data={chatParticipants}
          keyExtractor={keyExtractor}
          renderItem={renderItem}
        />
      )}
      <Fab onPress={navigateToNewAgent} icon={<AddContact />} />
    </BodyCard>
  );
}
