import {View, Alert} from 'react-native';
import React from 'react';
import {
  BodyCard,
  Cta,
  Input,
  KeyboardAvoidingView,
  Row,
  Spacer,
  Text,
} from '../../components';
import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {z} from 'zod';
import {NewContactSchema} from '../../utils';
import {saveNewContact} from '../../utils/apis';
import {IndiaFlag, User, Phone} from '../../assets/icons';
import {useNavigation} from '@react-navigation/native';
import {useStore} from '../../utils/hooks';
import {showTostMessage} from '../../utils/helpers';

type FormData = z.infer<typeof NewContactSchema>;
const startView = (
  <Row>
    <IndiaFlag />
    <Spacer width={8} />
    <Text variant="subText1_500">+91</Text>
  </Row>
);
export default function NewContact() {
  const {
    control,
    handleSubmit,
    formState: {isValid},
    setValue,
    setError,
  } = useForm<FormData>({
    resolver: zodResolver(NewContactSchema),
  });
  const {goBack} = useNavigation();

  const {myNumber} = useStore(state => ({myNumber: state?.user?.phone}));

  async function onSubmit({name, phone}: FormData) {
    try {
      if (myNumber === phone) {
        setValue('name', '');
        setValue('phone', '');
        return showTostMessage("Can't save own number");
      }
      const response = await saveNewContact(name, phone);

      if (response.status === 200 && response.ok) {
        showTostMessage('Contact saved successfully');
        goBack();
      } else if (response.status === 400 && !response.ok) {
        const {error} = await response.json();
        setError('phone', {message: error});
        showTostMessage(error + '');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong!');
    }
  }
  return (
    <BodyCard padTop padBottom>
      <KeyboardAvoidingView>
        <View>
          <Input
            labelIcon={<User />}
            control={control}
            name="name"
            label="Name"
            placeholder="John"
          />
          <Input
            control={control}
            labelIcon={<Phone />}
            name="phone"
            label="Phone number"
            placeholder="9999 999 999"
            maxLength={10}
            keyboardType="number-pad"
            startView={startView}
          />
        </View>
      </KeyboardAvoidingView>
      <Cta
        title="Save Contact"
        onPress={handleSubmit(onSubmit)}
        disabled={!isValid}
      />
    </BodyCard>
  );
}
