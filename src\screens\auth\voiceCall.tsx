import {Pressable, StyleSheet, View} from 'react-native';
import React, {useEffect} from 'react';
import {BackBtn, BodyCard, Image, Row, Spacer, Text} from '../../components';
import {colors} from '../../design/colors';
import {Dots, Massage, Mute, Phone, Speaker} from '../../assets/icons';
import {WebRTCContext} from '../../utils/providers/WebRtcProvider';
import {WebSocketContext} from '../../utils/providers/WebSocket';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {RootStack} from '../../utils';

type VoiceCall = RouteProp<RootStack, 'voiceCall'>;

export default function VoiceCall() {
  const {
    startCall,
    endCall,
    declineCall,
    answerCall,
    handleLog,
    toggleSpeaker,
    toggleAudio,
    isLoudspeaker,
    isMuted,
    timer,
    isCallEnded,
  } = React.useContext(WebRTCContext);
  const {currentOpenChat, remoteUserId} = React.useContext(WebSocketContext);
  const navigation = useNavigation();
  const {
    params: {isIncomingCall, userName, avatar},
  } = useRoute<VoiceCall>();

  const [incomingCall, setIncomingCall] = React.useState(isIncomingCall);

  function callAccepted() {
    answerCall();
    setIncomingCall(false);
  }
  function navigateToChat() {
    navigation.goBack();
  }

  useEffect(() => {
    if (currentOpenChat && !isIncomingCall) {
      startCall(currentOpenChat.id, 'audio', remoteUserId);
    }
  }, []);

  useEffect(() => {
    if (isCallEnded) {
      navigation.goBack();
    }
  }, [isCallEnded, navigation]);

  return (
    <>
      {incomingCall && timer === 0 ? (
        <BodyCard padTop padBottom>
          <Row center>
            <Pressable>
              <BackBtn />
            </Pressable>
            <View style={styles.flex}>
              <Text center variant="H2_500">
                {userName || 'Unknown Caller'}
              </Text>
              <Text center>{'Incoming Call...'}</Text>
            </View>
          </Row>
          <Row style={styles.flex} middle>
            <Image alt={''} uri={avatar || null} style={styles.image} />
          </Row>
          <Row between center>
            <View>
              <Pressable style={styles.denied} onPress={declineCall}>
                <Phone
                  height={16}
                  width={16}
                  fill={colors.icon.white}
                  style={styles.phone}
                />
              </Pressable>
              <Spacer height={12} />
              <Text center variant="subText2">
                Decline
              </Text>
            </View>
            <View>
              <Pressable style={styles.accept} onPress={callAccepted}>
                <Phone height={34} width={34} fill={colors.icon.white} />
              </Pressable>
              <Spacer height={12} />
              <Text variant="subText2" center>
                Accept
              </Text>
            </View>
            <View>
              <Pressable
                onPress={navigateToChat}
                style={[styles.denied, {backgroundColor: colors.icon.B200}]}>
                <Massage height={16} width={16} fill={colors.icon.white} />
              </Pressable>
              <Spacer height={12} />
              <Text variant="subText2" center>
                Message
              </Text>
            </View>
          </Row>
        </BodyCard>
      ) : (
        <BodyCard padTop padBottom>
          <Row center middle>
            {/* <View style={styles.minimize}>
            <Minimize fill={colors.icon.white} />
          </View> */}
            <View>
              <Text variant="H2_500">{userName || 'Unknown Caller'}</Text>
              <Text center>
                {timer > 0
                  ? `${Math.floor(timer / 60)
                      .toString()
                      .padStart(2, '0')}:${(timer % 60)
                      .toString()
                      .padStart(2, '0')}`
                  : 'Ringing...'}
              </Text>
            </View>
            {/* <View style={styles.minimize}>
            <Minimize fill={colors.icon.white} />
          </View> */}
          </Row>
          <Row style={styles.flex} middle>
            <Image alt={''} uri={avatar || null} style={styles.image} />
          </Row>

          <View
            style={{
              backgroundColor: colors.primaryB50,
              padding: 8,
              borderRadius: 16,
              gap: 12,
              width: 280,
              flexDirection: 'row',
              alignSelf: 'center',
              justifyContent: 'space-around',
            }}>
            <Pressable style={styles.pressable} onPress={handleLog}>
              <Dots height={24} width={24} fill={colors.icon.white} />
            </Pressable>
            <Pressable
              style={[
                styles.pressable,
                {
                  backgroundColor: isLoudspeaker
                    ? colors.icon.B200
                    : colors.icon.B10,
                },
              ]}
              onPress={toggleSpeaker}>
              <Speaker
                fill={isLoudspeaker ? colors.icon.white : colors.icon.B50}
              />
            </Pressable>
            <Pressable
              onPress={toggleAudio}
              style={[
                styles.pressable,
                {backgroundColor: isMuted ? colors.icon.B200 : colors.textB10},
              ]}>
              <Mute fill={isMuted ? colors.icon.PB50 : colors.icon.B50} />
            </Pressable>
            <Pressable
              onPress={endCall}
              style={[styles.pressable, {backgroundColor: colors.icon.danger}]}>
              <Phone fill={colors.icon.white} style={styles.phone} />
            </Pressable>
          </View>
        </BodyCard>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  minimize: {
    padding: 8,
    backgroundColor: colors.textB50,
    borderRadius: 100,
  },
  image: {
    height: 200,
    width: 200,
    borderRadius: 200 / 2,
  },
  flex: {
    flex: 1,
  },
  pressable: {
    padding: 12,
    backgroundColor: colors.textB10,
    borderRadius: 100,
  },
  phone: {
    transform: [
      {
        rotate: '135deg',
      },
    ],
  },
  denied: {
    padding: 12,
    backgroundColor: colors.icon.danger,
    borderRadius: 20,
    height: 40,
    width: 40,
    alignSelf: 'center',
  },
  accept: {
    backgroundColor: colors.success,
    padding: 12,
    borderRadius: 100,
  },
});
