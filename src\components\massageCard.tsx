import React from 'react';
import {View, StyleSheet, Pressable} from 'react-native';
import Spacer from './Spacer';
import Image from './Image';
import Text from './Text';
import {BlueTick} from '../assets/icons';
import Row from './Row';
import {colors} from '../design/colors';
import ReadReceipt from './readReceipt';
import {Skeleton} from 'moti/skeleton';

interface Props {
  id: string;
  img: string | null;
  name: string;
  bio: string;
  lastSeen?: string;
  lastMsg?: string;
  agent?: boolean;
  onPressImage?: (
    id: string,
    name: string,
    img: string | null,
    bio: string,
  ) => void;
  onPress?: (
    id: string,
    img: string | null,
    name: string,
    lastSeen?: string,
    remoteUserId?: string,
    agentPhone?: string,
  ) => void;
  massageStatus?: 'sent' | 'delivered' | 'seen';
  massageCount: number;
  phone?: string;
  username?: string;
  endView?: React.ReactNode;
  loading: boolean;
  disabled?: boolean;
  remoteUserId?: string;
  agentPhone?: string;
}

function MassageCard({
  id,
  img,
  name,
  lastSeen,
  lastMsg,
  massageStatus,
  agent = false,
  onPressImage,
  massageCount,
  phone,
  username,
  onPress,
  endView,
  loading,
  disabled = false,
  remoteUserId,
  bio,
  agentPhone,
}: Props) {
  function handlePress() {
    if (onPress) {
      onPress(id, img, name, lastSeen, remoteUserId, agentPhone);
    }
  }

  function handleOnPressImage() {
    onPressImage && onPressImage(id, name, img, bio);
  }

  return (
    <View>
      <Skeleton.Group show={loading}>
        <Row center>
          <Skeleton colorMode="light" radius="round">
            <Pressable onPress={handleOnPressImage}>
              <Image uri={img} alt={name} height={52} width={52} />
            </Pressable>
          </Skeleton>
          <Spacer width={12} />
          <Pressable
            style={[styles.messageCard, disabled && styles.disable]}
            onPress={handlePress}
            disabled={disabled}>
            <Skeleton colorMode="light">
              <Row spread>
                <View>
                  <Row center>
                    <View>
                      <Text variant="subText1_500">{name}</Text>

                      {phone && (
                        <Text variant="subText1_500" color={colors.text.B50}>
                          {phone}
                        </Text>
                      )}
                      {username && (
                        <Text variant="subText2" color={colors.text.B50}>
                          @{username}
                        </Text>
                      )}
                    </View>
                    {agent && (
                      <>
                        <Spacer width={4} />
                        <BlueTick style={{alignSelf: 'center'}} />
                      </>
                    )}
                  </Row>
                  {lastMsg && (
                    <Row>
                      <Text numberOfLines={1} variant="subText2">
                        {lastMsg?.length > 28
                          ? `${lastMsg.slice(0, 28)}...`
                          : lastMsg}
                      </Text>
                      <Spacer width={2} />
                      <ReadReceipt
                        massageType="sender"
                        massageStatus={massageStatus}
                      />
                    </Row>
                  )}
                </View>

                <View style={styles.end}>
                  {lastSeen && <Text variant="subText2">{lastSeen}</Text>}
                  <Spacer height={2} />
                  {massageCount > 0 && (
                    <View style={styles.badge}>
                      <Text variant="body_500" color={colors.text.white}>
                        {massageCount > 99 ? '99+' : massageCount}
                      </Text>
                    </View>
                  )}
                </View>
                {endView && endView}
              </Row>
            </Skeleton>
          </Pressable>
        </Row>
      </Skeleton.Group>
    </View>
  );
}

const styles = StyleSheet.create({
  messageCard: {
    flex: 1,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  badge: {
    alignItems: 'center',
    backgroundColor: colors.primaryB200,
    justifyContent: 'center',
    height: 20,
    width: 20,
    borderRadius: 10,
  },
  end: {
    alignItems: 'flex-end',
  },
  disable: {
    opacity: 0.5,
  },
});

export default React.memo(MassageCard);
