import {<PERSON>, BackHand<PERSON>, Easing, View} from 'react-native';
import React from 'react';
import {BodyCard, Spacer, Text} from '../../components';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import LottieView from 'lottie-react-native';
import {RootStack} from '../../utils';
import messaging from '@react-native-firebase/messaging';
import {updateFcmToken} from '../../utils/apis';

const AnimatedLottieView = Animated.createAnimatedComponent(LottieView);

type VerifiedType = RouteProp<RootStack, 'verified'>;

export default function Verified() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {
    params: {isSuperUser, isSuperAdmin},
  } = useRoute<VerifiedType>();
  console.log(isSuperUser, isSuperAdmin, 'isSuperAdminisSuperAdmin');

  function navigateToHome() {
    navigation.navigate('tab', {screen: 'chatList'});
    return true;
  }
  React.useEffect(() => {
    messaging()
      .getToken()
      .then(token => {
        updateFcmToken(token);
      });
    const timeOut = setTimeout(() => {
      if (isSuperAdmin && isSuperUser) {
        navigation.replace('adminList');
      } else {
        navigation.replace('tab', {screen: 'chatList'});
      }
    }, 2000);
    BackHandler.addEventListener('hardwareBackPress', navigateToHome);

    return () => {
      clearInterval(timeOut);
      BackHandler.removeEventListener('hardwareBackPress', navigateToHome);
    };
  }, [navigation]);

  const animationProgress = React.useRef(new Animated.Value(0));
  React.useEffect(() => {
    Animated.timing(animationProgress.current, {
      toValue: 1,
      duration: 2000,
      easing: Easing.linear,
      useNativeDriver: false,
    }).start();
  }, []);
  return (
    <BodyCard>
      <View style={{justifyContent: 'center', alignItems: 'center', flex: 1}}>
        <AnimatedLottieView
          source={require('../../assets/animations/happy.json')}
          speed={1}
          style={{height: 64, width: 64}}
          autoPlay
          loop
          progress={animationProgress.current}
        />
        <Text variant="subText1_500">Hurray 🥳 OTP Verified Successfully</Text>
        <Spacer height={8} />
        <Text variant="subText2" center>
          Thank You! Now you can continue using{'\n'} N2 chat as a verified user
        </Text>
      </View>
    </BodyCard>
  );
}
