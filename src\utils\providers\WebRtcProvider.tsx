import React, {createContext, useEffect, useRef, useState} from 'react';
import {
  mediaDevices,
  MediaStream,
  RTCIceCandidate,
  RTCPeerConnection,
  RTCSessionDescription,
} from 'react-native-webrtc';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../types';
import {WebSocketContext} from './WebSocket';
import {useCallIncomingNotifications, useStore} from '../hooks';
import inCallManager from 'react-native-incall-manager';

type CallType = 'audio' | 'video' | 'screen';

interface RemoteUserInfo {
  chatId: string;
  from: string;
  to: string;
}

interface WebRTCContextType {
  localStream: MediaStream | null;
  remoteStream: MediaStream | null;
  startCall: (
    userId: string,
    type: CallType,
    remoteUserId?: string,
  ) => Promise<void>;
  answerCall: () => Promise<void>;
  endCall: () => void;
  declineCall: () => void;
  toggleAudio: () => void;
  handleLog: () => void;
  toggleSpeaker: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setCallerInfo: (parm?: any) => void;
  isMuted: boolean;
  isLoudspeaker: boolean;
  timer: number;
  isCallEnded: boolean;
  isCallActive: boolean;
  isIncomingCall: boolean;
  callerInfo: {
    userName: string;
    avatar?: string;
  } | null;
}

const configuration = {
  iceServers: [
    {urls: 'stun:stun.l.google.com:19302'},
    {urls: 'stun:stun1.l.google.com:19302'},
    {urls: 'stun:stun2.l.google.com:19302'},
  ],
  iceCandidatePoolSize: 10,
};

const sessionConstraints = {
  mandatory: {
    OfferToReceiveAudio: true,
    OfferToReceiveVideo: false,
    VoiceActivityDetection: true,
  },
};

export const WebRTCContext = createContext<WebRTCContextType>({
  localStream: null,
  remoteStream: null,
  isMuted: false,
  isLoudspeaker: false,
  startCall: async () => {},
  answerCall: async () => {},
  endCall: () => {},
  declineCall: () => {},
  toggleAudio: () => {},
  handleLog: () => {},
  toggleSpeaker: () => {},
  setCallerInfo: () => {},
  timer: 0,
  isCallEnded: false,
  isCallActive: false,
  isIncomingCall: false,
  callerInfo: null,
});

interface Props {
  children: React.ReactNode;
  socket: WebSocket | null;
}

export default function WebRTCProvider({children, socket}: Props) {
  const [currentNotificationId, setCurrentNotificationId] = useState<
    string | null
  >();

  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  const [remoteOffer, setRemoteOffer] = useState<RTCSessionDescription | null>(
    null,
  );
  const [isIncomingCall, setIsIncomingCall] = useState(false);
  const [callerInfo, setCallerInfo] = useState<{
    userName: string;
    avatar?: string;
  } | null>(null);
  const [isCallEnded, setIsCallEnded] = useState(false);
  const [remoteCallInfo, setRemoteCallInfo] = useState<RemoteUserInfo | null>(
    null,
  );
  const [remoteUserId, setRemoteUserId] = useState<string | undefined>();
  const [isLoudspeaker, setIsLoudspeaker] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isCallActive, setIsCallActive] = useState(false);
  const [timer, setTimer] = useState<number>(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const peerConnection = useRef<RTCPeerConnection | null>(null);

  const {currentOpenChat, appState} = React.useContext(WebSocketContext);
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {phone, currentUserId} = useStore(state => ({
    phone: state?.user?.phone,
    currentUserId: state?.user?.id,
  }));

  const startTimer = () => {
    timerRef.current = setInterval(() => {
      setTimer(prev => prev + 1);
    }, 1000);
  };

  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    setTimer(0);
  };

  const setupPeerConnectionListeners = (pc: RTCPeerConnection) => {
    pc.onconnectionstatechange = () => {
      if (
        pc.connectionState === 'failed' ||
        pc.connectionState === 'disconnected'
      ) {
        endCall();
      } else if (pc.connectionState === 'connected') {
        startTimer();
      }
    };

    pc.ontrack = event => {
      if (event.streams && event.streams[0]) {
        setRemoteStream(event.streams[0]);
      }
    };
  };

  const getMediaStream = async (type: CallType) => {
    try {
      const constraints = {
        audio: true,
        video:
          type === 'video'
            ? {
                facingMode: 'user',
              }
            : false,
        frameRate: 30,
      };

      const stream = await mediaDevices.getUserMedia(constraints);
      setLocalStream(stream);
      return stream;
    } catch (err) {
      console.error('Error accessing media devices:', err);
    }
  };

  const startCall = async (
    chatId: string,
    type: CallType,
    remoteUserId?: string,
  ) => {
    setRemoteUserId(remoteUserId);
    try {
      const stream = await getMediaStream('audio');
      inCallManager.start({media: 'audio', ringback: '_DTMF_'});
      inCallManager.chooseAudioRoute('BLUETOOTH');

      const pc = (peerConnection.current = new RTCPeerConnection(
        configuration,
      ));

      if (stream) {
        stream.getTracks().forEach(track => {
          pc.addTrack(track, stream);
        });
      }
      setIsCallActive(true);
      const offer = await pc.createOffer(sessionConstraints);
      await pc.setLocalDescription(offer);

      if (socket) {
        socket.send(
          JSON.stringify({
            type: 'call-offer',
            offer,
            chat_id: currentOpenChat?.id,
            callType: type,
            to: remoteUserId,
            from: currentUserId,
          }),
        );
      }

      pc.onicecandidate = event => {
        if (event.candidate && socket) {
          socket.send(
            JSON.stringify({
              type: 'ice-candidate',
              candidate: event.candidate,
              chat_id: currentOpenChat?.id,
              to: remoteUserId,
              from: currentUserId,
            }),
          );
        }
      };

      setupPeerConnectionListeners(pc);
    } catch (err) {
      console.error('Error starting call:', err);
      endCall();
    }
  };

  const answerCall = async () => {
    try {
      const stream = await getMediaStream('audio');
      const pc = (peerConnection.current = new RTCPeerConnection(
        configuration,
      ));

      if (stream) {
        stream.getTracks().forEach(track => {
          pc.addTrack(track, stream);
        });
      }
      setIsCallActive(true);
      if (remoteOffer) {
        await pc.setRemoteDescription(remoteOffer);
        const answer = await pc.createAnswer();
        await pc.setLocalDescription(answer);

        if (socket) {
          socket.send(
            JSON.stringify({
              type: 'call-answer',
              answer,
              chat_id: remoteCallInfo?.chatId,
              to: remoteCallInfo?.from,
              from: remoteCallInfo?.to,
            }),
          );
        }
      }

      pc.onicecandidate = event => {
        if (event.candidate && socket) {
          socket.send(
            JSON.stringify({
              type: 'ice-candidate',
              candidate: event.candidate,
              chat_id: currentOpenChat?.id,
              to: remoteCallInfo?.from,
              from: remoteCallInfo?.to,
            }),
          );
        }
      };

      setupPeerConnectionListeners(pc);
      inCallManager.stopRingtone();
    } catch (err) {
      console.error('Error answering call:', err);
      endCall();
    }
  };

  function handleLog() {
    console.log(peerConnection, 'peerConnection', phone);
    console.log(remoteStream, 'remoteStream');
  }

  const endCall = () => {
    inCallManager.stopRingback();
    inCallManager.stopRingtone();
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
    }
    if (peerConnection.current) {
      peerConnection.current.close();
      peerConnection.current = null;
    }
    setLocalStream(null);
    setRemoteStream(null);
    setRemoteCallInfo(null);
    setRemoteOffer(null);
    setIsCallEnded(true);
    setIsLoudspeaker(false);
    setIsMuted(false);
    setIsCallActive(false);
    stopTimer();

    if (socket) {
      socket.send(
        JSON.stringify({
          type: 'end-call',
          chat_id: currentOpenChat?.id
            ? currentOpenChat?.id
            : remoteCallInfo?.chatId,
          to: remoteCallInfo?.from ? remoteCallInfo?.from : remoteUserId,
          from: currentUserId,
        }),
      );
    }

    setTimeout(() => {
      setIsCallEnded(false);
    }, 1000);
  };

  const declineCall = () => {
    inCallManager.stopRingback();
    inCallManager.stopRingtone();
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
    }
    if (peerConnection.current) {
      peerConnection.current.close();
      peerConnection.current = null;
    }
    setLocalStream(null);
    setRemoteStream(null);
    setRemoteCallInfo(null);
    setRemoteOffer(null);
    setIsCallEnded(true);
    setIsLoudspeaker(false);
    setIsMuted(false);
    setIsCallActive(false);
    stopTimer();

    if (socket) {
      socket.send(
        JSON.stringify({
          type: 'denied-call',
          chat_id: remoteCallInfo?.chatId,
          to: remoteCallInfo?.from,
          from: remoteCallInfo?.to,
        }),
      );
    }

    setTimeout(() => {
      setIsCallEnded(false);
    }, 1000);
  };

  const {showIncomingCall, cancelCallNotification} =
    useCallIncomingNotifications({
      onAnswer: answerCall,
      onDecline: declineCall,
    });

  const toggleAudio = () => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled;
        setIsMuted(!audioTrack.enabled);
      }
    }
  };

  const toggleSpeaker = () => {
    if (isLoudspeaker) {
      inCallManager.setSpeakerphoneOn(false);
    } else {
      inCallManager.setSpeakerphoneOn(true);
    }
    setIsLoudspeaker(!isLoudspeaker);
  };

  const handleWebSocketMessage = async (event: WebSocketMessageEvent) => {
    const data = JSON.parse(event.data);

    switch (data.type) {
      case 'remote-offer': {
        setIsCallEnded(false);
        setRemoteOffer(data?.offer);
        setRemoteCallInfo({
          chatId: data.chatId,
          from: data.from,
          to: data.to,
        });
        setIsIncomingCall(true);
        setCallerInfo({
          userName: data?.userInfo[0]?.saved_name || data?.userInfo[0]?.phone,
          avatar: data?.userInfo[0]?.avatar?.url,
        });
        setIsCallActive(true);

        inCallManager.startRingtone('_BUNDLE_', 30, '_DEFAULT_', 30);

        if (appState === 'background') {
          const notification = await showIncomingCall({
            name: data?.userInfo[0]?.saved_name || data?.userInfo[0]?.phone,
          });

          if (notification) {
            setCurrentNotificationId(notification);
          }
        }
        navigation.navigate('voiceCall', {
          isIncomingCall: true,
          userName: data?.userInfo[0]?.saved_name || data?.userInfo[0]?.phone,
          avatar: data?.userInfo[0]?.avatar?.url,
        });

        // const timeoutId = setTimeout(() => {
        //   endCall();
        // }, 10000);

        // const clearTimeoutOnAnswerOrDecline = () => {
        //   clearTimeout(timeoutId);
        // };

        // socket?.addEventListener('message', event => {
        //   const eventData = JSON.parse(event.data);
        //   if (
        //     ['remote-call-answer', 'remote-end-call'].includes(eventData.type)
        //   ) {
        //     clearTimeoutOnAnswerOrDecline();
        //   }
        // });

        break;
      }

      case 'remote-user-busy':
        {
          const interval = setInterval(() => {
            inCallManager.start({media: 'audio', ringback: '_BUSY_'});
            inCallManager.stop({busytone: '_DTMF_'});
          }, 3000);

          setTimeout(() => {
            clearInterval(interval);
          }, 3000);
        }

        if (localStream) {
          localStream.getTracks().forEach(track => track.stop());
        }

        if (peerConnection.current) {
          peerConnection.current.close();
          peerConnection.current = null;
        }

        setLocalStream(null);
        setRemoteStream(null);
        setRemoteCallInfo(null);
        setRemoteOffer(null);
        stopTimer();

        // setTimeout(() => {
        //   setIsCallEnded(true);
        //   inCallManager.stopRingtone();
        // }, 10000);

        break;

      case 'remote-call-answer':
        setIsIncomingCall(false);
        if (
          peerConnection.current &&
          data.answer &&
          data.from !== currentUserId
        ) {
          try {
            inCallManager.stopRingback();
            if (data.from !== currentUserId) {
              peerConnection.current.setRemoteDescription(
                new RTCSessionDescription(data.answer),
              );
              setIsCallActive(true);
            }
          } catch (error) {
            console.error(
              'Error setting remote description from answer:',
              error,
            );
          }
        } else {
          console.warn(
            'Received answer but no peer connection exists or answer is missing',
          );
        }
        break;
      case 'remote-ice-candidate':
        try {
          const candidate = new RTCIceCandidate(data.candidate);
          if (peerConnection.current) {
            peerConnection.current.addIceCandidate(candidate).then(() => {
              console.log('Successfully added ICE candidate');
            });
          }
        } catch (error) {
          console.error('Error processing ICE candidate:', error);
        }
        break;

      case 'remote-end-call':
        inCallManager.stopRingback();
        inCallManager.stopRingtone();

        if (currentNotificationId) {
          await cancelCallNotification(currentNotificationId);
          setCurrentNotificationId(null);
        }
        if (localStream) {
          localStream.getTracks().forEach(track => track.stop());
        }

        if (peerConnection.current) {
          peerConnection.current.close();
          peerConnection.current = null;
        }
        setIsIncomingCall(false);
        setCallerInfo(null);
        setLocalStream(null);
        setRemoteStream(null);
        setRemoteCallInfo(null);
        setRemoteOffer(null);
        setIsCallEnded(true);
        stopTimer();
        setIsLoudspeaker(false);
        setIsMuted(false);
        setIsCallActive(false);
        setTimeout(() => {
          setIsCallEnded(false);
        }, 1000);
        break;

      case 'remote-denied-call':
        inCallManager.stopRingback();
        inCallManager.stopRingtone();

        if (localStream) {
          localStream.getTracks().forEach(track => track.stop());
        }

        if (peerConnection.current) {
          peerConnection.current.close();
          peerConnection.current = null;
        }
        setIsIncomingCall(false);
        setCallerInfo(null);
        setLocalStream(null);
        setRemoteStream(null);
        setRemoteCallInfo(null);
        setRemoteOffer(null);
        setIsCallEnded(true);
        setIsLoudspeaker(false);
        stopTimer();
        setIsMuted(false);
        setIsCallActive(false);

        setTimeout(() => {
          setIsCallEnded(false);
        }, 1000);
        break;
    }
  };

  useEffect(() => {
    if (!socket) return;

    socket.addEventListener('message', handleWebSocketMessage);

    return () => {
      socket.removeEventListener('message', handleWebSocketMessage);
    };
  }, [handleWebSocketMessage]);

  useEffect(() => {
    return () => {
      setIsCallEnded(false);
    };
  }, []);

  // useEffect(() => {
  //   notifee.onForegroundEvent(event => {
  //     console.log(JSON.stringify(event), 'onForegroundEvent');
  //   });

  //   notifee.onBackgroundEvent(async event => {
  //     const {type, detail} = event;
  //     if (type === 0) {
  //       const pressAction = detail?.pressAction?.id;
  //       if (pressAction) {
  //         switch (pressAction) {
  //           case 'answer': {
  //             await answerCall();
  //             break;
  //           }
  //         }
  //       }
  //     }
  //     console.log(JSON.stringify(event), 'onBackgroundEvent');
  //   });
  // }, []);

  return (
    <>
      <WebRTCContext.Provider
        value={{
          isCallActive,
          isIncomingCall,
          callerInfo,
          localStream,
          remoteStream,
          startCall,
          answerCall,
          endCall,
          toggleAudio,
          handleLog,
          toggleSpeaker,
          isMuted,
          isLoudspeaker,
          timer,
          isCallEnded,
          declineCall,
          setCallerInfo,
        }}>
        {children}
      </WebRTCContext.Provider>
    </>
  );
}

//No task registered for key app.notifee.notification-event
