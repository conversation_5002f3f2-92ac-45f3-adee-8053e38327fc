import {Pressable, View} from 'react-native';
import React from 'react';
import {
  BodyCard,
  Text,
  BackBtn,
  Spacer,
  Input,
  Cta,
  KeyboardAvoidingView,
} from '../../components';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../../utils';
import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {z} from 'zod';
import {CloseEye, OpenEye} from '../../assets/icons';
import {PasswordSchema} from '../../utils/validationSchema';
import {colors} from '../../design/colors';

type newPasswordRouteProp = RouteProp<RootStack, 'newPassword'>;
type FormData = z.infer<typeof PasswordSchema>;
export default function ConfirmPassword() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const [showPassword, setShowPassword] = React.useState(true);
  const {
    control,
    handleSubmit,
    formState: {isValid},
  } = useForm<FormData>({
    resolver: zodResolver(PasswordSchema),
  });

  const {
    params: {phone, otp, userRole},
  } = useRoute<newPasswordRouteProp>();

  function togglePassword() {
    setShowPassword(!showPassword);
  }
  function onSubmit({password}: FormData) {
    if (phone) {
      navigation.navigate('confirmPassword', {
        phone: phone,
        otp: otp,
        newPassword: password,
        userRole,
      });
    }
  }

  return (
    <BodyCard padBottom padTop>
      <KeyboardAvoidingView>
        <BackBtn />
        <View>
          <Text variant="H2_500">New password</Text>
          <Spacer height={4} />
          <Text variant="subText2" color={colors.text.B40}>
            Enter a new password for this account. It is recommended not use the
            passwords used earlier for account security.
          </Text>
        </View>
        <Spacer height={16} />
        <Input
          maxLength={20}
          control={control}
          name="password"
          secureTextEntry={showPassword}
          endView={
            <Pressable onPress={togglePassword}>
              {showPassword ? <OpenEye /> : <CloseEye />}
            </Pressable>
          }
        />
      </KeyboardAvoidingView>
      <Cta
        appearance="primary"
        title="Continue"
        onPress={handleSubmit(onSubmit)}
        disabled={!isValid}
      />
    </BodyCard>
  );
}
