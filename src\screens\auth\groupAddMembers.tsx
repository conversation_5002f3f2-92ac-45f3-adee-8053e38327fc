import {<PERSON><PERSON>, FlatList} from 'react-native';
import React, {useCallback, useState} from 'react';
import {
  BackBtn,
  BodyCard,
  Fab,
  Header,
  Input,
  MassageCard,
  Spacer,
} from '../../components';
import {CheckIcon, SearchIcon, RightFab, CheckBox} from '../../assets/icons';
import {colors} from '../../design/colors';
import {useForm} from 'react-hook-form';
import {
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack, ContactList} from '../../utils';
import {addGroupMembers, getContactList} from '../../utils/apis';
import {useStore} from '../../utils/hooks';
import {showTostMessage} from '../../utils/helpers';

export default function GroupAddMembers() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const [selectedIndices, setSelectedIndices] = useState<string[]>([]);
  const [fullContactList, setFullContactList] = useState<ContactList[]>([]);
  type groupRouteProp = RouteProp<RootStack, 'groupAddMembers'>;
  const {
    params: {chatId, selectContact},
  } = useRoute<groupRouteProp>();

  const {control} = useForm<FormData>({});
  const {contactLists} = useStore(state => ({
    contactLists: state.contactLists,
  }));
  const handleFabPress = async () => {
    try {
      const response = await addGroupMembers(chatId, selectedIndices);
      const data = await response.json();
      if (response.ok && response.status == 200) {
        showTostMessage('Members Added Successfully');
        navigation.navigate('groupInfo', {chatId});
      } else {
        showTostMessage(data.error);
      }
    } catch (error) {
      showTostMessage('Something went Wrong');
    }
  };
  const handlePress = React.useCallback((data: ContactList) => {
    setSelectedIndices(prevState =>
      prevState.includes(data?.phone)
        ? prevState.filter(item => item !== data?.phone)
        : [...prevState, data?.phone],
    );
  }, []);

  const renderItem = ({item}: {item: ContactList}) => {
    return (
      <MassageCard
        bio="bio"
        loading={false}
        disabled={item?.isSelected}
        id={item?.contact_id}
        img={null}
        name={item?.name}
        phone={item.phone}
        username={item.username}
        massageStatus="delivered"
        massageCount={0}
        onPress={() => handlePress(item)}
        endView={
          item?.isSelected ? (
            <CheckBox />
          ) : selectedIndices?.includes(item?.phone) ? (
            <CheckBox />
          ) : (
            <CheckIcon />
          )
        }
      />
    );
  };

  useFocusEffect(
    useCallback(() => {
      getContactList()
        .then(res => {
          if (res.status === 500) {
            Alert.alert('Error', 'Something went wrong, try again later!');
          }
          return res.json();
        })
        .then(response => {
          for (let i = 0; i < response.length; i++) {
            if (selectContact?.includes(response[i].phone)) {
              response[i].isSelected = true;
            } else {
              response[i].isSelected = false;
            }
          }
          setFullContactList(response);
        })

        .catch(error => {
          console.log(error);
          Alert.alert('Error', 'Something went wrong, try again later!');
        });
    }, []),
  );

  function onChangeText(search: string) {
    const filteredContacts = contactLists.filter(contact =>
      contact.name.toLowerCase().includes(search.toLowerCase()),
    );
    setFullContactList(filteredContacts);
  }

  return (
    <BodyCard padTop>
      <BackBtn />
      <Header title="Add members" />
      <Input
        control={control}
        name="Search contacts"
        placeholder="Search"
        placeholderTextColor={colors.primaryB100}
        endView={<SearchIcon />}
        inputStyle={{backgroundColor: colors.primaryB50}}
        onChangeText={onChangeText}
      />
      <FlatList
        data={fullContactList}
        showsVerticalScrollIndicator={false}
        renderItem={renderItem}
        keyExtractor={(_, idx) => idx.toString()}
        ItemSeparatorComponent={() => <Spacer height={4} />}
      />

      {selectedIndices?.length > 0 && (
        <Fab icon={<RightFab />} onPress={handleFabPress} />
      )}
    </BodyCard>
  );
}
