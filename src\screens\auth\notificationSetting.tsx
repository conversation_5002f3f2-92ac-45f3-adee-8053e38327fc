import {View, KeyboardAvoidingView} from 'react-native';
import React from 'react';
import {BackBtn, BodyCard, Row, Spacer, Text, Toggle} from '../../components';
import {colors} from '../../design/colors';

const notificationsSeting = [
  {
    id: '1',
    name: 'Chat notifications',
  },
  {
    id: '2',
    name: 'Group notifications',
  },
  {
    id: '3',
    name: 'App sounds',
  },
  {
    id: '4',
    name: 'Vibrations',
  },
];

export default function NotificationSetting() {
  return (
    <BodyCard padBottom padTop>
      <KeyboardAvoidingView style={{flex: 1}}>
        <BackBtn />
        <View>
          <Text variant="H2_500">Notification Settings</Text>
          <Spacer height={8} />
          <Text variant="subText2" color={colors.text.B40}>
            Manage your app notifications settings here
          </Text>
        </View>
        <Spacer height={52} />
        <View
          style={{
            rowGap: 12,
            flex: 1,
            // backgroundColor: 'red',
          }}>
          {notificationsSeting.map(item => (
            <Row key={item.id} spread>
              <Text variant="subText1">{item.name}</Text>
              <Toggle thumbSize={20} disabled={false} />
            </Row>
          ))}
        </View>
      </KeyboardAvoidingView>
    </BodyCard>
  );
}
