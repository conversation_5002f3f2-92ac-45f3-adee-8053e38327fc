import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StyleProp,
  ViewStyle,
  Image,
  Alert,
  Linking,
  Keyboard,
  Pressable,
} from 'react-native';
import {
  Camera as VisionCamera,
  CameraDevice,
  PhotoFile,
} from 'react-native-vision-camera';
import {PERMISSIONS, request, check, RESULTS} from 'react-native-permissions';
import {Cross, FlashCamera, SwitchCamera, TakePhoto} from '../assets/icons';
import Spacer from './Spacer';
import DocumentPicker, {
  DocumentPickerResponse,
} from 'react-native-document-picker';
import {createStoryWithCamera} from '../utils/apis';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../utils';
import {colors} from '../design/colors';
import {showTostMessage} from '../utils/helpers';
export interface Props {
  onPhotoTaken: (photo: PhotoFile) => void;
  style?: StyleProp<ViewStyle>;
  toggleCamera: () => void;
  sendFileFromGallery?: (data: DocumentPickerResponse[]) => Promise<void>;
  cameraMode?: 'story' | 'chat';
}

export interface CustomCameraRef {
  takePhoto: () => Promise<void>;
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
}

const Camera = forwardRef<CustomCameraRef, Props>(
  (
    {onPhotoTaken, style, toggleCamera, sendFileFromGallery, cameraMode},
    ref,
  ) => {
    const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

    const cameraRef = useRef<VisionCamera>(null);
    const [isRecording, setIsRecording] = useState(false);
    const [isFrontCamera, setIsFrontCamera] = useState(true);
    const [device, setDevice] = useState<CameraDevice | undefined>();
    const [hasPermission, setHasPermission] = useState<boolean>(false);
    const [flashMode, setFlashMode] = useState<'off' | 'on'>('off');
    const [disable, setDisable] = useState(false);
    const [capturedPhoto, setCapturedPhoto] = useState<PhotoFile | null>(null);

    function navigateToBack() {
      toggleCamera();
    }

    const fetchDevices = async () => {
      try {
        const devices = await VisionCamera.getAvailableCameraDevices();
        const selectedDevice = isFrontCamera
          ? devices.find(d => d.position === 'front')
          : devices.find(d => d.position === 'back');
        setDevice(selectedDevice);
      } catch (error) {
        console.error('Failed to fetch camera devices:', error);
      }
    };

    const requestCameraPermission = async () => {
      const result = await request(PERMISSIONS.ANDROID.CAMERA);
      if (result === RESULTS.GRANTED) {
        setHasPermission(true);
      } else {
        Alert.alert(
          'Camera Permission',
          'Camera access is required to take photos. Please grant the permission.',
          [
            {
              text: 'Cancel',
              style: 'cancel',
            },
            {
              text: 'Open Settings',
              onPress: () => Linking.openSettings(),
            },
          ],
        );
      }
    };

    const checkCameraPermission = async () => {
      const result = await check(PERMISSIONS.ANDROID.CAMERA);
      if (result === RESULTS.GRANTED) {
        setHasPermission(true);
        fetchDevices();
      } else {
        requestCameraPermission();
      }
    };

    useEffect(() => {
      checkCameraPermission();
      Keyboard.dismiss();
    }, []);

    useEffect(() => {
      if (hasPermission) {
        fetchDevices();
      }
    }, [isFrontCamera, hasPermission]);

    useImperativeHandle(ref, () => ({
      takePhoto: async () => {
        if (cameraRef.current) {
          try {
            const photo = await cameraRef.current.takePhoto({flash: flashMode});
            if (photo) {
              setCapturedPhoto(photo);
              setDisable(true);
            }
          } catch (error) {
            console.error('Failed to take photo:', error);
          }
        }
      },
      startRecording: async () => {
        if (cameraRef.current) {
          setIsRecording(true);
          try {
            await cameraRef.current.startRecording({
              onRecordingFinished: video => {
                console.log('Recording finished:', video);
                setIsRecording(false);
              },
              onRecordingError: error => {
                console.error('Recording error:', error);
                setIsRecording(false);
              },
            });
          } catch (error) {
            console.error('Failed to start recording:', error);
            setIsRecording(false);
          }
        }
      },
      stopRecording: async () => {
        if (cameraRef.current && isRecording) {
          try {
            await cameraRef.current.stopRecording();
            setIsRecording(false);
          } catch (error) {
            console.error('Failed to stop recording:', error);
            setIsRecording(false);
          }
        }
      },
    }));

    const toggleFlash = () => {
      setFlashMode(prevMode => {
        if (prevMode === 'off') return 'on';
        return 'off';
      });
    };

    if (!hasPermission) {
      return (
        <Text style={styles.loadingText}>Requesting camera permission...</Text>
      );
    }

    if (!device) return <Text style={styles.loadingText}>Loading...</Text>;

    return (
      <>
        <View style={[styles.container, style]}>
          <VisionCamera
            style={styles.camera}
            device={device}
            isActive={!capturedPhoto}
            ref={cameraRef}
            video={true}
            audio={true}
            photo={true}
          />
          {capturedPhoto ? (
            <Image
              source={{uri: 'file://' + capturedPhoto.path}}
              style={styles.capturedImage}
            />
          ) : null}

          {capturedPhoto ? (
            <View style={styles.confirmationContainer}>
              <Text style={styles.confirmationText}>
                Do you want to send this photo?
              </Text>
              <View style={styles.confirmationButtons}>
                <TouchableOpacity
                  onPress={async () => {
                    if (cameraMode === 'story') {
                      const response = await createStoryWithCamera(
                        capturedPhoto,
                      );
                      if (response.ok && response.status === 200) {
                        showTostMessage('Story created');
                        navigation.navigate('tab', {screen: 'chatList'});
                      } else {
                        showTostMessage('Something Went Wrong');
                      }
                    } else {
                      onPhotoTaken(capturedPhoto);
                    }
                    setCapturedPhoto(null);
                    setDisable(false);
                  }}
                  style={styles.confirmButton}>
                  <Text style={styles.buttonText}>Send</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => {
                    setCapturedPhoto(null);
                    setDisable(false);
                  }}
                  style={styles.cancelButton}>
                  <Text style={styles.buttonText}>Retake</Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <>
              <View style={styles.controls}>
                <TouchableOpacity
                  disabled={disable}
                  onPress={() => {
                    if (ref && typeof ref !== 'function' && ref.current) {
                      ref.current.takePhoto();
                    }
                  }}
                  style={styles.button}>
                  <TakePhoto />
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>

        {!capturedPhoto && (
          <View style={{position: 'absolute', top: 20, paddingHorizontal: 16}}>
            <View
              style={{
                width: '100%',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <Pressable onPress={navigateToBack}>
                <Cross />
              </Pressable>
              <View>
                <Spacer height={8} />
                <TouchableOpacity
                  onPress={() => setIsFrontCamera(prev => !prev)}>
                  <SwitchCamera />
                </TouchableOpacity>
              </View>
            </View>
            <View style={{alignItems: 'flex-end', marginTop: 12}}>
              {!isFrontCamera && (
                <TouchableOpacity onPress={toggleFlash}>
                  <FlashCamera
                    fill={flashMode === 'on' ? colors.primaryB200 : 'white'}
                  />
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}
      </>
    );
  },
);

Camera.displayName = 'Camera';

const styles = StyleSheet.create({
  container: {
    height: '100%',
    width: '100%',
  },
  camera: {
    flex: 1,
  },
  capturedImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
  controls: {
    position: 'absolute',
    bottom: 28,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  controlRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    left: 30,
  },
  confirmationContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    padding: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmationText: {
    color: 'white',
    marginBottom: 10,
  },
  confirmationButtons: {
    flexDirection: 'row',
  },
  confirmButton: {
    backgroundColor: 'green',
    padding: 10,
    borderRadius: 5,
    marginHorizontal: 10,
  },
  cancelButton: {
    backgroundColor: 'red',
    padding: 10,
    borderRadius: 5,
    marginHorizontal: 10,
  },
  buttonText: {
    color: 'white',
  },

  loadingText: {
    color: 'black',
    textAlign: 'center',
    marginTop: '50%',
  },
  button: {
    backgroundColor: '#fff',
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
});

export default Camera;
