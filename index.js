import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import messaging from '@react-native-firebase/messaging';

messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log(
    'sssssssssssssssssssssssssssss dddddddddddddddddddddddddddddddd',
    JSON.stringify(remoteMessage),
  );
});

messaging().getInitialNotification(async remoteMessage => {
  console.log(
    'ttttttttttttttttttttttttttttttttttttttttttr dddddddddddddddddddddddddddddddd',
    JSON.stringify(remoteMessage),
  );
  1;
});

AppRegistry.registerComponent(appName, () => App);
