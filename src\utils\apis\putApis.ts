import {API_URL_DEBUG, API_URL_RELEASE} from '@env';
import {DocumentPickerResponse} from 'react-native-document-picker';
import {PhotoFile} from 'react-native-vision-camera';
console.log(API_URL_DEBUG, API_URL_RELEASE);

const API_URL = __DEV__ ? API_URL_DEBUG : API_URL_RELEASE;

console.log(API_URL);

export async function updateUserProfile(name?: string, bio?: string) {
  return await fetch(`${API_URL}/user/profile`, {
    method: 'put',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({name, bio}),
  });
}

export async function updateUserAvatar(
  photo: PhotoFile | DocumentPickerResponse,
) {
  const body = new FormData();

  let fileName, fileUri;

  if ('path' in photo) {
    fileName = photo.path.split('/').pop();
    fileUri = `file://${photo.path}`;
  } else {
    fileName = photo.name;
    fileUri = photo.uri;
  }

  body.append('file', {
    uri: fileUri,
    name: fileName,
    type: 'image/jpeg',
  });
  const response = await fetch(`${API_URL}/user/avatar`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    body,
  });
  return response;
}

export async function adminAgentLogOut(agent_id: string) {
  return await fetch(`${API_URL}/admin/agent/logout`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({agent_id}),
  });
}

export async function removeUserFromGroup(chat_id: string, phone: string) {
  return await fetch(`${API_URL}/group/remove/${chat_id}?phone=${phone}`, {
    method: 'PUT',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  });
}

export async function updateUserGroupInfo(
  chat_id: string,
  name: string,
  description?: string,
) {
  return await fetch(`${API_URL}/group/update/info/${chat_id}`, {
    method: 'PUT',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({name, description}),
  });
}

export async function exitGroup(chat_id: string | undefined) {
  return await fetch(`${API_URL}/group/exit/${chat_id}`, {
    method: 'put',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  });
}

export async function updateGroupAvatar(
  chat_id: string | undefined,
  photo: PhotoFile | DocumentPickerResponse,
) {
  const body = new FormData();

  let fileName, fileUri;

  if ('path' in photo) {
    fileName = photo.path.split('/').pop();
    fileUri = `file://${photo.path}`;
  } else {
    fileName = photo.name;
    fileUri = photo.uri;
  }

  body.append('file', {
    uri: fileUri,
    name: fileName,
    type: 'image/jpeg',
  });
  const response = await fetch(`${API_URL}/group/avatar/${chat_id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    body,
  });
  return response;
}
