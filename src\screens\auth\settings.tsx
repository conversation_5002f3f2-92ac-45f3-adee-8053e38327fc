import {View, StyleSheet, Pressable} from 'react-native';
import React from 'react';
import {
  BodyCard,
  Text,
  BackBtn,
  Spacer,
  Cta,
  KeyboardAvoidingView,
  Alert,
} from '../../components';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../../utils';
import {LogOut, Chevron} from '../../assets/icons';
import {colors} from '../../design/colors';
import {userLogOut} from '../../utils/apis/postApis';
import {useStore} from '../../utils/hooks';
import {globalSocket} from '../../utils/apis';

export default function Settings() {
  const [showAlert, setShowAlert] = React.useState(false);
  const {cleanLocalCache} = useStore(state => ({
    cleanLocalCache: state.cleanLocalCache,
  }));

  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  function navigateToProfileSettings() {
    navigation.navigate('profile');
  }
  function navigateToAccountSettings() {
    navigation.navigate('accountSetting');
  }
  function navigateToNotificationSettings() {
    navigation.navigate('notificationSetting');
  }
  function toggleAlert() {
    setShowAlert(prevState => !prevState);
  }

  async function handleLogout() {
    userLogOut();
    cleanLocalCache();
    setShowAlert(false);
    globalSocket().close();

    while (navigation.canGoBack()) {
      navigation.pop();
    }
    navigation.replace('welcome');
  }

  return (
    <>
      <BodyCard padBottom padTop>
        <KeyboardAvoidingView>
          <BackBtn />
          <View>
            <Text variant="H2_500">Settings</Text>
            <Spacer height={8} />
            <Text variant="subText2" color={colors.text.B40}>
              User profile, app preferences and account related settings can be
              accessed here.
            </Text>
          </View>
          <Spacer height={32} />
          <View>
            <Pressable onPress={navigateToProfileSettings} style={styles.row}>
              <Text variant="subText1" style={styles.text}>
                Profile Settings
              </Text>
              <Chevron />
            </Pressable>
            <Pressable onPress={navigateToAccountSettings} style={styles.row}>
              <Text variant="subText1" style={styles.text}>
                Account Settings
              </Text>
              <Chevron />
            </Pressable>
            <Pressable
              onPress={navigateToNotificationSettings}
              style={styles.row}>
              <Text variant="subText1" style={styles.text}>
                Notification Settings
              </Text>
              <Chevron />
            </Pressable>
          </View>
        </KeyboardAvoidingView>
        <Cta
          icon={<LogOut />}
          appearance="outline"
          title="Logout"
          onPress={toggleAlert}
        />
      </BodyCard>
      <Alert
        visible={showAlert}
        message="Do you really want to logout?"
        onClose={toggleAlert}
        onConfirm={handleLogout}
        rightText="Yes, logout"
      />
    </>
  );
}
const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 10,
  },
  text: {
    flex: 1,
  },
});
