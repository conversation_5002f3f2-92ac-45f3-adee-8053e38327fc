import React, {useState} from 'react';
import {
  View,
  Image as RNImage,
  StyleSheet,
  ImagePropsBase,
  StyleProp,
  ImageStyle,
} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';
// import Text from './Text';
import {colors} from '../design/colors';
interface ImageProps extends ImagePropsBase {
  alt: string;
  style?: StyleProp<ImageStyle>;
  uri: string | null;
}
// TODO: we are using random image thats why we are not use alt and getInitials
function Image({uri, style, ...rest}: ImageProps) {
  const [imageError, setImageError] = useState(false);

  // const getInitials = (name: string) => `${name.charAt(0).toUpperCase()}`;

  function handleImageError() {
    setImageError(!imageError);
  }

  return (
    <View style={{alignSelf: 'center'}}>
      {uri === null || imageError || uri === undefined ? (
        <RNImage
          style={[styles.image, style]}
          source={{
            uri: `https://avatar.iran.liara.run/public/${Math.floor(
              Math.random() * 90 + 10,
            )}`,
          }}
        />
      ) : (
        // <View style={[styles.image, styles.bgColor]}>
        //   <Text center variant="H1_500" color={colors.text.white}>
        //     {getInitials(alt)}
        //   </Text>
        // </View>
        <RNImage
          {...rest}
          style={[styles.image, style]}
          source={{uri: uri}}
          resizeMode="cover"
          onError={handleImageError}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  image: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(13 / 2),
    alignItems: 'center',
    justifyContent: 'center',
  },
  bgColor: {
    backgroundColor: colors.primaryB200,
  },
});

export default React.memo(Image);
