import React, {useState} from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  Modal,
  Pressable,
} from 'react-native';
import Text from './Text';
import {colors} from '../design/colors';
import {BlurView} from '@react-native-community/blur';
import {Back} from '../assets/icons';
import Spacer from './Spacer';

interface Props<T> {
  data: T[];
  placeholder?: string;
  onSelect: (item: T) => void;
  label: keyof T;
  value: keyof T;
  lable2: keyof T;
}

export default function Dropdown<T extends object>({
  data,
  placeholder,
  onSelect,
  label,
  value,
  lable2,
}: Props<T>) {
  const [isVisible, setIsVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItem, setSelectedItem] = useState<T | null>(null);

  const filteredData = data?.filter(item => {
    const value1 = item[label] as string;
    const value2 = item[lable2] as string;

    return (
      (typeof value1 === 'string' &&
        value1.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (typeof value2 === 'string' &&
        value2.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  });

  const handleSelect = (item: T) => {
    setSelectedItem(item);
    setIsVisible(false);
    onSelect(item);
  };

  function onClose() {
    setIsVisible(!isVisible);
  }
  return (
    <View>
      <TouchableOpacity style={styles.dropdown} onPress={onClose}>
        <Text variant="subText1_500" color={colors.primaryB100}>
          {selectedItem
            ? (selectedItem[label] as string)
            : placeholder || 'Select an item'}
        </Text>
      </TouchableOpacity>

      <Modal
        visible={isVisible}
        transparent
        animationType="fade"
        onRequestClose={onClose}
        onDismiss={onClose}>
        <BlurView style={styles.blurView} blurType="dark" blurAmount={1} />
        <Pressable onPress={onClose} />
        <View style={styles.modal}>
          <Pressable onPress={onClose}>
            <Back fill={colors.icon.black} />
          </Pressable>
          <Spacer height={16} />
          <TextInput
            style={styles.searchInput}
            placeholder={`Search by ${label.toString()}...`}
            value={searchTerm}
            onChangeText={text => setSearchTerm(text)}
          />
          <FlatList
            data={filteredData}
            keyExtractor={item => item[value] as string}
            renderItem={({item}) => {
              return (
                <Pressable
                  style={styles.item}
                  onPress={() => handleSelect(item)}>
                  <Text variant="subText1">{item[lable2] as string}</Text>
                  <Text variant="subText1">{item[label] as string}</Text>
                </Pressable>
              );
            }}
          />
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  dropdown: {
    backgroundColor: colors.primaryB50,
    borderRadius: 100,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  blurView: {
    ...StyleSheet.absoluteFillObject,
  },

  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  modal: {
    marginTop: 100,
    marginHorizontal: 20,
    backgroundColor: colors.white,
    borderRadius: 10,
    padding: 20,
    maxHeight: 400,
  },
  searchInput: {
    height: 40,
    borderColor: '#ccc',
    borderWidth: 1,
    marginBottom: 10,
    paddingLeft: 10,
    borderRadius: 5,
  },
  item: {
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
});
