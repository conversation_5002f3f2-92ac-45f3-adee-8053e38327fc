import {API_URL_DEBUG, API_URL_RELEASE} from '@env';
import {DocumentPickerResponse} from 'react-native-document-picker';
import {PhotoFile} from 'react-native-vision-camera';

const API_URL = __DEV__ ? API_URL_DEBUG : API_URL_RELEASE;
console.log(API_URL);

//use for account create first time
export async function postUserOtp(phone: string) {
  const resp = await fetch(`${API_URL}/user/otp`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone}),
  });

  return resp;
}

export async function forwardMessage(
  media_id: string | null,
  message: string,
  numbers: string[],
  type: string,
) {
  const resp = await fetch(`${API_URL}/chat/forward`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({media_id, message, numbers, type}),
  });

  return resp;
}

//login ke liya
export async function userLogin(phone: string, password: string) {
  console.log(API_URL, 'API_URL');

  return await fetch(`${API_URL}/user/login`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone, password}),
  });
}

//signup
export async function userCreateAccount(
  phone: string,
  password: string,
  otp: string,
  name: string,
) {
  return await fetch(`${API_URL}/user/signup`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone, password, otp, name}),
  });
}

//logout
export async function userLogOut() {
  return await fetch(`${API_URL}/user/logout`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  });
}

//resetpassword
export async function userResetPassword(
  phone: string,
  password: string,
  otp: string,
) {
  return await fetch(`${API_URL}/user/reset-password`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone, password, otp}),
  });
}

//resetOTP
export async function userResetOtp(phone: string) {
  return await fetch(`${API_URL}/user/reset/otp`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone}),
  });
}

//one-click-login
export async function userOneClickLogin() {
  return await fetch(`${API_URL}/user/one-click-login`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  });
}

export async function userRoomChat(
  user_id: string,
  phone: string,
  is_number: boolean,
) {
  return await fetch(`${API_URL}/chat/room`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({
      data: {user_id, phone, is_number},
    }),
  });
}

//New Contact
export async function saveNewContact(name: string, phone: string) {
  return await fetch(`${API_URL}/contact`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone, name}),
  });
}

export async function createRoomForChat(id: string) {
  return await fetch(`${API_URL}/chat/room`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({data: {user_id: id}}),
  });
}

export async function createGroup(
  name: string,
  description: string,
  numbers: string[],
) {
  return await fetch(`${API_URL}/group/create`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({name, description, numbers}),
  });
}

export async function addGroupMembers(chat_id: string, numbers: string[]) {
  return await fetch(`${API_URL}/group/add/${chat_id}`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({numbers}),
  });
}

export async function sendFilAndPhotos(data: DocumentPickerResponse) {
  const body = new FormData();
  body.append('file', data);
  return await fetch(`${API_URL}/media`, {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    body,
  });
}

export async function sendCameraPhoto(photo: PhotoFile) {
  const body = new FormData();
  const fileName = photo.path.split('/').pop();
  const fileUri = `file://${photo.path}`;

  body.append('file', {
    uri: fileUri,
    name: fileName,
    type: 'image/jpeg',
  });
  const response = await fetch(`${API_URL}/media`, {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    body,
  });
  return response;
}

export async function adminLoginOtp(phone: string) {
  const resp = await fetch(`${API_URL}/admin/login/otp`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone}),
  });

  return resp;
}

export async function adminLogin(phone: string, otp: string) {
  const resp = await fetch(`${API_URL}/admin/login`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone, otp}),
  });
  return resp;
}

export async function adminLogOut() {
  return await fetch(`${API_URL}/admin/logout`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  });
}
export async function agentLogin(phone: string, password: string) {
  return await fetch(`${API_URL}/agent/login`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone, password}),
  });
}

export async function agentResendOtp(phone: string) {
  return await fetch(`${API_URL}/agent/reset/otp`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone}),
  });
}

export async function agentResetPassword(
  phone: string,
  password: string,
  otp: string,
) {
  return await fetch(`${API_URL}/agent/reset-password`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({phone, password, otp}),
  });
}

export async function createStoryWithCaption(
  caption: string,
  backgroundColor: string,
) {
  return await fetch(`${API_URL}/story/caption`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({caption, backgroundColor}),
  });
}

export async function createStoryWithGallery(message: DocumentPickerResponse) {
  const body = new FormData();
  body.append('file', message);
  return await fetch(`${API_URL}/story`, {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    body,
  });
}

export async function createStoryWithCamera(photo: PhotoFile) {
  const body = new FormData();
  const fileName = photo.path.split('/').pop();
  const fileUri = `file://${photo.path}`;

  body.append('file', {
    uri: fileUri,
    name: fileName,
    type: 'image/jpeg',
  });
  const response = await fetch(`${API_URL}/story`, {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    body,
  });
  return response;
}

export async function uploadAudio(audioFile: string) {
  const body = new FormData();
  body.append('audio', {
    uri: audioFile,
    type: 'audio/mp3',
    name: 'audio_recording.mp3',
  });

  const response = await fetch(`${API_URL}/media`, {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    body,
  });
  return response;
}

export async function updateFcmToken(fcmToken: string) {
  const resp = await fetch(`${API_URL}/user/update-fcm-token`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({fcmToken}),
  });

  return resp;
}

export async function saveNewAdmin(
  name: string,
  phone: string,
  password: string,
  business_name: string,
) {
  const data = await fetch(`${API_URL}/admin`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({name, phone, password, business_name}),
  });

  return data;
}

export async function saveNewAgent(
  name: string,
  phone: string,
  password: string,
) {
  const data = await fetch(`${API_URL}/agent`, {
    method: 'POST',
    headers: {
      'content-Type': 'application/json',
    },
    body: JSON.stringify({name, phone, password}),
  });

  return data;
}
