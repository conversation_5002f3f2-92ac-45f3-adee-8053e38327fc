import {Alert, Pressable, View} from 'react-native';
import React from 'react';
import {
  BodyCard,
  Text,
  BackBtn,
  Spacer,
  Input,
  Cta,
  KeyboardAvoidingView,
} from '../../components';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../../utils';
import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {z} from 'zod';
import {CloseEye, OpenEye} from '../../assets/icons';
import {colors} from '../../design/colors';
import {
  postUserOtp,
  userResetOtp,
  userLogin,
  agentLogin,
  agentResendOtp,
} from '../../utils/apis/postApis';
import {PasswordSchema} from '../../utils';
import {useStore} from '../../utils/hooks';
import {getUserProfile} from '../../utils/apis';

type PasswordScreenRouteProp = RouteProp<RootStack, 'password'>;

type FormData = z.infer<typeof PasswordSchema>;

export default function Password() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {
    params: {
      phone,
      userExisted,
      name,
      userRole,
      photoUrl,
      isSuperUser,
      isSuperAdmin,
    },
  } = useRoute<PasswordScreenRouteProp>();

  const [showPassword, setShowPassword] = React.useState(true);
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const {setAuthToken, setUser} = useStore(state => ({
    setAuthToken: state.setAuthToken,
    setUser: state.setUser,
  }));

  const {
    control,
    handleSubmit,
    setValue,
    setError,
    formState: {isValid},
  } = useForm<FormData>({
    resolver: zodResolver(PasswordSchema),
    mode: 'onChange',
    shouldUnregister: true,
  });

  function togglePassword() {
    setShowPassword(!showPassword);
  }

  async function fetchUserProfile() {
    try {
      getUserProfile()
        .then(res => res.json())
        .then(data => {
          if (data) {
            setUser({
              phone: data.phone,
              id: data.id,
              role: data.role,
              username: data.username,
              name: data.name,
              superUser: data.isSuperUser,
              superAdmin: data.isSuperAdmin,
              bio: data?.bio,
            });
          }
        });
    } catch (e) {
      Alert.alert('Error');
    }
  }
  function showAlertBoxPopUp(message: string) {
    Alert.alert('Message', `Your OTP is ${message}`);
  }
  async function onSubmit({password}: FormData) {
    try {
      if (phone && !userExisted) {
        setIsSubmitting(true);
        const response = await postUserOtp(phone);
        const data = await response.json();
        showAlertBoxPopUp(data?.message);
        if (data.message) {
          if (name) {
            navigation.navigate('otp', {
              phone: phone,
              password: password,
              name: name,
              userExisted: userExisted,
              photoUrl: photoUrl,
              userRole: userRole,
              isSuperUser: isSuperUser,
              isSuperAdmin: isSuperAdmin,
            });
          }
        }
      } else if (userRole === 'agent') {
        const res = await agentLogin(phone, password);
        if (!res.ok) {
          setError('password', {message: 'phone or password is incorrect'});
        } else if (res.ok && res.status === 200) {
          const token = await res.json();
          setAuthToken(token.token);
          fetchUserProfile();
          navigation.replace('tab', {screen: 'chatList'});
        }
      } else if (userRole === 'user') {
        setIsSubmitting(true);
        const response = await userLogin(phone, password);
        if (!response.ok) {
          setError('password', {message: 'phone or password is incorrect'});
        }
        const data = await response.json();
        setAuthToken(data.token);
        fetchUserProfile();
        if (data.message) {
          while (navigation.canGoBack()) {
            navigation.pop();
          }
          navigation.replace('tab', {screen: 'chatList'});
        }
      }
    } catch (e) {
      Alert.alert('Error', 'Something went wrong!');
    } finally {
      setIsSubmitting(false);
      setValue('password', '');
    }
  }

  function navigateToBack() {
    navigation.goBack();
  }

  async function handleForgotPassword() {
    if (phone) {
      try {
        if (userRole === 'admin') {
          const response = await agentResendOtp(phone);
          const otpResponse = await response.json();
          showAlertBoxPopUp(otpResponse?.message);
          navigation.navigate('otp', {
            phone: phone,
            userExisted: userExisted,
            userRole: userRole,
            isSuperUser: isSuperUser,
            isSuperAdmin: isSuperAdmin,
          });
          return;
        }

        const response = await userResetOtp(phone);
        const data = await response.json();
        if (data.message) {
          showAlertBoxPopUp(data.message);

          if (data.message) {
            navigation.navigate('otp', {
              phone: phone,
              userExisted: userExisted,
              userRole: userRole,
              isSuperUser: isSuperUser,
              isSuperAdmin: isSuperAdmin,
            });
          }
        }
      } catch (e) {
        Alert.alert('Error', 'Something went wrong!');
      }
    }
  }
  return (
    <BodyCard padBottom padTop>
      <KeyboardAvoidingView>
        <BackBtn />
        <View>
          <Text variant="H2_500">{!userExisted && 'Create a '}Password</Text>
          <Spacer height={4} />
          <Text variant="subText2" color={colors.text.B40}>
            {!userExisted &&
              'Secure your account with a password. You can use this password to login next time.'}
            {userExisted &&
              `Please enter the password for account with this number ${'\n'} +91 ${phone}. Not you?`}
            {userExisted && (
              <Text
                variant="subText2"
                color={colors.primaryB200}
                onPress={navigateToBack}>
                Change number
              </Text>
            )}
          </Text>
        </View>
        <Spacer height={16} />
        <Input
          control={control}
          name="password"
          placeholder="*********"
          maxLength={20}
          secureTextEntry={showPassword}
          endView={
            <Pressable onPress={togglePassword}>
              {showPassword ? <OpenEye /> : <CloseEye />}
            </Pressable>
          }
        />
        {userExisted ? (
          <>
            <Spacer height={8} />
            <Pressable onPress={handleForgotPassword}>
              <Text variant="subText2" color={colors.primaryB200}>
                Forgot password?
              </Text>
            </Pressable>
          </>
        ) : (
          <Text variant="subText2" color={colors.text.B40}>
            Choose a password with alpha-numeric characters. It should include
            special characters as well.
          </Text>
        )}
      </KeyboardAvoidingView>
      <Cta
        title="continue"
        disabled={!isValid || isSubmitting}
        onPress={handleSubmit(onSubmit)}
      />
    </BodyCard>
  );
}
