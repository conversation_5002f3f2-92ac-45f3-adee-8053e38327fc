import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  View,
  Animated,
  Pressable,
  Alert,
  ToastAndroid,
  StyleSheet,
  RefreshControl,
  TextInput,
  Keyboard,
} from 'react-native';
import {
  BodyCard,
  Fab,
  Header,
  Input,
  MassageCard,
  Spacer,
  Text,
} from '../../components';
import {SearchIcon, AddContact, Massage, NewAction} from '../../assets/icons';
import {colors} from '../../design/colors';
import {useForm} from 'react-hook-form';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {ContactList, RootStack} from '../../utils';
import {FlatList} from 'react-native-gesture-handler';
import {zodResolver} from '@hookform/resolvers/zod';
import {z} from 'zod';
import {ChatSearchSchema} from '../../utils/validationSchema';
import {createRoomFor<PERSON>hat, getContactList} from '../../utils/apis';
import {heightPercentageToDP} from 'react-native-responsive-screen';
import {useStore} from '../../utils/hooks';

type FormData = z.infer<typeof ChatSearchSchema>;

export default function Contact() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {control, handleSubmit, setValue} = useForm<FormData>({
    resolver: zodResolver(ChatSearchSchema),
    defaultValues: {search: ''},
  });

  const {contactLists, setContactLists} = useStore(state => ({
    contactLists: state.contactLists,
    setContactLists: state.setContactLists,
  }));

  const [showAdditionalFabs, setShowAdditionalFabs] = useState(false);
  const [fullContactList, setFullContactList] = useState<
    ContactList[] | undefined
  >(undefined);
  const [refreshing, setRefreshing] = useState(false);
  const inputRef = useRef<TextInput>(null);
  const addContactAnim = useRef(new Animated.Value(0)).current;
  const chatWithNumberAnim = useRef(new Animated.Value(0)).current;

  function navigateToNewContact() {
    navigation.navigate('newContact');
  }

  function navigateToChatWithNumber() {
    navigation.navigate('chatWithNumber');
  }

  function toggleFabs() {
    setShowAdditionalFabs(!showAdditionalFabs);
  }

  function handleContactSearch({search}: FormData) {
    if (!Keyboard.isVisible()) {
      inputRef.current?.focus();
    }
    const filteredContacts = contactLists.filter(
      contact =>
        contact.name.toLowerCase().includes(search.toLowerCase()) ||
        contact.phone.toLowerCase().includes(search.toLowerCase()),
    );
    setFullContactList(filteredContacts);
  }

  const onChangeSearch = (search: string) => {
    setValue('search', search);
    if (search === '') {
      fullContactList && setContactLists(fullContactList);
    } else {
      const filteredContacts = contactLists.filter(
        contact =>
          contact.name.toLowerCase().includes(search.toLowerCase()) ||
          contact.phone.toLowerCase().includes(search.toLowerCase()),
      );

      setContactLists(filteredContacts);
    }
  };

  const navigateToChat = React.useCallback(
    (id: string, img: string | null, name: string) => {
      navigation.navigate('chats', {
        id,
        img,
        name,
        chatType: 'user',
      });
      setValue('search', ''); // Clear search input
    },
    [navigation, setValue],
  );

  async function handlePress(id: string, img: string | null, name: string) {
    if (id === '') {
      return ToastAndroid.showWithGravityAndOffset(
        'This contact is not an N2 Chat user',
        ToastAndroid.SHORT,
        ToastAndroid.BOTTOM,
        25,
        50,
      );
    } else {
      try {
        const response = await createRoomForChat(id);
        const data = await response.json();
        navigateToChat(data.id, img, name);
      } catch (error) {
        Alert.alert('Error', 'Something went wrong');
      }
    }
  }

  useEffect(() => {
    Animated.parallel([
      Animated.timing(addContactAnim, {
        toValue: showAdditionalFabs ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(chatWithNumberAnim, {
        toValue: showAdditionalFabs ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [showAdditionalFabs]);

  const fetchContactList = useCallback(() => {
    setRefreshing(true);
    getContactList()
      .then(res => {
        if (res.status === 500) {
          Alert.alert('Error', 'Something went wrong. Try again later.');
        }
        return res.json();
      })
      .then(response => {
        setFullContactList(response);
        setContactLists(response);
        setRefreshing(false);
      })
      .catch(error => {
        console.log(error);
        Alert.alert('Error', 'Something went wrong, try again later!');
      });

    setValue('search', '');
    return () => {
      setValue('search', '');
      setFullContactList(contactLists);
    };
  }, [setValue]);

  useFocusEffect(fetchContactList);

  return (
    <BodyCard padTop>
      <Header title="Contacts" showBadgesIcon={false} />
      <View style={{flex: 1}}>
        <Input
          ref={inputRef}
          control={control}
          name="search"
          placeholder="Search by name"
          border={false}
          placeholderTextColor={colors.primaryB100}
          onChangeText={onChangeSearch}
          endView={
            <Pressable
              hitSlop={{top: 12, bottom: 12, right: 16}}
              onPress={handleSubmit(handleContactSearch)}>
              <SearchIcon />
            </Pressable>
          }
          inputStyle={{backgroundColor: colors.primaryB50}}
        />

        <FlatList
          refreshControl={
            <RefreshControl
              refreshing={refreshing && !!contactLists}
              onRefresh={fetchContactList}
            />
          }
          data={contactLists}
          showsVerticalScrollIndicator={false}
          renderItem={({item}) => (
            <MassageCard
              loading={refreshing || !contactLists}
              id={item?.contact_id}
              img={item?.contact_user?.avatar?.url ?? null}
              name={item?.name}
              phone={item.phone}
              onPress={handlePress}
              username={item?.contact_user?.username}
              massageCount={0}
              bio={'Bio'}
            />
          )}
          keyExtractor={item => item.id.toString()}
          ItemSeparatorComponent={() => <Spacer height={12} />}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text variant="subText1" center color={colors.text.B100}>
                No contacts found!{'\n'}Please add a contact to start a chat
              </Text>
            </View>
          }
        />
      </View>
      {showAdditionalFabs && (
        <>
          <Animated.View
            style={{
              position: 'absolute',
              bottom: 80,
              right: 0,
              opacity: addContactAnim,
              transform: [
                {
                  translateY: addContactAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [20, 0],
                  }),
                },
                {
                  scale: addContactAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.8, 1],
                  }),
                },
              ],
            }}>
            <Fab onPress={navigateToNewContact} icon={<AddContact />} />
          </Animated.View>
          <Animated.View
            style={{
              position: 'absolute',
              bottom: 160,
              right: 0,
              opacity: chatWithNumberAnim,
              transform: [
                {
                  translateY: chatWithNumberAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [40, 0],
                  }),
                },
                {
                  scale: chatWithNumberAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.8, 1],
                  }),
                },
              ],
            }}>
            <Fab
              onPress={navigateToChatWithNumber}
              icon={<Massage fill={colors.icon.white} />}
            />
          </Animated.View>
        </>
      )}
      <Fab onPress={toggleFabs} icon={<NewAction />} />
    </BodyCard>
  );
}

const styles = StyleSheet.create({
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: heightPercentageToDP(20),
  },
});
