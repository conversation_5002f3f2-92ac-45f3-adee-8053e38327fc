import {
  Image,
  View,
  StyleSheet,
  TouchableOpacity,
  Pressable,
} from 'react-native';
import React, {useRef, useState} from 'react';
import {
  BodyCard,
  Text,
  BackBtn,
  Spacer,
  Cta,
  Input,
  KeyboardAvoidingView,
  BottomSheet,
  Row,
  Camera as CameraComp,
} from '../../components';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {ProfileSetupSchema, RootStack} from '../../utils';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {useForm} from 'react-hook-form';
import {colors} from '../../design/colors';
import {Camera, CameraAdd, Gallery} from '../../assets/icons';
import DocumentPicker, {
  DocumentPickerResponse,
  types,
} from 'react-native-document-picker';
import {openSettings, PERMISSIONS, request} from 'react-native-permissions';
import {CustomCameraRef} from '../../components/Camera';
import {PhotoFile} from 'react-native-vision-camera';
import {showTostMessage} from '../../utils/helpers';

type FormData = z.infer<typeof ProfileSetupSchema>;
type ProfileScreenRouteProp = RouteProp<RootStack, 'password'>;

export default function ProfileSetup() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const [showModal, setShowModal] = React.useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [photoUrl, setPhotoUrl] = useState<
    PhotoFile | DocumentPickerResponse
  >();

  const {
    params: {phone, userExisted, isSuperUser, userRole},
  } = useRoute<ProfileScreenRouteProp>();
  const cameraRef = useRef<CustomCameraRef>(null);
  const {
    control,
    handleSubmit,
    formState: {isValid},
  } = useForm<FormData>({
    resolver: zodResolver(ProfileSetupSchema),
    mode: 'onChange',
    defaultValues: {
      name: '',
    },
  });

  const toggleManualModal = React.useCallback(() => {
    setShowModal(showing => !showing);
  }, []);

  async function handleGallery() {
    try {
      const result = await DocumentPicker.pick({
        type: [types.images],
      });
      console.log(result, 'result');
      setPhotoUrl(result[0]);
    } catch (error) {
      if (!DocumentPicker.isCancel(error)) {
        showTostMessage('Failed to pick image');
      }
    } finally {
      setShowModal(false);
    }
  }

  async function requestCameraPermission() {
    const permission = await request(PERMISSIONS.ANDROID.CAMERA);

    if (permission === 'blocked') {
      openSettings();
    }
  }

  async function toggleCamera() {
    setShowCamera(showing => !showing);
    setShowModal(false);
  }

  const handlePhotoTaken = async (photo: PhotoFile) => {
    setPhotoUrl(photo);
    toggleCamera();
  };

  async function onSubmit({name}: FormData) {
    navigation.navigate('password', {
      name: name,
      phone: phone,
      userExisted: userExisted,
      userRole: userRole,
      isSuperUser: isSuperUser,
      photoUrl: photoUrl,
      isSuperAdmin: false,
    });
  }

  React.useEffect(() => {
    requestCameraPermission();
  }, []);

  return (
    <>
      {showCamera && (
        <CameraComp
          ref={cameraRef}
          onPhotoTaken={handlePhotoTaken}
          toggleCamera={toggleCamera}
        />
      )}
      <BodyCard padTop padBottom>
        <BackBtn />
        <Spacer height={40} />
        <KeyboardAvoidingView>
          <View>
            <Text variant="H2_500">
              You did it 🙌{'\n'}What should we call you?
            </Text>
            <Spacer height={4} />
            <Text variant="subText2" color={colors.text.B40}>
              You can update your profile avatar and add your username below.
              Please add your name to continue.
            </Text>
          </View>
          <View style={styles.imageContainer}>
            <Image
              style={styles.image}
              source={
                photoUrl
                  ? 'path' in photoUrl
                    ? {uri: `file://${photoUrl.path}`}
                    : {uri: photoUrl.uri}
                  : require('../../assets/images/avatar.png')
              }
            />
            <Spacer height={12} />
            <TouchableOpacity
              activeOpacity={0.6}
              style={styles.cameraIconWrapper}
              onPress={toggleManualModal}>
              <CameraAdd
                width={18}
                height={18}
                fill={colors.icon.white}
                style={{alignSelf: 'center'}}
              />
            </TouchableOpacity>
          </View>
          <View>
            <Input control={control} name="name" label="My name is:" />
          </View>
        </KeyboardAvoidingView>
        <Cta
          title="Save & Continue"
          onPress={handleSubmit(onSubmit)}
          disabled={!isValid}
        />
      </BodyCard>
      <BottomSheet visible={showModal} onClose={toggleManualModal}>
        <View style={styles.bottomSheet}>
          <Row between>
            <View>
              <Pressable style={styles.attachments} onPress={handleGallery}>
                <Gallery />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Gallery
              </Text>
            </View>
            <View>
              <Pressable style={styles.attachments} onPress={toggleCamera}>
                <Camera height={24} width={24} fill={colors.icon.B100} />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Camera
              </Text>
            </View>
          </Row>
        </View>
      </BottomSheet>
    </>
  );
}

const styles = StyleSheet.create({
  imageContainer: {
    width: 86,
    height: 86,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 43, // make it circular
  },
  cameraIconWrapper: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    backgroundColor: colors.primaryB200,
    borderRadius: 20,
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomSheet: {
    height: 200,
    width: '100%',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    shadowColor: 'black',
    elevation: 3,
    shadowRadius: 10,
    shadowOpacity: 1,
    backgroundColor: colors.white,
  },
  attachments: {
    padding: 20,
    backgroundColor: colors.primaryB50,
    borderRadius: 100,
  },
});
