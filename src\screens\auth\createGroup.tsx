import {
  Image,
  Keyboard,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
  Alert as RNAlert,
} from 'react-native';
import React, {useRef, useState} from 'react';
import {
  BackBtn,
  BodyCard,
  BottomSheet,
  Cta,
  Input,
  MassageCard,
  Row,
  Spacer,
  Text,
  Camera as CameraComp,
  Alert,
} from '../../components';
import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {ContactList, NewCreateGroupSchema, RootStack} from '../../utils';
import {z} from 'zod';
import {
  CreateGroups,
  Delete,
  Emoji,
  CameraAdd,
  AddMembers,
  Gallery,
  Camera,
} from '../../assets/icons';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {FlatList} from 'react-native-gesture-handler';
import {createGroup, updateGroupAvatar} from '../../utils/apis';
import {colors} from '../../design/colors';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import EmojiSelector, {Categories} from 'react-native-emoji-selector';
import {PhotoFile} from 'react-native-vision-camera';
import {openSettings, PERMISSIONS, request} from 'react-native-permissions';
import DocumentPicker, {
  DocumentPickerResponse,
  types,
} from 'react-native-document-picker';
import {showTostMessage} from '../../utils/helpers';
import {CustomCameraRef} from '../../components/Camera';

type FormData = z.infer<typeof NewCreateGroupSchema>;
type CreateGroupRouteProp = RouteProp<RootStack, 'createGroup'>;
export default function CreateGroup() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {
    params: {selectContact: selectContact},
  } = useRoute<CreateGroupRouteProp>();

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    formState: {isValid},
  } = useForm<FormData>({
    resolver: zodResolver(NewCreateGroupSchema),
  });

  const [contacts, setContacts] = useState<ContactList[]>(selectContact);
  const [numbers, setNumbers] = useState<string[]>(
    selectContact?.map(item => item.phone) || [],
  );
  const [selectedUserIndex, setSelectedUserIndex] = React.useState<
    number | null
  >(null);
  function HandleAddMoreMember() {
    navigation.goBack();
  }

  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [showEmoji, setShowEmoji] = useState(false);
  const [showModal, setShowModal] = React.useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [photoUrl, setPhotoUrl] = useState<
    PhotoFile | DocumentPickerResponse
  >();
  const cameraRef = useRef<CustomCameraRef>(null);

  function handleRemove() {
    setContacts(prevContacts =>
      prevContacts.filter((_, i) => i !== selectedUserIndex),
    );
    setNumbers(prevNumbers =>
      prevNumbers.filter((_, i) => i !== selectedUserIndex),
    );
    setSelectedUserIndex(null);
  }
  function toggleAlert() {
    setSelectedUserIndex(null);
  }
  const handlePress = (index: number) => {
    setSelectedUserIndex(index);
  };
  const renderItem = ({item, index}: {item: ContactList; index: number}) => (
    <MassageCard
      loading={false}
      id={item?.contact_id}
      img={null}
      name={item?.name}
      phone={item.phone}
      username={item.username}
      massageStatus="delivered"
      massageCount={0}
      onPress={() => handlePress(index)}
      endView={<Delete />}
      bio="bio"
    />
  );
  const toggleEmoji = React.useCallback(() => {
    if (Keyboard.isVisible()) {
      Keyboard.dismiss();
    }

    setShowEmoji(!showEmoji);
  }, [showEmoji]);
  const onEmojiSelected = React.useCallback((emoji: string) => {
    setValue(
      'name',
      (getValues('name') === undefined ? '' : getValues('name')) + emoji,
    );
  }, []);

  async function createNewGroup({name, description}: FormData) {
    if (selectContact) {
      try {
        setIsSubmitting(true);
        const response = await createGroup(name, description, numbers);
        if (response.ok && response.status == 200) {
          // const groupId = await response.json();
          // if (photoUrl) {
          //   await updateGroupAvatar(groupId.id, photoUrl);
          //   navigation.navigate('GroupCongrats');
          // } else {
          navigation.navigate('GroupCongrats');
          // }
        } else {
          showTostMessage('Please Select valid Contact');
        }
      } catch (e) {
        RNAlert.alert('Error', 'Something went wrong!');
      } finally {
        setIsSubmitting(false);
      }
    }
  }

  const toggleManualModal = React.useCallback(() => {
    setShowModal(showing => !showing);
  }, [showModal]);
  async function handleGallery() {
    try {
      const result = await DocumentPicker.pick({
        type: [types.images],
      });
      setPhotoUrl(result[0]);
    } catch (error) {
      if (!DocumentPicker.isCancel(error)) {
        showTostMessage('Something went wrong!');
      }
    } finally {
      setShowModal(false);
    }
  }

  async function requestCameraPermission() {
    const permission = await request(PERMISSIONS.ANDROID.CAMERA);

    if (permission === 'blocked') {
      openSettings();
    }
  }

  async function toggleCamera() {
    setShowCamera(showing => !showing);
    setShowModal(false);
  }

  const handlePhotoTaken = async (photo: PhotoFile) => {
    setPhotoUrl(photo);
    toggleCamera();
  };

  React.useEffect(() => {
    function onKeyboardDidShow() {
      if (showEmoji) {
        setShowEmoji(!showEmoji);
      }
    }
    const showSubscription = Keyboard.addListener(
      'keyboardDidShow',
      onKeyboardDidShow,
    );
    return () => {
      showSubscription.remove();
    };
  }, [showEmoji]);
  React.useEffect(() => {
    requestCameraPermission();
  }, []);

  return (
    <>
      {showCamera && (
        <CameraComp
          ref={cameraRef}
          onPhotoTaken={handlePhotoTaken}
          toggleCamera={toggleCamera}
        />
      )}
      <BodyCard padTop padBottom>
        <Row>
          <BackBtn />
          <Text variant="H2_500" middle>
            Create group
          </Text>
        </Row>
        <View style={styles.centeredContainer}>
          <Row style={styles.imageContainer}>
            <Image
              alt={''}
              source={
                photoUrl
                  ? 'path' in photoUrl
                    ? {uri: `file://${photoUrl.path}`}
                    : {uri: photoUrl.uri}
                  : require('../../assets/images/avatar.png')
              }
              style={styles.image}
            />
            <Spacer height={12} />
            {/* <TouchableOpacity
              activeOpacity={0.6}
              style={styles.cameraIconWrapper}
              onPress={toggleManualModal}>
              <CameraAdd
                width={18}
                height={18}
                fill={colors.icon.white}
                style={{
                  alignSelf: 'center',
                  backgroundColor: colors.primaryB200,
                }}
              /> 
            </TouchableOpacity>*/}
          </Row>
        </View>
        <Input
          control={control}
          name="name"
          label="Group name"
          placeholder="name"
          endView={
            <>
              <Pressable onPress={toggleEmoji}>
                <Emoji height={24} width={24} />
              </Pressable>
            </>
          }
        />
        <Spacer height={24} />
        <Input
          inputStyle={{height: 120}}
          control={control}
          name="description"
          label="Group description"
          multiline
          placeholder="Group description"
        />

        <Pressable onPress={HandleAddMoreMember}>
          <Row center>
            <View
              style={{
                backgroundColor: colors.primaryB200,
                borderRadius: 100,
                padding: 12,
              }}>
              <AddMembers style={{alignSelf: 'center'}} fill={colors.white} />
            </View>
            <Text variant="subText1_500"> Add members</Text>
          </Row>
        </Pressable>

        <Spacer height={12} />

        <FlatList
          data={contacts}
          showsVerticalScrollIndicator={false}
          renderItem={renderItem}
          keyExtractor={item => item.id.toString()}
          ItemSeparatorComponent={() => <Spacer height={4} />}
        />
        <Cta
          title="Create group"
          disabled={!isValid || isSubmitting || contacts.length === 0}
          icon={<CreateGroups />}
          onPress={handleSubmit(createNewGroup)}
        />
      </BodyCard>

      <BottomSheet visible={showModal} onClose={toggleManualModal}>
        <View style={styles.bottomSheet}>
          <Row between>
            <View>
              <Pressable style={styles.attachments} onPress={handleGallery}>
                <Gallery />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Gallery
              </Text>
            </View>
            <View>
              <Pressable style={styles.attachments} onPress={toggleCamera}>
                <Camera height={24} width={24} fill={colors.icon.B100} />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Camera
              </Text>
            </View>
          </Row>
        </View>
      </BottomSheet>

      {showEmoji && (
        <View style={{flex: 1, backgroundColor: colors.white}}>
          <EmojiSelector
            category={Categories.all}
            onEmojiSelected={onEmojiSelected}
            showSearchBar={false}
            columns={9}
          />
        </View>
      )}
      {selectedUserIndex !== null && (
        <Alert
          visible={selectedUserIndex !== null}
          message="Do you really want to remove user?"
          onClose={toggleAlert}
          onConfirm={handleRemove}
          rightText="Yes, Remove"
        />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  centeredContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: 'auto',
  },
  imageContainer: {
    width: 86,
    height: 86,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 16,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 43,
  },
  cameraIconWrapper: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    backgroundColor: colors.primaryB200,
    borderRadius: 20,
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomSheet: {
    height: 200,
    width: '100%',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    shadowColor: 'black',
    elevation: 3,
    shadowRadius: 10,
    shadowOpacity: 1,
    backgroundColor: colors.white,
  },
  attachments: {
    padding: 20,
    backgroundColor: colors.primaryB50,
    borderRadius: 100,
  },
});
