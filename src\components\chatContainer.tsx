import React from 'react';
import {StyleSheet, View} from 'react-native';
import Text from './Text';
import Image from './Image';
import Row from './Row';
import Spacer from './Spacer';
import {colors} from '../design/colors';
import ReadReceipt from './readReceipt';
import {ChatRow} from '../utils';
import {getTimeAndMin} from '../utils/helpers';
import Media from './media';
import {Skeleton} from 'moti/skeleton';
import {widthPercentageToDP} from 'react-native-responsive-screen';

interface Props {
  chatRow: ChatRow;
  massageType: 'sender' | 'receiver';
  massageStatus: 'sent' | 'delivered' | 'seen';
  chatType: 'user' | 'group';
  loading: boolean;
}

export default function ChatContainer({
  chatRow,
  massageType,
  massageStatus,
  chatType,
  loading,
}: Props) {
  return (
    <Row reverse={massageType === 'sender'} center>
      <Skeleton.Group show={loading}>
        {chatType === 'group' && (
          <>
            <View
              style={{
                alignSelf: massageType === 'sender' ? 'flex-end' : 'flex-start',
              }}>
              <Skeleton radius="round" colorMode="light">
                <Image
                  uri={chatRow?.sender?.avatar?.url || null}
                  alt={chatRow?.sender?.username}
                />
              </Skeleton>
            </View>
            <Spacer width={12} />
          </>
        )}
        <Skeleton colorMode="light">
          <View
            style={[
              {
                maxWidth:
                  chatType === 'group' ? widthPercentageToDP(75) : undefined,
              },
              styles.massageContainer,
              massageType === 'sender' && styles.sender,
            ]}>
            <View>
              {chatRow?.media && (
                <View
                  style={{
                    maxWidth:
                      chatType === 'group'
                        ? widthPercentageToDP(75)
                        : undefined,
                  }}>
                  <Media chatType={chatType} data={chatRow.media} />
                </View>
              )}
              {chatRow.media === null && (
                <>
                  {massageType === 'receiver' && chatType === 'group' && (
                    <>
                      <Row spread>
                        <Text variant="subText2" color={colors.text.B20}>
                          {chatRow?.sender?.in_contacts
                            ? chatRow?.sender?.in_contacts[0]?.saved_name
                            : chatRow?.sender?.username}
                        </Text>
                        <Spacer width={8} />
                        <Text variant="subText2" color={colors.text.B20}>
                          {chatRow?.sender?.phone}
                        </Text>
                      </Row>
                      <Spacer height={4} />
                    </>
                  )}

                  <Text variant="subText2">{chatRow?.message}</Text>
                </>
              )}
              <View style={styles.iconContainer}>
                <ReadReceipt
                  massageStatus={massageStatus}
                  massageType={massageType}
                />
                <Text>{getTimeAndMin(chatRow.createdAt)}</Text>
              </View>
            </View>
          </View>
        </Skeleton>
      </Skeleton.Group>
    </Row>
  );
}

const styles = StyleSheet.create({
  massageContainer: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopLeftRadius: 0,
    borderRadius: 8,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.25,
    shadowRadius: 0,
    elevation: 1,
  },
  sender: {
    backgroundColor: colors.primaryB50,
    borderTopRightRadius: 0,
    borderRadius: 8,
  },
  iconContainer: {
    alignSelf: 'flex-end',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
});
