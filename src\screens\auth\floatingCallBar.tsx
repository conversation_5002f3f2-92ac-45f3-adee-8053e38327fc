import React from 'react';
import {Pressable, StyleSheet, View} from 'react-native';
import {Text} from '../../components';
import {Phone, Speaker, Mute} from '../../assets/icons';
import {colors} from '../../design/colors';
import {WebRTCContext} from '../../utils/providers/WebRtcProvider';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../../utils';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

export const FloatingCallBar: React.FC = () => {
  const {
    endCall,
    answerCall,
    declineCall,
    toggleSpeaker,
    toggleAudio,
    isLoudspeaker,
    isMuted,
    timer,
    isCallActive,
    isIncomingCall,
    callerInfo,
  } = React.useContext(WebRTCContext);

  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const insets = useSafeAreaInsets();

  const navigateToCallScreen = () => {
    navigation.navigate('voiceCall', {
      isIncomingCall: false,
      userName: callerInfo?.userName,
      avatar: callerInfo?.avatar,
    });
  };

  const handleAnswerCall = async () => {
    await answerCall();
    navigateToCallScreen();
  };

  if (!isCallActive) return null;

  if (isIncomingCall && timer === 0) {
    return (
      <View style={[styles.container, {bottom: insets.bottom + 20}]}>
        <Pressable
          onPress={navigateToCallScreen}
          style={styles.callInfoContainer}>
          <Text color={colors.text.primary} variant="subText1_500">
            {callerInfo?.userName || 'Unknown Caller'}
          </Text>
          <Text color={colors.text.warning} variant="subText1_500">
            Incoming call...
          </Text>
        </Pressable>
        <View style={styles.controlsContainer}>
          <Pressable
            style={[styles.iconButton, {backgroundColor: colors.icon.danger}]}
            onPress={declineCall}>
            <Phone fill={colors.icon.white} style={styles.phoneIcon} />
          </Pressable>
          <Pressable
            style={[styles.iconButton, {backgroundColor: colors.success}]}
            onPress={handleAnswerCall}>
            <Phone fill={colors.icon.white} />
          </Pressable>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, {bottom: insets.bottom + 20}]}>
      <Pressable
        onPress={navigateToCallScreen}
        style={styles.callInfoContainer}>
        <Text color={colors.text.primary} variant="subText1_500">
          {callerInfo?.userName || 'Unknown Caller'}
        </Text>
        <Text color={colors.text.warning} variant="subText1_500">
          {timer > 0
            ? `${Math.floor(timer / 60)
                .toString()
                .padStart(2, '0')}:${(timer % 60).toString().padStart(2, '0')}`
            : 'Calling...'}
        </Text>
      </Pressable>
      <View style={styles.controlsContainer}>
        <Pressable
          style={[
            styles.iconButton,
            {
              backgroundColor: isLoudspeaker
                ? colors.icon.B200
                : colors.icon.B10,
            },
          ]}
          onPress={toggleSpeaker}>
          <Speaker fill={isLoudspeaker ? colors.icon.white : colors.icon.B50} />
        </Pressable>
        <Pressable
          style={[
            styles.iconButton,
            {
              backgroundColor: isMuted ? colors.icon.B200 : colors.icon.B10,
            },
          ]}
          onPress={toggleAudio}>
          <Mute fill={isMuted ? colors.icon.PB50 : colors.icon.B50} />
        </Pressable>
        <Pressable
          style={[styles.iconButton, {backgroundColor: colors.icon.danger}]}
          onPress={endCall}>
          <Phone fill={colors.icon.white} style={styles.phoneIcon} />
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'static',
    backgroundColor: colors.primaryB50,
    borderRadius: 28,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    zIndex: 999,
  },
  callInfoContainer: {
    flex: 1,
    marginRight: 8,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  iconButton: {
    padding: 10,
    borderRadius: 100,
    justifyContent: 'center',
    alignItems: 'center',
    width: 40,
    height: 40,
  },
  phoneIcon: {
    transform: [{rotate: '135deg'}],
  },
});
