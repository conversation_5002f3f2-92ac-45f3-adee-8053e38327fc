import React from 'react';
import {
  BackBtn,
  BodyCard,
  Cta,
  Input,
  KeyboardAvoidingView,
  Row,
  Spacer,
  Text,
} from '../../components';
import {useForm} from 'react-hook-form';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../../utils/types';
import {getUserType} from '../../utils/apis/getApis';
import {logInSchema} from '../../utils/validationSchema';
import {Alert} from 'react-native';
import {colors} from '../../design/colors';
import {IndiaFlag} from '../../assets/icons';
import {adminLoginOtp} from '../../utils/apis';

type FormData = z.infer<typeof logInSchema>;

const startView = (
  <Row>
    <IndiaFlag />
    <Spacer width={8} />
    <Text variant="subText1_500">+91</Text>
  </Row>
);
export default function Login() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const {
    control,
    handleSubmit,
    formState: {isValid},
  } = useForm<FormData>({
    resolver: zodResolver(logInSchema),
    mode: 'onChange',
    shouldUnregister: true,
  });

  const handleLogin = async ({phone}: FormData) => {
    try {
      setIsSubmitting(true);
      const resp = await getUserType(phone);
      const response = await resp.json();

      if (response.error) {
        Alert.alert('Error', response);
      } else if (response.userRole === 'admin') {
        const data = await adminLoginOtp(phone);
        const res = await data.json();
        Alert.alert('Message', `Your otp is ${res.message}`);
        navigation.navigate('otp', {
          phone: phone,
          userExisted: response.userExisted,
          userRole: response.userRole,
          isSuperAdmin: response.isSuperAdmin,
          isSuperUser: response.isSuperUser,
        });
      } else if (response.userExisted === false) {
        navigation.navigate('profileSetup', {
          phone: phone,
          userExisted: response.userExisted,
        });
        // existing user
      } else {
        navigation.navigate('password', {
          phone: phone,
          userExisted: response.userExisted,
          isSuperUser: response.isSuperUser,
          userRole: response.userRole,
          isSuperAdmin: response.isSuperAdmin,
        });
      }
    } catch (error) {
      if (error === 'Phone already in use!') {
        Alert.alert('Error', 'Phone already in use!');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <BodyCard padBottom padTop>
      <KeyboardAvoidingView>
        <BackBtn />
        <Text variant="H2_500">What’s your phone number?</Text>
        <Spacer height={8} />
        <Text variant="subText2" color={colors.text.B40}>
          An OTP will be sent to this number, if you’re new here. So please
          enter the number that you have access to.
        </Text>
        <Spacer height={16} />
        <Input
          control={control}
          name="phone"
          placeholder="9999 999 999"
          maxLength={10}
          keyboardType="number-pad"
          startView={startView}
        />
      </KeyboardAvoidingView>

      <Cta
        title="Continue"
        onPress={handleSubmit(handleLogin)}
        disabled={!isValid || isSubmitting}
      />
    </BodyCard>
  );
}
