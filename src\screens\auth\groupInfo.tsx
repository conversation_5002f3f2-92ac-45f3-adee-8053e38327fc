import {
  Image,
  StyleSheet,
  View,
  Pressable,
  FlatList,
  <PERSON><PERSON> as RNA<PERSON><PERSON>,
  TouchableOpacity,
} from 'react-native';
import React, {useRef, useState} from 'react';
import {
  BackBtn,
  BodyCard,
  Cta,
  Input,
  MassageCard,
  Row,
  Spacer,
  Text,
  Alert,
  BottomSheet,
  Camera as CameraComp,
  Fab,
} from '../../components';
import {
  AddMembers,
  Camera,
  CameraAdd,
  Cross,
  Delete,
  Gallery,
  RightFab,
} from '../../assets/icons';
import {colors} from '../../design/colors';
import {zodResolver} from '@hookform/resolvers/zod';
import {
  GroupInfoType,
  GroupParticipants,
  NewCreateGroupSchema,
  RootStack,
} from '../../utils';
import {useForm} from 'react-hook-form';
import {
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {
  exitGroup,
  getGroupInfo,
  removeUserFromGroup,
  updateGroupAvatar,
  updateUserGroupInfo,
} from '../../utils/apis';
import {z} from 'zod';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {showTostMessage} from '../../utils/helpers';
import {useStore} from '../../utils/hooks';
import DocumentPicker, {
  DocumentPickerResponse,
  types,
} from 'react-native-document-picker';
import {PhotoFile} from 'react-native-vision-camera';
import {CustomCameraRef} from '../../components/Camera';
import {openSettings, PERMISSIONS, request} from 'react-native-permissions';
import {
  heightPercentageToDP,
  widthPercentageToDP,
} from 'react-native-responsive-screen';

type GroupInfoParam = RouteProp<RootStack, 'groupInfo'>;

type FormData = z.infer<typeof NewCreateGroupSchema>;

export default function GroupInfo() {
  const {control, handleSubmit, setValue} = useForm<FormData>({
    resolver: zodResolver(NewCreateGroupSchema),
  });
  const [showAlert, setShowAlert] = React.useState(false);
  const [showModal, setShowModal] = React.useState(false);
  const [showExitGroup, setShowExitGroup] = React.useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [photoUrl, setPhotoUrl] = useState<
    PhotoFile | DocumentPickerResponse
  >();
  const cameraRef = useRef<CustomCameraRef>(null);
  const [selectedUserIndex, setSelectedUserIndex] = React.useState<
    number | null
  >(null);
  const [galleryFile, setGalleryFile] = useState<
    DocumentPickerResponse[] | null
  >(null);

  const {userId} = useStore(state => ({userId: state?.user?.id}));
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const [disabled, setIsDisable] = React.useState(true);
  const [grpNumber, setGrpNumber] = React.useState<number[]>([]);

  const {
    params: {chatId},
  } = useRoute<GroupInfoParam>();

  const [groupInfoResponse, setGroupeInfoResponse] =
    React.useState<GroupInfoType>();

  function HandleAddMoreMember() {
    groupInfoResponse?.id &&
      navigation.navigate('groupAddMembers', {
        chatId: groupInfoResponse?.id,
        selectContact: grpNumber,
      });
  }

  function toggleAlert() {
    setShowAlert(prevState => !prevState);
  }

  function toggleExitGroupAlert() {
    setShowExitGroup(prevState => !prevState);
  }

  async function handleRemove() {
    try {
      if (selectedUserIndex === null) return;

      const chat_id =
        groupInfoResponse?.participants[selectedUserIndex]?.chat_id;
      const phone =
        groupInfoResponse?.participants[selectedUserIndex]?.user?.phone;

      if (chat_id && phone) {
        const response = await removeUserFromGroup(chat_id, phone);
        if (response.ok) {
          setGroupeInfoResponse(prevState => {
            if (!prevState) return prevState;
            const updatedParticipants = prevState.participants.filter(
              (_, i) => i !== selectedUserIndex,
            );
            return {
              ...prevState,
              participants: updatedParticipants,
            };
          });
          const updatedIndex = grpNumber.filter(
            (_, index) => index !== selectedUserIndex,
          );
          setGrpNumber(updatedIndex);
          showTostMessage('User removed');
        } else {
          showTostMessage('Failed to remove user');
        }
      }
    } catch (error) {
      RNAlert.alert('Error', 'Something went wrong');
    } finally {
      toggleAlert();
      setSelectedUserIndex(null);
    }
  }

  async function handleDeleteUser(index: number) {
    setSelectedUserIndex(index);
    setShowAlert(true);
  }

  function renderItem({item, index}: {item: GroupParticipants; index: number}) {
    return (
      <MassageCard
        loading={false}
        id={item?.id}
        img={item?.user?.avatar?.url ?? null}
        name={`${item?.contact_name ? item?.contact_name : item?.user?.name}${
          groupInfoResponse?.created_by === item.user.id ? ` (Admin)` : ''
        } `}
        phone={item?.user?.phone}
        username={item?.user?.username}
        massageCount={0}
        bio={'bio'}
        endView={
          groupInfoResponse?.created_by === userId &&
          groupInfoResponse?.created_by !== item.user.id && (
            <Pressable onPress={() => handleDeleteUser(index)}>
              <Delete />
            </Pressable>
          )
        }
      />
    );
  }
  async function handleExitGroup() {
    setShowExitGroup(true);
  }

  async function exitToGroup() {
    try {
      const response = await exitGroup(chatId);
      if (response.ok && response.status == 200) {
        showTostMessage('Group Left');
        navigation.navigate('tab', {screen: 'groups'});
      } else {
        showTostMessage('Something went Wrong');
      }
    } catch (error) {
      showTostMessage('Something went Wrong');
    }
  }
  async function updateGroupInfo({name, description}: FormData) {
    try {
      setIsDisable(true);
      const response = await updateUserGroupInfo(chatId, name, description);
      if (response.ok && response.status == 200) {
        showTostMessage('Group info updated');

        navigation.navigate('tab', {screen: 'groups'});
      } else {
        showTostMessage('Something went Wrong');
      }
    } catch (error) {
      showTostMessage('Something went Wrong');
    } finally {
      setIsDisable(false);
    }
  }

  const toggleManualModal = React.useCallback(() => {
    setShowModal(showing => !showing);
  }, []);

  async function handleGallery() {
    try {
      const result = await DocumentPicker.pick({
        type: [types.images],
      });
      // setPhotoUrl(result[0]);
      setGalleryFile(result);
    } catch (error) {
      if (!DocumentPicker.isCancel(error)) {
        showTostMessage('Something went wrong!');
      }
    } finally {
      setShowModal(false);
    }
  }

  async function requestCameraPermission() {
    const permission = await request(PERMISSIONS.ANDROID.CAMERA);

    if (permission === 'blocked') {
      openSettings();
    }
  }

  async function toggleCamera() {
    setShowCamera(showing => !showing);
    setShowModal(false);
  }

  const handlePhotoTaken = async (photo: PhotoFile) => {
    setPhotoUrl(photo);
    toggleCamera();
    await handleUpdateCameraStory();
  };

  async function handleUpdateCameraStory() {
    if (!photoUrl || !groupInfoResponse?.id) {
      showTostMessage('No photo selected or group ID is missing');
      return;
    }

    try {
      const response = await updateGroupAvatar(groupInfoResponse.id, photoUrl);

      if (response.ok && response.status === 200) {
        showTostMessage('Group avatar updated successfully');
      } else {
        showTostMessage('Failed to update group avatar');
      }
    } catch (error) {
      console.error('Error updating group avatar:', error);
      showTostMessage('Failed to update group avatar');
    }
  }

  async function handelUpdateStory() {
    if (!galleryFile || !groupInfoResponse?.id) {
      showTostMessage('No image selected or group ID is missing.');
      return;
    }
    try {
      const response = await updateGroupAvatar(
        groupInfoResponse.id,
        galleryFile[0],
      );
      if (response.ok && response.status === 200) {
        showTostMessage('Group avatar updated successfully');
        // navigation.navigate('tab', {screen: 'groups'});
      } else {
        showTostMessage('Failed to update group avatar');
      }
    } catch (error) {
      console.log('Error updating group avatar:', error);
      showTostMessage('Something went wrong!');
    }
  }

  useFocusEffect(
    React.useCallback(() => {
      const fetchGroupInfo = async () => {
        try {
          const response = await getGroupInfo(chatId);
          if (response.ok && response.status === 200) {
            const data = await response.json();
            if (data) {
              const gNumbar = data.participants.map(item => item.user.phone);
              setGrpNumber(gNumbar);
              setGroupeInfoResponse(data);
              setValue('name', data?.name);
              setValue('description', data?.description);
            }
          } else {
            showTostMessage('something went wrong');
          }
        } catch (error) {
          RNAlert.alert('Error', 'Something went Wrong');
        }
      };

      fetchGroupInfo();
    }, []),
  );

  React.useEffect(() => {
    requestCameraPermission();
  }, []);

  return (
    <>
      {showCamera && (
        <CameraComp
          ref={cameraRef}
          onPhotoTaken={handlePhotoTaken}
          toggleCamera={toggleCamera}
        />
      )}
      {galleryFile && (
        <>
          <View style={{width: '100%', height: '100%'}}>
            <Image
              resizeMode="cover"
              style={{
                width: widthPercentageToDP(100),
                height: heightPercentageToDP(100),
              }}
              source={{uri: galleryFile[0].uri}}
            />
          </View>
          <Pressable
            style={{
              position: 'absolute',
              top: 36,
              left: 20,
            }}>
            <Cross onPress={() => setGalleryFile(null)} />
          </Pressable>
          <Pressable>
            <Fab icon={<RightFab />} onPress={handelUpdateStory} />
          </Pressable>
        </>
      )}

      <BodyCard padTop padBottom>
        <Row>
          <BackBtn />
          <Text variant="H2_500" middle>
            {groupInfoResponse?.name}
            {'\n'}
            <Text variant="subText2">
              {groupInfoResponse?.participants?.length} members
            </Text>
          </Text>
        </Row>
        <View style={styles.centeredContainer}>
          <Row style={styles.imageContainer}>
            <Image
              alt={''}
              source={
                photoUrl
                  ? 'path' in photoUrl
                    ? {uri: `file://${photoUrl.path}`}
                    : {uri: photoUrl.uri}
                  : groupInfoResponse?.avatar?.url
                  ? {uri: groupInfoResponse.avatar.url}
                  : require('../../assets/images/avatar.png')
              }
              style={styles.image}
            />

            <Spacer height={12} />
            <TouchableOpacity
              activeOpacity={0.6}
              style={styles.cameraIconWrapper}
              onPress={toggleManualModal}>
              <CameraAdd
                width={18}
                height={18}
                fill={colors.icon.white}
                style={{alignSelf: 'center'}}
              />
            </TouchableOpacity>
          </Row>
        </View>
        <Input
          control={control}
          name="name"
          label="Group name"
          placeholder="name"
          onChange={() => setIsDisable(false)}
        />
        <Input
          inputStyle={{height: 80}}
          control={control}
          name="description"
          label="Group description"
          placeholder="Group description"
          onChange={() => setIsDisable(false)}
        />
        {groupInfoResponse?.created_by === userId && (
          <Pressable onPress={HandleAddMoreMember}>
            <Row center>
              <View
                style={{
                  backgroundColor: colors.primaryB200,
                  borderRadius: 100,
                  padding: 12,
                }}>
                <AddMembers style={{alignSelf: 'center'}} fill={colors.white} />
              </View>
              <Text variant="subText1_500"> Add members</Text>
            </Row>
          </Pressable>
        )}
        <Spacer height={12} />
        <FlatList
          data={groupInfoResponse?.participants}
          renderItem={renderItem}
          keyExtractor={item => item.id.toString()}
          showsVerticalScrollIndicator={false}
          ItemSeparatorComponent={() => <Spacer height={4} />}
        />
        {!disabled && (
          <>
            <Spacer height={20} />
            <Cta
              appearance="primary"
              onPress={handleSubmit(updateGroupInfo)}
              title="Update group"
            />
            <Spacer height={4} />
          </>
        )}
        <Cta
          appearance="dangerOutline"
          onPress={handleExitGroup}
          title="Exit group"
        />
        {showAlert && (
          <Alert
            visible={showAlert}
            message="Do you really want to remove user?"
            onClose={toggleAlert}
            onConfirm={handleRemove}
            rightText="Yes, Remove"
          />
        )}
        {showExitGroup && (
          <Alert
            visible={showExitGroup}
            message="Do you really want to Exit Group?"
            onClose={toggleExitGroupAlert}
            onConfirm={exitToGroup}
            rightText="Yes, Exit"
          />
        )}
      </BodyCard>
      <BottomSheet visible={showModal} onClose={toggleManualModal}>
        <View style={styles.bottomSheet}>
          <Row between>
            <View>
              <Pressable style={styles.attachments} onPress={handleGallery}>
                <Gallery />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Gallery
              </Text>
            </View>
            <View>
              <Pressable style={styles.attachments} onPress={toggleCamera}>
                <Camera height={24} width={24} fill={colors.icon.B100} />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Camera
              </Text>
            </View>
          </Row>
        </View>
      </BottomSheet>
    </>
  );
}

const styles = StyleSheet.create({
  centeredContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: 'auto',
  },
  imageContainer: {
    width: 86,
    height: 86,
    marginVertical: 16,
  },
  image: {
    width: 86,
    height: 86,
    borderRadius: 43,
  },
  cameraIconWrapper: {
    position: 'absolute',
    bottom: -3,
    right: 4,
    backgroundColor: colors.primaryB200,
    borderRadius: 20,
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomSheet: {
    height: 200,
    width: '100%',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    shadowColor: 'black',
    elevation: 3,
    shadowRadius: 10,
    shadowOpacity: 1,
    backgroundColor: colors.white,
  },
  attachments: {
    padding: 20,
    backgroundColor: colors.primaryB50,
    borderRadius: 100,
  },
});
