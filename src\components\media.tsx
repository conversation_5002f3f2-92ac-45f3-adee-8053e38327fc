import {
  Image,
  PermissionsAndroid,
  Platform,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import {Media as MediaType} from '../utils';
import Video, {OnBufferData} from 'react-native-video';
import {MEDIA_BASE_URL_DEBUG, MEDIA_BASE_URL_RELEASE} from '@env';
import {Documents, Download} from '../assets/icons';
import {colors} from '../design/colors';
import Text from './Text';
import Row from './Row';
import RNFS from 'react-native-fs';
import VoicePlayer from './VoicePlayer';

interface Props {
  data: MediaType;
  chatType?: 'user' | 'group';
}

const MEDIA_BASE_URL = __DEV__ ? MEDIA_BASE_URL_DEBUG : MEDIA_BASE_URL_RELEASE;

function Media({data}: Props) {
  function onBuffer(e: OnBufferData) {
    console.log(e);
  }

  function onError() {
    // console.log('first', e);
  }

  async function requestStoragePermission() {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      }
      return true;
    } catch (err) {
      console.log(err);
      return false;
    }
  }

  async function handleDownload() {
    const downloadPath = `${RNFS.ExternalDirectoryPath}/${data.name}`;

    try {
      // Download the file
      const result = await RNFS.downloadFile({
        fromUrl: `${MEDIA_BASE_URL}${data.url}`,
        toFile: downloadPath,
      }).promise;

      if (result.statusCode === 200) {
        const fileExists = await RNFS.exists(downloadPath);

        if (fileExists) {
          console.log('File downloaded and exists at:', downloadPath);
        } else {
          console.log('File does not exist after download.');
        }
      } else {
        console.log('Failed to download file:', result.statusCode);
      }
    } catch (error) {
      console.error('Error downloading or checking file:', error);
    }
  }

  React.useEffect(() => {
    requestStoragePermission().then(res => console.log('sssssssss', res));
  }, []);

  return (
    <>
      {data.type === 'image' && (
        <Image
          style={{
            height: 300,
            width: 250,
            objectFit: 'fill',
            borderRadius: 8,
          }}
          source={{
            uri: `${MEDIA_BASE_URL}${data.url}`,
          }}
        />
      )}
      {data.type === 'video' && (
        <Video
          resizeMode="cover"
          paused
          controls
          onError={onError}
          onBuffer={onBuffer}
          style={{
            height: 300,
            width: 250,
          }}
          source={{
            uri: `${MEDIA_BASE_URL}${data.url}`,
          }}
        />
      )}
      {data.type === 'audio' && (
        <VoicePlayer url={`${MEDIA_BASE_URL}${data.url}`} />
      )}
      {data.type === 'application' && (
        <Row
          center
          style={{
            backgroundColor: colors.primaryB75,
            borderRadius: 8,
            paddingVertical: 12,
            paddingHorizontal: 6,
            gap: 12,
          }}>
          <Documents fill={colors.primaryB200} height={24} width={24} />
          <Text color={colors.text.B50}>{data.name}</Text>
          <TouchableOpacity onPress={handleDownload}>
            <Download />
          </TouchableOpacity>
        </Row>
      )}
    </>
  );
}

export default React.memo(Media);
