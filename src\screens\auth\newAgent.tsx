import {View} from 'react-native';
import React from 'react';
import {
  BodyCard,
  Cta,
  Input,
  KeyboardAvoidingView,
  Row,
  Spacer,
  Text,
} from '../../components';
import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {z} from 'zod';
import {NewAgentSchema, RootStack} from '../../utils';
import {saveNewAgent} from '../../utils/apis';
import {IndiaFlag, User, Phone, LockPassword} from '../../assets/icons';
import {showTostMessage} from '../../utils/helpers';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

type FormData = z.infer<typeof NewAgentSchema>;
const startView = (
  <Row>
    <IndiaFlag />
    <Spacer width={8} />
    <Text variant="subText1_500">+91</Text>
  </Row>
);
export default function NewAgent() {
  const {
    control,
    handleSubmit,
    formState: {isValid},
  } = useForm<FormData>({
    resolver: zodResolver(NewAgentSchema),
    mode: 'onChange',
    shouldUnregister: true,
  });
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  async function onSubmit({phone, name, password}: FormData) {
    try {
      const response = await saveNewAgent(name, phone, password);
      const data = await response.json();
      if (response.status === 200 && response.ok) {
        showTostMessage(data.message);
        navigation.goBack();
      } else {
        showTostMessage(data.message);
      }
    } catch (error) {
      showTostMessage('Something went wrong!');
    }
  }
  return (
    <BodyCard padTop padBottom>
      <KeyboardAvoidingView>
        <View>
          <Input
            labelIcon={<User />}
            control={control}
            name="name"
            label="Name"
            placeholder="John"
          />
          <Input
            control={control}
            labelIcon={<Phone />}
            name="phone"
            label="Phone number"
            placeholder="9999 999 999"
            maxLength={10}
            keyboardType="number-pad"
            startView={startView}
          />
          <Input
            labelIcon={<LockPassword />}
            control={control}
            name="password"
            label="Password"
            placeholder="password"
          />
        </View>
      </KeyboardAvoidingView>
      <Cta
        title="Create Agent"
        onPress={handleSubmit(onSubmit)}
        disabled={!isValid}
      />
    </BodyCard>
  );
}
