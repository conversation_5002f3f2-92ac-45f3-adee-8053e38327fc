import * as React from 'react';
import {
  ColorValue,
  Text as RNText,
  StyleProp,
  StyleSheet,
  TextProps,
  TextStyle,
} from 'react-native';
import {colors} from '../design/colors';
import {getFontSize} from '../utils';

interface Props extends TextProps {
  Weight?: 'regular' | 'bold' | 'medium';
  center?: boolean;
  middle?: boolean;
  variant?:
    | 'H1'
    | 'H2'
    | 'subText1'
    | 'subText2'
    | 'body'
    | 'H1_500'
    | 'H2_500'
    | 'subText1_500'
    | 'subText2_500'
    | 'body_500';
  style?: StyleProp<TextStyle>;
  color?: ColorValue;
}

const Text = ({
  children,
  center,
  middle,
  variant = 'body',
  style,
  color,
  ...rest
}: Props) => {
  const getVariantStyle = (variant: Props['variant']) => {
    switch (variant) {
      case 'H1':
        return styles.h1;
      case 'H1_500':
        return styles.h1_500;
      case 'H2':
        return styles.h2;
      case 'H2_500':
        return styles.h2_500;
      case 'body':
        return styles.body;
      case 'body_500':
        return styles.body_500;
      case 'subText1':
        return styles.subText1;
      case 'subText1_500':
        return styles.subText1_500;
      case 'subText2':
        return styles.subText2;
      case 'subText2_500':
        return styles.subText2_500;
      default:
        return;
    }
  };
  return (
    <>
      <RNText
        {...rest}
        style={[
          getVariantStyle(variant),
          center ? styles.centerAlign : null,
          middle ? styles.middleAlign : null,
          {color: color ? color : colors.text.black},
          style,
        ]}>
        {children}
      </RNText>
    </>
  );
};

const styles = StyleSheet.create({
  centerAlign: {
    textAlign: 'center',
  },
  middleAlign: {
    textAlignVertical: 'center',
  },
  h1: {
    fontFamily: 'GoogleSans-Regular',
    fontSize: getFontSize(28),
  },
  h2: {
    fontFamily: 'GoogleSans-Regular',
    fontSize: getFontSize(21),
  },
  h1_500: {
    fontFamily: 'GoogleSans-Medium',
    fontSize: getFontSize(28),
  },
  h2_500: {
    fontFamily: 'GoogleSans-Medium',
    fontSize: getFontSize(21),
  },
  body: {
    fontFamily: 'GoogleSans-Regular',
    fontSize: getFontSize(9),
  },
  body_500: {
    fontFamily: 'GoogleSans-Medium',
    fontSize: getFontSize(9),
  },
  subText1: {
    fontFamily: 'GoogleSans-Regular',
    fontSize: getFontSize(16),
  },
  subText1_500: {
    fontFamily: 'GoogleSans-Medium',
    fontSize: getFontSize(16),
  },
  subText2: {
    fontFamily: 'GoogleSans-Regular',
    fontSize: getFontSize(12),
  },
  subText2_500: {
    fontFamily: 'GoogleSans-Medium',
    fontSize: getFontSize(12),
  },
});
export default Text;
