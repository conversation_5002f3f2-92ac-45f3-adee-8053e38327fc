import {View, KeyboardAvoidingView, Pressable} from 'react-native';
import React from 'react';
import {BackBtn, BodyCard, Spacer, Text} from '../../components';
import {Key, Lock} from '../../assets/icons';
import {colors} from '../../design/colors';

export default function AccountSetting() {
  function navigateToChangePassword() {
    console.log('do later');
  }
  function navigateToPrivacySetting() {
    console.log('do later');
  }
  return (
    <BodyCard padBottom padTop>
      <KeyboardAvoidingView style={{flex: 1}}>
        <BackBtn />
        <View>
          <Text variant="H2_500">Account Settings</Text>
          <Spacer height={8} />
          <Text variant="subText2" color={colors.text.B40}>
            User profile, app preferences and account related settings can be
            accessed here.
          </Text>
        </View>
        <Spacer height={52} />
        <Pressable
          style={{flexDirection: 'row', gap: 10}}
          onPress={navigateToChangePassword}>
          <Key />
          <Text variant="subText1">Change Password</Text>
        </Pressable>
        <Spacer height={30} />
        <Pressable
          style={{flexDirection: 'row', gap: 10}}
          onPress={navigateToPrivacySetting}>
          <Lock />
          <Text variant="subText1">Privacy Settings</Text>
        </Pressable>
      </KeyboardAvoidingView>
    </BodyCard>
  );
}
