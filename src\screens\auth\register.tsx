import React from 'react';
import {Alert, Image} from 'react-native';
import {useForm} from 'react-hook-form';
import Input from '../../components/Input';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {BodyCard, Cta, Row, Spacer, Text} from '../../components';
import {postUserOtp} from '../../utils/apis/postApis';
const SignUpSchema = z.object({
  phone: z
    .string()
    .min(10, {message: 'Phone number must be at least 10 digits'})
    .max(10, {message: 'Phone number must be at least 10 digits'}),
  password: z.string(),
  // .min(8, {message: 'Password must be at least 6 characters'})
  // .max(20),
});
type SignUpSchemaType = z.infer<typeof SignUpSchema>;
export default function Register() {
  const {control, handleSubmit} = useForm<SignUpSchemaType>({
    resolver: zod<PERSON><PERSON>olver(SignUpSchema),
  });

  async function onSubmit({phone}: SignUpSchemaType) {
    try {
      const response = await postUserOtp(phone);
      await response.json();
    } catch (e) {
      Alert.alert('Error', 'Something Went Wrong!');
    }
  }

  return (
    <BodyCard>
      <Row middle>
        <Image
          style={{height: 200, width: 200}}
          source={{uri: 'https://n2chat.com/logo.png'}}
        />
      </Row>
      <Text center variant="H1">
        Create an account
      </Text>
      <Input
        control={control}
        name="phone"
        placeholder="Phone Number"
        keyboardType="number-pad"
        label="Phone Number"
        maxLength={10}
      />

      <Input
        control={control}
        name="password"
        placeholder="Password"
        secureTextEntry
        label="Password"
      />
      <Spacer height={12} />
      <Cta
        appearance="primary"
        title="Register"
        onPress={handleSubmit(onSubmit)}
      />
    </BodyCard>
  );
}
