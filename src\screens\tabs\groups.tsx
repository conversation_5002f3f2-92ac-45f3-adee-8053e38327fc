import React, {useContext, useRef, useState} from 'react';
import {
  View,
  FlatList,
  Pressable,
  StyleSheet,
  RefreshControl,
  TextInput,
  Keyboard,
} from 'react-native';
import {
  BodyCard,
  ChatProfileModal,
  Fab,
  Header,
  Input,
  MassageCard,
  Spacer,
  Text,
} from '../../components';
import {CreateGroups, SearchIcon} from '../../assets/icons';
import {colors} from '../../design/colors';
import {useForm} from 'react-hook-form';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {GroupList, GroupSearchSchema, RootStack} from '../../utils';
import {heightPercentageToDP} from 'react-native-responsive-screen';
import {useStore} from '../../utils/hooks';
import {
  convertIntoLocalTime,
  findUserBio,
  findUserName,
} from '../../utils/helpers';
import {z} from 'zod';
import {WebSocketContext} from '../../utils/providers/WebSocket';

type FormData = z.infer<typeof GroupSearchSchema>;

export default function Groups() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const {groupList, user} = useStore(state => ({
    groupList: state.groupLists,
    user: state.user,
  }));
  const {setCurrentOpenChat, reconnect} = useContext(WebSocketContext);

  const [profileInfo, setProfileInfo] = useState<{
    id: string;
    name: string;
    img: string | null;
    // bio: string;
  } | null>(null);

  const [filteredGroupList, setFilteredGroupList] = useState<GroupList[]>([]);
  const [refreshing] = useState<boolean>(false);
  const [loading] = useState(false);
  const inputRef = useRef<TextInput>(null);
  const {control, handleSubmit, setValue} = useForm<FormData>({
    defaultValues: {search: ''},
  });
  function viewImageModal(
    id: string,
    name: string,
    img: string | null,
    // bio?: string,
  ) {
    setProfileInfo({id, name, img});
  }
  function navigateToCreateNewGroup() {
    navigation.navigate('newGroup');
  }

  const navigateToChat = React.useCallback(
    (id: string, img: string | null, name: string) => {
      setCurrentOpenChat(prevState => ({
        ...prevState,
        id,
        chatType: 'group',
      }));

      navigation.navigate('chats', {
        id,
        img,
        name,
        chatType: 'group',
      });
      setValue('search', '');
    },
    [navigation, setCurrentOpenChat, setValue],
  );

  function handleGroupSearch({search}: FormData) {
    if (!Keyboard.isVisible()) {
      inputRef.current?.focus();
    }
    const filteredGroups = groupList.filter(group =>
      group.name.toLowerCase().includes(search.toLowerCase()),
    );

    setFilteredGroupList(filteredGroups);
  }

  function onChangeSearch(search: string) {
    setValue('search', search);

    if (search === '') {
      setFilteredGroupList(groupList);
    } else {
      const filteredGroups = groupList.filter(group =>
        group.name.toLowerCase().includes(search.toLowerCase()),
      );
      setFilteredGroupList(filteredGroups);
    }
  }
  function onPressStartChat() {
    profileInfo &&
      navigateToChat(profileInfo?.id, profileInfo?.img, profileInfo?.name),
      setProfileInfo(null);
  }

  useFocusEffect(
    React.useCallback(() => {
      setFilteredGroupList(groupList);
    }, [groupList]),
  );

  return (
    <>
      <BodyCard padTop>
        <Header
          title="Groups"
          //  onPressThreeDots={() => console.log('first')}
        />
        <Input
          ref={inputRef}
          control={control}
          name="search"
          placeholder="Search"
          placeholderTextColor={colors.primaryB100}
          border={false}
          onChangeText={onChangeSearch}
          endView={
            <Pressable
              hitSlop={{top: 12, bottom: 12, right: 16}}
              onPress={handleSubmit(handleGroupSearch)}>
              <SearchIcon />
            </Pressable>
          }
          inputStyle={{backgroundColor: colors.primaryB50}}
        />

        <FlatList
          data={filteredGroupList}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={reconnect} />
          }
          renderItem={({item}) => {
            return (
              <MassageCard
                loading={loading || filteredGroupList === undefined}
                id={item.id}
                img={item?.avatar?.url ?? null}
                name={item?.name}
                lastSeen={convertIntoLocalTime(
                  item?.messages[0]?.createdAt ?? item?.createdAt,
                )}
                lastMsg={
                  item?.messages[0]?.message
                    ? `${findUserName(item?.participants, user?.id)} ~ ${
                        item?.messages[0]?.message
                      }`
                    : ''
                }
                agent={false}
                massageStatus={
                  item?.messages[0]?.message_read ? 'seen' : 'delivered'
                }
                massageCount={item?.unread_count}
                onPressImage={viewImageModal}
                onPress={navigateToChat}
                bio={findUserBio(item?.participants, user?.id)}
              />
            );
          }}
          keyExtractor={item => item.id.toString()}
          ItemSeparatorComponent={() => <Spacer height={12} />}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text variant="subText1" center color={colors.text.B100}>
                No groups found!{'\n'}Create a group to start a group chat
              </Text>
            </View>
          }
        />
        <Fab onPress={navigateToCreateNewGroup} icon={<CreateGroups />} />
      </BodyCard>
      <ChatProfileModal
        profileInfo={profileInfo}
        onPressBack={() => {
          setProfileInfo(null);
        }}
        onPressStartChat={onPressStartChat}
      />
    </>
  );
}

const styles = StyleSheet.create({
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: heightPercentageToDP(20),
  },
});
