import {
  Image,
  View,
  StyleSheet,
  TouchableOpacity,
  Pressable,
  Alert,
} from 'react-native';
import React, {useEffect, useRef, useState} from 'react';
import {
  BodyCard,
  Text,
  BackBtn,
  Spacer,
  Cta,
  KeyboardAvoidingView,
  Row,
  Input,
  Camera as CameraComp,
  BottomSheet,
} from '../../components';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {useForm} from 'react-hook-form';
import {colors} from '../../design/colors';
import {useStore} from '../../utils/hooks';
import {
  ContactTabIcon,
  CameraAdd,
  Phone,
  Gallery,
  Camera,
} from '../../assets/icons';
import {profileSettingSchema, RootStack} from '../../utils';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {
  getUserProfile,
  updateUserAvatar,
  updateUserProfile,
} from '../../utils/apis';
import DocumentPicker, {
  DocumentPickerResponse,
  types,
} from 'react-native-document-picker';
import {PhotoFile} from 'react-native-vision-camera';
import {CustomCameraRef} from '../../components/Camera';
import {openSettings, PERMISSIONS, request} from 'react-native-permissions';
import {showTostMessage} from '../../utils/helpers';

type FormData = z.infer<typeof profileSettingSchema>;

export default function ProfileUpdate() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const [showModal, setShowModal] = React.useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [photoUrl, setPhotoUrl] = useState<
    PhotoFile | DocumentPickerResponse
  >();
  const {
    control,
    handleSubmit,
    formState: {isValid},
  } = useForm<FormData>({
    resolver: zodResolver(profileSettingSchema),
    // mode: 'onChange',
    // defaultValues: {
    //   name: '',
    // },
  });
  const cameraRef = useRef<CustomCameraRef>(null);

  const {user, setUser} = useStore(state => ({
    user: state.user,
    setUser: state.setUser,
  }));
  const toggleManualModal = React.useCallback(() => {
    setShowModal(showing => !showing);
  }, []);

  async function handleGallery() {
    try {
      const result = await DocumentPicker.pick({
        type: [types.images],
      });
      setPhotoUrl(result[0]);
      if (result[0]) {
        const avatar = await updateUserAvatar(result[0]);
        if (avatar.status === 200) {
          showTostMessage('Avatar updated successfully');
          fetchUserProfile();
        }
      }
    } catch (error) {
      if (!DocumentPicker.isCancel(error)) {
        showTostMessage('Something went wrong!');
      }
    } finally {
      setShowModal(false);
    }
  }

  async function requestCameraPermission() {
    const permission = await request(PERMISSIONS.ANDROID.CAMERA);

    if (permission === 'blocked') {
      openSettings();
    }
  }

  async function toggleCamera() {
    setShowCamera(showing => !showing);
    setShowModal(false);
  }

  const handlePhotoTaken = async (photo: PhotoFile) => {
    setPhotoUrl(photo);
    if (photo) {
      const avatar = await updateUserAvatar(photo);
      if (avatar.status === 200) {
        showTostMessage('Avatar updated successfully');
        fetchUserProfile();
      }
    }
    toggleCamera();
  };

  async function fetchUserProfile() {
    try {
      const res = await getUserProfile();
      const data = await res.json();
      if (data) {
        setUser({
          phone: data.phone,
          id: data.id,
          role: data.role,
          username: data.username,
          name: data.name,
          superUser: data.isSuperUser,
          superAdmin: data.isSuperAdmin,
          url: data.avatar?.url,
          bio: data?.bio,
        });
      }
    } catch (e) {
      Alert.alert('Error fetching profile');
    }
  }
  useEffect(() => {
    console.log(user, 'Updated user state');
  }, [user]);

  async function onSubmit({name, bio}: FormData) {
    try {
      const data = await updateUserProfile(name, bio);
      if (data.ok && data.status == 200) {
        await fetchUserProfile();
        showTostMessage('Profile Update Successfully');
        navigation.goBack();
      } else {
        showTostMessage('Something went wrong!');
      }
    } catch (error) {
      showTostMessage('Something went wrong!');
    }
  }
  useEffect(() => {
    requestCameraPermission();
  }, []);
  return (
    <>
      {showCamera && (
        <CameraComp
          ref={cameraRef}
          onPhotoTaken={handlePhotoTaken}
          toggleCamera={toggleCamera}
        />
      )}
      <BodyCard padBottom padTop>
        <KeyboardAvoidingView>
          <Row>
            <BackBtn />
            <Text variant="H2_500">Edit Profile</Text>
          </Row>
          <Spacer height={2} />
          <Text variant="subText2" color={colors.text.B50}>
            Edit and update your profile details below.
          </Text>
          <Spacer height={56} />
          <View style={[styles.profileCard]}>
            <View style={styles.centeredContainer}>
              <Row style={styles.imageContainer}>
                <Image
                  alt={user?.username}
                  source={
                    photoUrl
                      ? 'path' in photoUrl
                        ? {uri: `file://${photoUrl.path}`}
                        : {uri: photoUrl.uri}
                      : user?.url
                      ? {uri: user.url}
                      : require('../../assets/images/avatar.png')
                  }
                  style={styles.profileImage}
                />
                <Spacer height={12} />
                <TouchableOpacity
                  activeOpacity={0.6}
                  style={styles.cameraIconWrapper}>
                  <CameraAdd
                    onPress={toggleManualModal}
                    width={18}
                    height={18}
                    fill={colors.icon.white}
                    style={{
                      alignSelf: 'center',
                      bottom: 75,
                      backgroundColor: colors.primaryB200,
                    }}
                  />
                </TouchableOpacity>
              </Row>
            </View>
            <View style={styles.formContainer}>
              <Input
                control={control}
                name="name"
                label="Name"
                placeholder={user?.name}
              />
              <Spacer height={24} />
              <Row>
                <Phone />
                <Text variant="subText1">Phone number</Text>
              </Row>
              <Spacer height={4} />
              <Text variant="subText1_500">+91 {user?.phone}</Text>
              <Spacer height={24} />
              <Row>
                <ContactTabIcon height={20} width={20} />
                <Text variant="subText1">Username</Text>
              </Row>
              <Spacer height={4} />
              <Text variant="subText1_500"> {user?.username}</Text>
              <Spacer height={24} />
              <Input
                label="Bio"
                name="bio"
                control={control}
                multiline
                inputStyle={styles.bioInput}
                placeholder="Hey there, I’m using N2chat"
              />
            </View>
          </View>
        </KeyboardAvoidingView>
        <Spacer height={54} />
        <Cta
          title="Update"
          onPress={handleSubmit(onSubmit)}
          disabled={!isValid}
        />
      </BodyCard>
      <BottomSheet visible={showModal} onClose={toggleManualModal}>
        <View style={styles.bottomSheet}>
          <Row between>
            <View>
              <Pressable style={styles.attachments} onPress={handleGallery}>
                <Gallery />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Gallery
              </Text>
            </View>
            <View>
              <Pressable style={styles.attachments} onPress={toggleCamera}>
                <Camera height={24} width={24} fill={colors.icon.B100} />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Camera
              </Text>
            </View>
          </Row>
        </View>
      </BottomSheet>
    </>
  );
}

const styles = StyleSheet.create({
  profileCard: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.borders.B20,
    padding: 18,
    alignItems: 'center',
    backgroundColor: 'white',
    elevation: 4,
    shadowOffset: {
      width: 4,
      height: 1,
    },
    shadowRadius: 3.84,
    shadowOpacity: 0.5,
    height: '72%',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    bottom: 75,
  },
  formContainer: {
    width: '100%',
    bottom: 90,
  },
  bioInput: {
    height: 92,
  },
  centeredContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: 'auto',
  },
  imageContainer: {
    width: 86,
    height: 86,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 16,
  },
  cameraIconWrapper: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    borderRadius: 20,
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomSheet: {
    height: 200,
    width: '100%',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    shadowColor: 'black',
    elevation: 3,
    shadowRadius: 10,
    shadowOpacity: 1,
    backgroundColor: colors.white,
  },
  attachments: {
    padding: 20,
    backgroundColor: colors.primaryB50,
    borderRadius: 100,
  },
});
