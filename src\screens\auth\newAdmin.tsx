import {View} from 'react-native';
import React from 'react';
import {
  BodyCard,
  Cta,
  Input,
  KeyboardAvoidingView,
  Row,
  Spacer,
  Text,
} from '../../components';
import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {z} from 'zod';
import {NewAdminSchema, RootStack} from '../../utils';
import {saveNewAdmin} from '../../utils/apis';
import {
  IndiaFlag,
  User,
  Phone,
  LockPassword,
  BuisnessIcon,
} from '../../assets/icons';
import {showTostMessage} from '../../utils/helpers';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

type FormData = z.infer<typeof NewAdminSchema>;
const startView = (
  <Row>
    <IndiaFlag />
    <Spacer width={8} />
    <Text variant="subText1_500">+91</Text>
  </Row>
);
export default function NewAdmin() {
  const {
    control,
    handleSubmit,
    formState: {isValid},
  } = useForm<FormData>({
    resolver: zodResolver(NewAdminSchema),
    mode: 'onChange',
    shouldUnregister: true,
  });
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  async function onSubmit({phone, name, password, business_name}: FormData) {
    try {
      const response = await saveNewAdmin(name, phone, password, business_name);
      const data = await response.json();
      if (response.status === 200 && response.ok) {
        showTostMessage(data.message);
        navigation.goBack();
      } else {
        showTostMessage(data.message);
      }
    } catch (error) {
      showTostMessage('Something went wrong!');
    }
  }
  return (
    <BodyCard padTop padBottom>
      <KeyboardAvoidingView>
        <View>
          <Input
            labelIcon={<User />}
            control={control}
            name="name"
            label="Name"
            placeholder="John"
          />
          <Input
            control={control}
            labelIcon={<Phone />}
            name="phone"
            label="Phone number"
            placeholder="9999 999 999"
            maxLength={10}
            keyboardType="number-pad"
            startView={startView}
          />
          <Input
            labelIcon={<BuisnessIcon />}
            control={control}
            name="business_name"
            label="Business name"
            placeholder="business name"
          />
          <Input
            labelIcon={<LockPassword />}
            control={control}
            name="password"
            label="Password"
            placeholder="password"
          />
        </View>
      </KeyboardAvoidingView>
      <Cta
        title="Create Admin"
        onPress={handleSubmit(onSubmit)}
        disabled={!isValid}
      />
    </BodyCard>
  );
}
