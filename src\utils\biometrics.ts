import ReactNativeBiometrics from 'react-native-biometrics';

const rnBiometric = new ReactNativeBiometrics();

export async function checkBiometric() {
  try {
    const {available} = await rnBiometric.isSensorAvailable();
    return available;
  } catch (error) {
    return null;
  }
}

export async function generateBiometricPublicKey() {
  try {
    const {keysExist} = await rnBiometric.biometricKeysExist();
    if (keysExist) {
      throw new Error('key exist');
    }
    const {publicKey} = await rnBiometric.createKeys();

    return publicKey;
  } catch (error) {
    console.error(error);
  }
}

export async function deleteBiometricPublicKey() {
  try {
    const {keysDeleted} = await rnBiometric.deleteKeys();
    return keysDeleted;
  } catch (error) {
    console.error(error);
  }
}

export async function loginWithBiometric() {
  try {
    const isBiometricAvailable = await checkBiometric();
    if (!isBiometricAvailable) {
      throw new Error('Biometric not available');
    }

    const {keysExist} = await rnBiometric.biometricKeysExist();
    if (!keysExist) {
      await rnBiometric.createKeys();
    }

    const {success, signature} = await rnBiometric.createSignature({
      promptMessage: 'login',
      payload: 'login',
    });

    if (!success) {
      throw new Error('Biometric auth failed');
    }
    if (signature) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    console.error(error);
  }
}
