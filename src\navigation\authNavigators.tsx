import * as React from 'react';
import {
  createNativeStackNavigator,
  NativeStackNavigationProp,
} from '@react-navigation/native-stack';
import {RootStack} from '../utils/types';
import bottomTabNavigators from './bottomTabNavigators';

import {
  Login,
  Register,
  ForgotPassword,
  Otp,
  NewPassword,
  Verified,
  ConfirmPassword,
  PasswordResetSuccessfully,
  Password,
  VoiceCall,
  Settings,
  ProfileSetting,
  AccountSetting,
  NotificationSetting,
  ProfileSetup,
  NewGroup,
  GroupCongrats,
  CreateStory,
  NewContact,
  ChatWithNumber,
  CreateGroup,
  Profile,
  GroupInfo,
  GroupAddMembers,
  StatusScreen,
  ChatInfo,
  AdminList,
  NewAdmin,
  NewAgent,
} from '../screens/auth/';

import {
  Back,
  Dots,
  ForwardMessage,
  MessageInfo,
  Phone,
  StarMessage,
} from '../assets/icons';
import {Pressable} from 'react-native';
import {Image, Spacer, Text} from '../components';
import {useNavigation} from '@react-navigation/native';
import {useStore} from '../utils/hooks';
import {getFontSize} from '../utils';
import {colors} from '../design/colors';
import WelcomeScreen from '../screens/welcome/welcomeScreen';
import {Chats} from '../screens/chats';
import WebSocketProvider from '../utils/providers/WebSocket';
import {WebRTCContext} from '../utils/providers/WebRtcProvider';

const Stack = createNativeStackNavigator<RootStack>();

const headerTitleStyle = {
  fontFamily: 'GoogleSans-Medium',
  fontSize: getFontSize(21),
  color: colors.text.B50,
};

function PublicNavigator() {
  return (
    <Stack.Navigator
      initialRouteName="welcome"
      screenOptions={{headerShown: false}}>
      <Stack.Screen name="welcome" component={WelcomeScreen} />
      <Stack.Screen name="login" component={Login} />
      <Stack.Screen name="register" component={Register} />
      <Stack.Screen name="forgotPassword" component={ForgotPassword} />
      <Stack.Screen name="otp" component={Otp} />
      <Stack.Screen name="newPassword" component={NewPassword} />
      <Stack.Screen name="verified" component={Verified} />
      <Stack.Screen name="confirmPassword" component={ConfirmPassword} />
      <Stack.Screen name="profileSetup" component={ProfileSetup} />
      <Stack.Screen
        name="password"
        component={Password}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="passwordResetSuccessfully"
        component={PasswordResetSuccessfully}
      />
    </Stack.Navigator>
  );
}

function PrivateNavigator() {
  const {setCallerInfo} = React.useContext(WebRTCContext);
  const navigator = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {initialRouteName} = useStore(state => ({
    initialRouteName: state.initialRouteName,
  }));

  function navigateToGoBack() {
    if (navigator.canGoBack()) {
      navigator.goBack();
    } else {
      navigator.navigate('tab', {screen: 'chatsList'});
    }
  }

  function navigateToStarMessage() {
    console.log('Star message');
  }

  function navigateToMessageInfo() {
    navigator.goBack();
  }

  function navigateToForwardMessage() {
    navigator.goBack();
  }

  function navigateToChatInfo(
    id: string,
    chatType: 'user' | 'group',
    lastSeen?: string,
  ) {
    if (chatType === 'user') {
      // navigator.navigate('chatInfo', {chatId: id, lastSeen});
      console.log(lastSeen, 'lastSeen');
    } else {
      navigator.navigate('groupInfo', {chatId: id});
    }
  }

  function navigateToVoiceCall(userName: string, avatar?: string | null) {
    navigator.navigate('voiceCall', {
      isIncomingCall: false,
      userName,
      avatar,
    });
  }

  return (
    <Stack.Navigator
      initialRouteName={initialRouteName}
      screenOptions={{headerShown: false}}>
      <Stack.Screen name="tab" component={bottomTabNavigators} />
      <Stack.Screen name="settings" component={Settings} />
      <Stack.Screen name="ProfileSetting" component={ProfileSetting} />
      <Stack.Screen name="accountSetting" component={AccountSetting} />
      <Stack.Screen name="verified" component={Verified} />
      <Stack.Screen
        name="notificationSetting"
        component={NotificationSetting}
      />
      <Stack.Screen name="newGroup" component={NewGroup} />
      <Stack.Screen name="GroupCongrats" component={GroupCongrats} />
      <Stack.Screen name="createStory" component={CreateStory} />
      <Stack.Screen name="adminList" component={AdminList} />
      <Stack.Screen
        name="newAdmin"
        component={NewAdmin}
        options={{
          headerShown: true,
          headerTitle: 'New Admin',
          headerBackButtonMenuEnabled: true,
          headerTitleStyle: headerTitleStyle,
        }}
      />
      <Stack.Screen
        name="newAgent"
        component={NewAgent}
        options={{
          headerShown: true,
          headerTitle: 'New Agent',
          headerBackButtonMenuEnabled: true,
          headerTitleStyle: headerTitleStyle,
        }}
      />

      <Stack.Screen
        name="newContact"
        component={NewContact}
        options={{
          headerShown: true,
          headerTitle: 'New Contact',
          headerBackButtonMenuEnabled: true,
          headerTitleStyle: headerTitleStyle,
        }}
      />
      <Stack.Screen
        name="chatWithNumber"
        component={ChatWithNumber}
        options={{
          headerShown: true,
          headerTitle: 'Chat with number',
          headerBackButtonMenuEnabled: true,
          headerTitleStyle: headerTitleStyle,
        }}
      />
      <Stack.Screen name="createGroup" component={CreateGroup} />
      <Stack.Screen name="profile" component={Profile} />
      <Stack.Screen name="groupInfo" component={GroupInfo} />
      <Stack.Screen name="groupAddMembers" component={GroupAddMembers} />
      <Stack.Screen name="statusScreen" component={StatusScreen} />
      <Stack.Screen name="chatInfo" component={ChatInfo} />
      <Stack.Screen name="welcome" component={WelcomeScreen} />
      <Stack.Screen
        name="chats"
        component={Chats}
        options={({route}) => {
          const selectedMessagesCount =
            route.params?.selectedMessagesCount || 0;
          const showHeaderOptions = selectedMessagesCount > 0;
          return {
            headerShown: !route.params?.showCamera,
            title: showHeaderOptions ? `${selectedMessagesCount}` : '',
            headerRight: () => {
              if (showHeaderOptions) {
                return (
                  <>
                    <Pressable onPress={navigateToStarMessage}>
                      <StarMessage />
                    </Pressable>
                    <Spacer width={10} />
                    <Pressable onPress={navigateToMessageInfo}>
                      <MessageInfo />
                    </Pressable>
                    <Spacer width={10} />
                    <Pressable onPress={navigateToForwardMessage}>
                      <ForwardMessage />
                    </Pressable>
                  </>
                );
              } else {
                return (
                  <>
                    <Spacer width={12} />
                    {route?.params?.chatType === 'user' && (
                      <Pressable
                        onPress={() => {
                          navigateToVoiceCall(
                            route?.params?.name,
                            route?.params?.img,
                          );
                          setCallerInfo({
                            userName: route?.params?.name,
                            avatar: route?.params?.img,
                          });
                        }}>
                        <Phone />
                      </Pressable>
                    )}

                    <Spacer width={12} />
                    <Pressable
                      onPress={() => {
                        console.log(route.params);
                      }}>
                      <Dots />
                    </Pressable>
                  </>
                );
              }
            },
            headerLeft: () => (
              <>
                <Pressable onPress={navigateToGoBack}>
                  <Back />
                </Pressable>
                <Spacer width={8} />
                {showHeaderOptions ? (
                  ''
                ) : (
                  <>
                    <Image
                      uri={route.params?.img ?? null}
                      alt={route.params?.name ?? ''}
                    />
                    <Spacer width={8} />
                    <Pressable
                      onPress={() =>
                        navigateToChatInfo(
                          route.params.id,
                          route.params.chatType,
                          route.params.lastSeen,
                        )
                      }
                      hitSlop={{right: 90}}>
                      <Text variant="H2_500">
                        {route.params?.name.length > 12
                          ? `${route.params.name.substring(0, 12)}...`
                          : route.params?.name}
                      </Text>
                    </Pressable>
                  </>
                )}
                <Spacer width={8} />
              </>
            ),
          };
        }}
      />
      <Stack.Screen name="voiceCall" component={VoiceCall} />
    </Stack.Navigator>
  );
}

export default function AuthNavigators() {
  const {isAuthenticated} = useStore(state => ({
    isAuthenticated: state.authToken,
  }));

  return isAuthenticated ? (
    <WebSocketProvider>
      <PrivateNavigator />
    </WebSocketProvider>
  ) : (
    <PublicNavigator />
  );
}
