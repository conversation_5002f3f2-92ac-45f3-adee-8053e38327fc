import messaging from '@react-native-firebase/messaging';
import notifee from '@notifee/react-native';

export async function listenForMessage() {
  const unsubscribe = messaging().onMessage(async remoteMessage => {
    console.log('Fnotification:', remoteMessage);
  });

  messaging().setBackgroundMessageHandler(async remoteMessage => {
    console.log('Message handled in the background!', remoteMessage);
  });

  messaging().onNotificationOpenedApp(async remoteMessage => {
    console.log(remoteMessage, 'ddddddddddddddddddddd39');
  });

  return () => {
    unsubscribe();
  };
}

{
  const unsubscribeNotifee = notifee.onForegroundEvent(event => {
    console.log(event);
  });
}
