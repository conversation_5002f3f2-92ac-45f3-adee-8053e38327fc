import React, {useRef, useEffect, useState} from 'react';
import {
  NativeSyntheticEvent,
  Pressable,
  StyleSheet,
  TextInput,
  TextInputKeyPressEventData,
  View,
  Alert as RNAlert,
} from 'react-native';
import {
  BodyCard,
  Text,
  Cta,
  BackBtn,
  Spacer,
  KeyboardAvoidingView,
  Alert,
} from '../../components';
import {Controller, useForm} from 'react-hook-form';
import {useRoute, RouteProp} from '@react-navigation/native';
import {RootStack} from '../../utils/types';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {colors} from '../../design/colors';
import {otpSchema} from '../../utils/validationSchema';
import {
  userCreateAccount,
  postUserOtp,
  userResetOtp,
  adminLogin,
  agentResendOtp,
} from '../../utils/apis/postApis';
import {useStore} from '../../utils/hooks';
import {getUserProfile, updateUserAvatar} from '../../utils/apis';

type FormData = z.infer<typeof otpSchema>;
type OtpScreenRouteProp = RouteProp<RootStack, 'otp'>;

export default function Otp() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const {
    control,
    handleSubmit,
    formState: {isValid, errors},
    setError,
    clearErrors,
  } = useForm<FormData>({
    resolver: zodResolver(otpSchema),
  });

  const [showAlert, setShowAlert] = useState(false);
  const [timeLeft, setTimeLeft] = useState(30);

  const {setAuthToken, setUser} = useStore(state => ({
    setAuthToken: state.setAuthToken,
    setUser: state.setUser,
  }));
  const {
    params: {
      phone,
      password,
      name,
      userExisted,
      isSuperUser,
      photoUrl,
      userRole,
      isSuperAdmin,
    },
  } = useRoute<OtpScreenRouteProp>();

  const otp1Ref = useRef<TextInput>(null);
  const otp2Ref = useRef<TextInput>(null);
  const otp3Ref = useRef<TextInput>(null);
  const otp4Ref = useRef<TextInput>(null);

  async function updateAvatar() {
    if (photoUrl) {
      const avatar = await updateUserAvatar(photoUrl);
      await avatar.json();
    }
  }

  async function fetchUserProfile() {
    try {
      getUserProfile()
        .then(res => res.json())
        .then(data => {
          if (data) {
            updateAvatar();
            setUser({
              phone: data.phone,
              id: data.id,
              role: data.role,
              username: data.username,
              name: data.name,
              superUser: data.isSuperUser,
              superAdmin: data.isSuperAdmin,
              url: data.url,
            });
          }
        });
    } catch (e) {
      RNAlert.alert('Error');
    }
  }

  async function onSubmit(formData: FormData) {
    try {
      const otp = Object.values(formData).join('');
      if (userExisted && isSuperUser) {
        const data = await adminLogin(phone, otp);
        if (!data.ok) {
          setError('otp1', {type: 'custom', message: 'Incorrect OTP'});
        } else if (data.ok && data.status === 200) {
          const response = await data.json();
          fetchUserProfile();
          setAuthToken(response.token);
          while (navigation.canGoBack()) {
            navigation.pop();
          }

          navigation.replace('verified', {isSuperAdmin, isSuperUser});
        }
      } else if (!userExisted) {
        if (phone && password && name) {
          const response = await userCreateAccount(phone, password, otp, name);
          const data = await response.json();

          if (data.error) {
            setError('otp1', {type: 'custom', message: 'Incorrect OTP'});
          } else {
            setAuthToken(data.token);
            fetchUserProfile();
            while (navigation.canGoBack()) {
              navigation.pop();
            }
            navigation.replace('verified', {isSuperAdmin, isSuperUser});
          }
        }
      } else if (phone) {
        navigation.navigate('newPassword', {phone, otp, userRole});
      }
    } catch (error) {
      RNAlert.alert('Error', 'Something went wrong. Please try again later.');
    }
  }

  function navigateToLogin() {
    navigation.replace('login');
  }

  function showAlertBox() {
    setShowAlert(prevState => !prevState);
  }

  const handleOtpChange = (
    value: string,
    nextRef?: React.RefObject<TextInput>,
  ) => {
    if (value.length === 1 && nextRef?.current) {
      nextRef.current.focus();
    }
  };

  const handleOtpKeyPress = (
    e: NativeSyntheticEvent<TextInputKeyPressEventData>,
    prevRef?: React.RefObject<TextInput>,
  ) => {
    if (e.nativeEvent.key === 'Backspace' && prevRef?.current) {
      clearErrors();
      prevRef.current.focus();
    }
  };

  function showAlertBoxPopUp(message: string) {
    RNAlert.alert('Message', `Your OTP is ${message}`);
  }

  async function handleResendOtp() {
    setTimeLeft(30);
    if (phone) {
      try {
        if (userRole === 'admin' && userExisted) {
          const agentOtpRes = await agentResendOtp(phone);
          const response = await agentOtpRes.json();
          showAlertBoxPopUp(response?.message);
        } else if (userExisted) {
          const response = await userResetOtp(phone);
          const data = await response.json();
          showAlertBoxPopUp(data?.message);
        } else {
          const response = await postUserOtp(phone);
          const data = await response.json();
          showAlertBoxPopUp(data?.message);
        }
      } catch (error) {
        RNAlert.alert('Error', 'Failed to resend OTP. Please try again later.');
      }
    }
  }

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeLeft(prevTimer => (prevTimer > 0 ? prevTimer - 1 : 0));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <>
      <BodyCard padBottom padTop>
        <KeyboardAvoidingView>
          <BackBtn />
          <Spacer height={40} />
          <View>
            <Text variant="H2_500">
              {userExisted && !isSuperUser
                ? 'OTP to reset password'
                : 'We’ve sent an OTP'}
            </Text>
            <Text variant="subText2" color={colors.text.B40}>
              {userExisted && !isSuperUser
                ? 'A four-digit code has been sent to your registered phone number. Please enter the same to continue.'
                : `Enter the four-digit code received on +91 ${phone}`}
            </Text>
            <Text
              variant="subText2"
              color={colors.text.primary}
              onPress={showAlertBox}>
              Change number
            </Text>
          </View>
          <Spacer height={10} />
          <View style={{flexDirection: 'row', gap: 8}}>
            <Controller
              control={control}
              render={({field: {onChange, onBlur, value}}) => (
                <TextInput
                  ref={otp1Ref}
                  onBlur={onBlur}
                  onChangeText={text => {
                    onChange(text);
                    handleOtpChange(text, otp2Ref);
                  }}
                  onKeyPress={e => handleOtpKeyPress(e, otp1Ref)}
                  value={value}
                  style={styles.input}
                  keyboardType="number-pad"
                  maxLength={1}
                />
              )}
              name="otp1"
            />
            <Controller
              control={control}
              render={({field: {onChange, onBlur, value}}) => (
                <TextInput
                  ref={otp2Ref}
                  onBlur={onBlur}
                  onChangeText={text => {
                    onChange(text);
                    handleOtpChange(text, otp3Ref);
                  }}
                  onKeyPress={e => handleOtpKeyPress(e, otp1Ref)}
                  value={value}
                  style={styles.input}
                  keyboardType="number-pad"
                  maxLength={1}
                />
              )}
              name="otp2"
            />
            <Controller
              control={control}
              render={({field: {onChange, onBlur, value}}) => (
                <TextInput
                  ref={otp3Ref}
                  onBlur={onBlur}
                  onChangeText={text => {
                    onChange(text);
                    handleOtpChange(text, otp4Ref);
                  }}
                  onKeyPress={e => handleOtpKeyPress(e, otp2Ref)}
                  value={value}
                  style={styles.input}
                  keyboardType="number-pad"
                  maxLength={1}
                />
              )}
              name="otp3"
            />
            <Controller
              control={control}
              render={({field: {onChange, onBlur, value}}) => (
                <TextInput
                  ref={otp4Ref}
                  onBlur={onBlur}
                  onChangeText={text => {
                    onChange(text);
                    handleOtpChange(text);
                  }}
                  onKeyPress={e => handleOtpKeyPress(e, otp3Ref)}
                  value={value}
                  style={styles.input}
                  keyboardType="number-pad"
                  maxLength={1}
                />
              )}
              name="otp4"
            />
          </View>
          {errors.otp1 && (
            <>
              <Spacer height={12} />
              <Text variant="subText2" color={colors.text.error}>
                {errors.otp1.message}
              </Text>
            </>
          )}
          <Spacer height={12} />
          <Pressable disabled={timeLeft > 0} onPress={handleResendOtp}>
            <Text variant="subText2_500">
              Resend OTP {timeLeft > 0 && `in ${timeLeft} sec`}
            </Text>
          </Pressable>
        </KeyboardAvoidingView>
        <Cta
          title={isSuperUser ? 'Login' : 'Verify'}
          onPress={handleSubmit(onSubmit)}
          disabled={!isValid}
        />
      </BodyCard>
      <Alert
        visible={showAlert}
        message={`Do you want to continue changing this +91 ${phone} phone number?`}
        onClose={showAlertBox}
        onConfirm={navigateToLogin}
      />
    </>
  );
}

const styles = StyleSheet.create({
  input: {
    borderWidth: 0.5,
    height: 48,
    width: 48,
    textAlign: 'center',
    borderRadius: 8,
    borderColor: colors.borders.B20,
  },
});
