import React, {useEffect, useRef} from 'react';
import {StyleSheet, View} from 'react-native';
import Animated, {
  cancelAnimation,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {colors} from '../design/colors';

interface Props {
  active: boolean;
  isPaused: boolean;
  onComplete: () => void;
  storyDurations: number;
  currentIndex: number;
  storyIndex: number;
  isSkipped?: boolean;
}

export default function ProgressBar({
  active,
  isPaused,
  onComplete,
  storyDurations,
  currentIndex,
  storyIndex,
  isSkipped,
}: Props) {
  const progress = useSharedValue(0);
  const startTime = useRef<number>(0);
  const remainingTime = useRef<number>(storyDurations);

  useEffect(() => {
    if (storyIndex < currentIndex || isSkipped) {
      progress.value = 1;
    } else if (storyIndex > currentIndex) {
      progress.value = 0;
    }
  }, [currentIndex, storyIndex, isSkipped]);

  useEffect(() => {
    if (active && !isPaused) {
      startTime.current = Date.now();
      progress.value = withTiming(
        1,
        {
          duration: remainingTime.current,
        },
        finished => {
          if (finished) {
            runOnJS(onComplete)();
          }
        },
      );
    } else if (active && isPaused) {
      const elapsedTime = Date.now() - startTime.current;
      remainingTime.current = Math.max(0, remainingTime.current - elapsedTime);
      cancelAnimation(progress);
    }

    return () => {
      if (!active) {
        remainingTime.current = storyDurations;
      }
    };
  }, [active, isPaused, onComplete]);

  const animatedStyle = useAnimatedStyle(() => ({
    width: `${progress.value * 100}%`,
  }));

  return (
    <View style={styles.progressContainer}>
      <Animated.View
        style={[
          styles.progressBar,
          animatedStyle,
          (storyIndex < currentIndex || isSkipped) && styles.completed,
        ]}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  progressContainer: {
    flex: 1,
    height: 2,
    backgroundColor: 'rgba(255,255,255,0.3)',
    borderRadius: 4,
    overflow: 'hidden',
    marginHorizontal: 2,
  },
  progressBar: {
    height: '100%',
    backgroundColor: colors.white,
  },
  completed: {
    width: '100%',
  },
});
