import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Agent, Contact, Groups} from '../screens/tabs';
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from 'react-native-responsive-screen';
import {ChatsList} from '../screens/chats';
import {ContactTabIcon, GroupTabIcon, Home, User} from '../assets/icons';
import {colors} from '../design/colors';
import {StyleSheet, View} from 'react-native';
import {getFontSize} from '../utils';
import {Spacer} from '../components';
import {useStore} from '../utils/hooks';

const Tab = createBottomTabNavigator();

const LINE = (
  <>
    <Spacer height={4} />
    <View
      style={{
        backgroundColor: colors.primaryB200,
        height: 4,
        width: 32,
        borderRadius: 8,
      }}
    />
  </>
);

export default function BottomTabNavigator() {
  const {role} = useStore(state => ({
    role: state.user?.role,
  }));
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          height: 85,
          paddingVertical: 16,
          paddingBottom: 16,
        },
        tabBarLabelStyle: styles.tabBarLabelStyle,
        tabBarHideOnKeyboard: true,
      }}>
      <Tab.Screen
        name="chatsList"
        component={ChatsList}
        options={{
          tabBarLabel: 'Chats',
          headerShown: false,
          tabBarShowLabel: true,
          tabBarIcon: ({focused}) => {
            return (
              <>
                <Home
                  height={hp(10)}
                  width={wp(10)}
                  fill={focused ? colors.primaryB200 : colors.primaryB100}
                />
                {focused && LINE}
              </>
            );
          },
        }}
      />
      {role === 'admin' && (
        <Tab.Screen
          name="agent"
          component={Agent}
          options={{
            tabBarLabel: 'Agent',
            headerShown: false,
            tabBarShowLabel: true,
            tabBarIcon: ({focused}) => {
              return (
                <>
                  <User
                    height={hp(10)}
                    width={wp(10)}
                    fill={focused ? colors.primaryB200 : colors.primaryB100}
                  />
                  {focused && LINE}
                </>
              );
            },
          }}
        />
      )}
      <Tab.Screen
        name="groups"
        component={Groups}
        options={{
          tabBarLabel: 'Groups',

          headerShown: false,
          tabBarShowLabel: true,
          tabBarIcon: ({focused}) => {
            return (
              <>
                <GroupTabIcon
                  height={hp(10)}
                  width={wp(10)}
                  fill={focused ? colors.primaryB200 : colors.primaryB100}
                />
                {focused && LINE}
              </>
            );
          },
        }}
      />
      <Tab.Screen
        name="contact"
        component={Contact}
        options={{
          tabBarLabel: 'Contact',
          headerShown: false,
          tabBarShowLabel: true,
          lazy: true,
          headerShadowVisible: true,
          tabBarIcon: ({focused}) => {
            return (
              <>
                <ContactTabIcon
                  height={hp(10)}
                  width={wp(10)}
                  fill={focused ? colors.primaryB200 : colors.primaryB100}
                />
                {focused && LINE}
              </>
            );
          },
        }}
      />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  tabBarLabelStyle: {
    fontSize: getFontSize(12),
    fontFamily: 'GoogleSans-Medium',
    marginBottom: 4,
    color: colors.text.B100,
  },
});
