import {View, StyleProp, ViewStyle, TextStyle, StyleSheet} from 'react-native';
import React from 'react';
import {colors} from '../design/colors';
import Row from './Row';
import Text from './Text';
import {FieldError} from 'react-hook-form';
import Spacer from './Spacer';

type Props = {
  style?: StyleProp<ViewStyle>;
  labelStyle?: StyleProp<TextStyle>;
  error?: FieldError;
  startView?: React.ReactNode;
  startViewStyle?: StyleProp<ViewStyle>;
  endView?: React.ReactNode;
  endViewStyle?: StyleProp<ViewStyle>;
  children: React.ReactNode;
  inputStyle?: StyleProp<ViewStyle>;
  label?: string;
  border?: boolean;
  labelIcon?: React.ReactNode;
};

export default function InputField({
  style,
  label,
  inputStyle,
  startView,
  startViewStyle,
  endView,
  endViewStyle,
  children,
  error,
  border,
  labelIcon,
}: Props) {
  return (
    <View style={style}>
      <Row center>
        {labelIcon && labelIcon}
        {label && (
          <>
            <Text variant="subText1">{label}</Text>
          </>
        )}
      </Row>
      {label && <Spacer height={4} />}
      <Row
        style={[
          styles.inputContainer,
          inputStyle,
          {borderWidth: border ? 0.5 : undefined},
        ]}
        center>
        {startView && (
          <View style={[styles.left, startViewStyle]}>{startView}</View>
        )}
        {children}
        {endView && <View style={[styles.right, endViewStyle]}>{endView}</View>}
      </Row>
      <Spacer height={8} />
      {error && error.message !== '' && (
        <Text variant="subText2" color={colors.error}>
          {error.message}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  inputContainer: {
    borderRadius: 32,
    borderColor: colors.borders.B20,
    maxHeight: 100,
  },

  left: {
    paddingLeft: 18,
  },
  right: {
    paddingRight: 18,
  },
});
