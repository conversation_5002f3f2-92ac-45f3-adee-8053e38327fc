import {PixelRatio, ToastAndroid} from 'react-native';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  GroupList,
  MessageRead,
  Participant,
  Sender,
} from './types';

const fontScale = PixelRatio.getFontScale();
export const getFontSize = (size: number) => size / fontScale;

export function convertIntoLocalTime(dateString: string): string {
  if (dateString === undefined) {
    return '';
  }
  const inputDate = new Date(dateString);

  const now = new Date();

  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);

  const format12HourTime = (date: Date): string => {
    let hours = date.getHours();
    const minutes = date.getMinutes();
    const amPmFormate = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12;
    const strMinutes = minutes < 10 ? '0' + minutes : minutes;
    return `${hours}:${strMinutes} ${amPmFormate}`;
  };

  if (inputDate >= today && inputDate < new Date(today.getTime() + 86400000)) {
    return format12HourTime(inputDate);
  }

  if (inputDate >= yesterday && inputDate < today) {
    return 'yesterday';
  }

  const day = String(inputDate.getDate()).padStart(2, '0');
  const month = String(inputDate.getMonth() + 1).padStart(2, '0');
  const year = inputDate.getFullYear();

  return `${day}/${month}/${year}`;
}

export function findUserRole(
  participants: Participant[],
  userId: string | undefined,
): boolean {
  const participant = participants?.find(item => item.id !== userId);

  return participant?.role === 'admin' || participant?.role === 'agent';
}

export function findUserName(
  participants: Participant[],
  userId: string | undefined,
): string {
  const participant = participants?.find(item => item.id !== userId);
  return (
    (participant?.saved_name ? participant?.saved_name : participant?.phone) ||
    'Unknown'
  );
}

export function findAgentName(
  participants: Participant[],
  number: string | undefined,
): string {
  const participant = participants?.find(item => item.phone !== number);
  return (
    (participant?.saved_name ? participant?.saved_name : participant?.phone) ||
    'Unknown'
  );
}

export function findUserBio(
  participants: Participant[],
  userId: string | undefined,
): string {
  const participant = participants?.find(item => item.id !== userId);

  return participant?.bio || '';
}

export function findUserAvatar(
  participants: Participant[],
  userId: string | undefined,
): string | null | undefined {
  const participant = participants?.find(item => item.id !== userId);
  return participant?.avatar?.url;
}

export function getLastMessageTimestamp(chat: ChatList): number {
  if (chat.messages.length > 0) {
    return new Date(
      chat.messages[chat.messages.length - 1].createdAt,
    ).getTime();
  }
  return new Date(chat.updatedAt).getTime();
}

export function sortRecentMessage(chats: ChatList[]): ChatList[] {
  return chats.sort(
    (a, b) => getLastMessageTimestamp(b) - getLastMessageTimestamp(a),
  );
}

export function sortMessages(messages: ChatRow[]): ChatRow[] {
  return messages.sort((a, b) => {
    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
  });
}

export function massageStatus(
  userId: string | undefined,
  sender: Sender,
  message_read: MessageRead[] | null,
): 'delivered' | 'seen' {
  if (message_read === null) {
    return 'delivered';
  }

  if (userId) {
    if (userId === sender?.id && message_read) {
      return 'seen';
    } else {
      return 'delivered';
    }
  }
  return 'delivered';
}

export function getTimeAndMin(timestamp: string): string {
  const date = new Date(timestamp);
  const ISTOffset = 5.5 * 60 * 60 * 1000;
  const ISTDate = new Date(date.getTime() + ISTOffset);

  let hours = ISTDate.getUTCHours();
  const minutes = ISTDate.getUTCMinutes();
  const ampm = hours >= 12 ? 'PM' : 'AM';

  hours = hours % 12;
  hours = hours ? hours : 12;

  return `${hours}:${minutes < 10 ? '0' : ''}${minutes} ${ampm}`;
}

export function showTostMessage(message: string) {
  ToastAndroid.showWithGravityAndOffset(
    message,
    ToastAndroid.BOTTOM,
    ToastAndroid.BOTTOM,
    25,
    50,
  );
}

function getLastMessageTimestampInGroup(groupList: GroupList) {
  if (groupList.messages.length > 0) {
    return new Date(
      groupList.messages[groupList.messages.length - 1].createdAt,
    ).getTime();
  }
  return new Date(groupList.createdAt).getTime();
}

export function sortGroupsByLastActive(groupList: GroupList[]) {
  return groupList.sort(
    (a, b) =>
      getLastMessageTimestampInGroup(b) - getLastMessageTimestampInGroup(a),
  );
}

export function findRemoteUserId(
  participants: Participant[],
  userId: string | undefined,
) {
  if (userId) return participants.find(item => item.id !== userId)?.id;
}

// export function debounce<T extends (...args:  []) => void>(
//   fn: T,
//   delay: number = 800
// ): (...args: Parameters<T>) => void {
//   let timeoutId: ReturnType<typeof setTimeout>;

//   return function (...args: Parameters<T>): void {
//     if (timeoutId) {
//       clearTimeout(timeoutId);
//     }

//     timeoutId = setTimeout(() => {
//       fn(...args );
//     }, delay);
//   };
// }
