import React from 'react';
import {
  View,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Pressable,
} from 'react-native';
import Text from './Text';
import {BlurView} from '@react-native-community/blur';
import BodyCard from './BodyCard';
import {Back} from '../assets/icons';
import {colors} from '../design/colors';
import Spacer from './Spacer';

interface Props {
  visible: boolean;
  onClose: () => void;
  title?: string;
  message: string;
  onConfirm: () => void;
  closeIcon?: boolean;
  rightText?: string;
  showLeftText?: boolean;
}

export default function Alert({
  visible,
  onClose,
  title,
  message,
  onConfirm,
  closeIcon = true,
  rightText,
  showLeftText = true,
}: Props) {
  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={visible}
      onRequestClose={onClose}
      // onDismiss={}
    >
      <BodyCard style={styles.overlay}>
        <BlurView style={styles.blurView} blurType="dark" blurAmount={1} />
        <View style={styles.container}>
          {closeIcon && (
            <>
              <Pressable onPress={onClose}>
                <Back fill={colors.icon.black} />
                <Spacer height={16} />
              </Pressable>
            </>
          )}
          {title && <Text variant="H2_500">{title}</Text>}
          {title && <Spacer height={12} />}
          {message && <Text variant="subText1_500">{message}</Text>}
          <Spacer height={16} />
          <View
            style={[
              styles.buttonContainer,
              {alignSelf: !showLeftText ? 'flex-end' : undefined},
            ]}>
            <TouchableOpacity onPress={onConfirm}>
              <Text variant="subText1_500" color={colors.text.primary}>
                {rightText ? rightText : 'Yes'}
              </Text>
            </TouchableOpacity>
            {showLeftText && (
              <TouchableOpacity onPress={onClose}>
                <Text variant="subText1_500">No</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      </BodyCard>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blurView: {
    ...StyleSheet.absoluteFillObject,
  },
  container: {
    backgroundColor: 'white',
    padding: 24,
    borderRadius: 10,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
});
