import {JwtPayload} from 'jwt-decode';
import {DocumentPickerResponse} from 'react-native-document-picker';
import {PhotoFile} from 'react-native-vision-camera';

export type RootStack = {
  login: undefined;
  welcome: undefined;
  register: undefined;
  otp: {
    phone: string;
    password?: string;
    name?: string;
    userExisted?: boolean;
    isSuperUser: boolean;
    isSuperAdmin: boolean;
    photoUrl?: PhotoFile | DocumentPickerResponse;
    userRole: 'admin' | 'user' | 'agent';
  };
  createStory: undefined;
  adminList: undefined;
  newAdmin: undefined;
  newAgent: undefined;
  verified: {
    isSuperUser: boolean;
    isSuperAdmin: boolean;
  };
  passwordResetSuccessfully: undefined;
  forgotPassword: undefined;
  tab: {screen: string};
  settings: undefined;
  profileSetup?: {
    phone: string;
    userExisted?: boolean;
  };
  profile: undefined;
  ProfileSetting: undefined;
  statusScreen: {
    stories: StoriesType;
    allStories?: StoriesType[];
    index: number;
  };
  accountSetting: undefined;
  notificationSetting: undefined;
  password: {
    name?: string;
    phone: string;
    userExisted: boolean;
    userRole: 'admin' | 'user' | 'agent';
    isSuperUser: boolean;
    photoUrl?: PhotoFile | DocumentPickerResponse;
    isSuperAdmin: boolean;
  };
  confirmPassword: {
    phone: string;
    otp: string;
    newPassword: string;
    userRole: 'admin' | 'user' | 'agent';
  };
  newPassword: {
    phone?: string;
    otp: string;
    userRole: 'admin' | 'user' | 'agent';
  };
  newContact: undefined;
  chatWithNumber: undefined;
  ChatsList: undefined;
  groups: undefined;
  newGroup: undefined;
  chatInfo: {
    chatId: string;
    lastSeen?: string;
  };
  groupAddMembers: {
    chatId: string;
    selectContact?: number[];
  };
  groupInfo: {
    chatId: string;
    name?: string;
    description?: string;
  };
  createGroup: {
    selectContact: ContactList[];
  };
  GroupCongrats: undefined;
  outgoingCall: undefined;
  voiceCall: {
    isIncomingCall: boolean;
    userName?: string;
    avatar?: string | null;
    id?: string;
  };
  chats: {
    id: string;
    img: string | null | undefined;
    name: string;
    lastSeen?: string;
    lastMsg?: string;
    chatType: 'user' | 'group';
    selectedMessagesCount?: number;
    showCamera?: boolean;
    isSuperAdmin?: boolean;
    videoCalling?: () => void;
    agentPhone?: string;
  };
};

export interface ChatParticipant {
  chat_id: string;
  user_id: string;
  last_read_message_id: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface Participant {
  id: string;
  username: string;
  phone: string;
  role: string;
  is_banned: boolean;
  last_active: string;
  bio: string;
  business_name: string | null;
  saved_name: string;
  avatar: {
    id: string;
    url: string;
  } | null;
  ChatParticipant: ChatParticipant;
}

export interface Message {
  id: string;
  message: string;
  createdAt: string;
  message_read: MessageRead[] | null;
}

export interface ChatList {
  id: string;
  number_id: string;
  name: string;
  created_by: string;
  creater_number_id: string;
  description: string | null;
  avatar: string | null;
  token: string;
  createdAt: string;
  updatedAt: string;
  participants: Participant[];
  messages: Message[];
  unread_count: number;
}

export interface GroupList {
  id: string;
  number_id: string | null;
  name: string;
  created_by: string;
  creater_number_id: string | null;
  description: string | null;
  avatar: {id: string; url: string} | null;
  token: string;
  createdAt: string;
  updatedAt: string;
  participants: Participant[];
  messages: Message[];
  unread_count: number;
}

export interface Chats {
  type: string;
  data: ChatMassages;
}

interface ChatMassages {
  count: number;
  rows: ChatRow[];
}
export interface InContact {
  saved_name: string;
}
export interface Sender {
  id: string;
  username: string;
  role: string;
  is_banned: boolean;
  phone: string;
  avatar: {
    id: string;
    url: string;
  } | null;
  in_contacts?: InContact[];
}

export interface Media {
  id: string;
  name?: string;
  type: string;
  ext?: string;
  caption?: string | null;
  tags?: string | null;
  url: string;
  size?: string;
  mime?: string;
}
export interface MessageRead {
  avatar: string;
  seen_at: string;
  user_id: string;
}
export interface ChatRow {
  id: string;
  message: string;
  linked_id: string | null;
  chat_id: string;
  sender_id: string;
  media_id: string | null;
  message_read: MessageRead[] | null;
  message_delivered: boolean;
  createdAt: string;
  updatedAt: string;
  sender: Sender;
  media: Media | null;
  linked_msg: null | undefined;
}

export interface ContactUser {
  avatar: {id: string; url: string} | null;
  id: string;
  isSuperUser: boolean;
  last_active: string;
  phone: string;
  role: string;
  username: string;
}
export interface ContactList {
  contact_id: string;
  contact_user: ContactUser | null;
  email: string;
  id: string;
  name: string;
  phone: string;
  user_id: string;
  username: string;
  isSelected?: boolean;
}

export interface AdminLists {
  access: Access;
  createdAt: string;
  created_by: string;
  email: string | undefined;
  id: string;
  isLoggedIn: boolean;
  is_banned: boolean;
  is_superuser: boolean;
  last_active: string;
  name: string;
  phone: string;
  role: string;
  status: string;
  updatedAt: string;
  username: string;
  business_name: string;
}

export interface Access {
  admin: boolean;
  chat: boolean;
  media: boolean;
  number: boolean;
  user: boolean;
}

export interface AgentList {
  createdAt: string;
  created_by: string;
  desc: string;
  id: string;
  name: string;
  phone: string;
  updatedAt: string;
}

export interface GroupUser {
  id: string;
  phone: string;
  username: string;
  name: string;
  email: null | string;
  role: 'admin' | 'user';
  last_active: string;
  business_name: string;
  avatar: {
    id: string;
    url: string;
  } | null;
  is_banned: boolean;
  isSuperUser: boolean;
}
export interface GroupParticipants {
  id: string;
  chat_id: string;
  number_id: string;
  role: 'admin' | 'user';
  isSuperAdmin: boolean;
  createdAt: string;
  updatedAt: string;
  number: object;
  contact_name: string;
  contact_id: string;
  user: GroupUser;
}

export interface StoriesType {
  user: {
    id: string;
    name: string;
    phone: string;
    contact_user: Pick<ContactUser, 'id' | 'username' | 'isSuperUser'>;
  };
  stories: StoryType[];
}
export interface StoryType {
  id: string;
  user_id: string;
  image_id: string | null;
  image_url: string | null;
  caption: string | null;
  views?: string[];
  background_color: string | null;
  posted_role: 'admin' | 'agent';
  isSuperUser: boolean;
  createdAt: string;
  updatedAt: string;
}
export interface GroupInfoType {
  id: string;
  name: string;
  created_by: string;
  description: string;
  avatar: {id: string; url: string} | null;
  createdAt: string;
  updatedAt: string;
  participants: GroupParticipants[];
}

export interface UserType extends JwtPayload {
  id: string;
  phone: string;
  role: 'admin' | 'user' | 'agent';
  username: string;
  name: string;
  bio: string;
  superAdmin: boolean;
  url: string;
  superUser: boolean;
}
export interface AppCacheType {
  authToken?: string;
  user?: Partial<UserType>;
  initialRouteName: 'tab' | 'welcome' | 'adminList';
  chatLists: ChatList[];
  groupLists: GroupList[];
  contactLists: ContactList[];
  isFcmTokenUpdate: boolean;
}
