import notifee, {AndroidImportance, AndroidStyle} from '@notifee/react-native';

export async function getPermissions() {
  await notifee.requestPermission();
}

export async function onDisplayNotification(
  body: string | undefined,
  title: string | undefined,
  imageUrl: string,
) {
  await notifee.requestPermission();

  const channelId = await notifee.createChannel({
    id: '1',
    name: 'Default Channel name',
    importance: AndroidImportance.HIGH,
  });

  await notifee.displayNotification({
    title: `<b>${title}</b>`,
    body: `<i>${body}</i>`,
    android: {
      channelId,
      importance: AndroidImportance.HIGH,
      smallIcon: 'ic_launcher',
      color: '#4CAF50',
      largeIcon: imageUrl,
      style: {
        type: AndroidStyle.BIGPICTURE,
        picture: 'https://example.com/path/to/image.jpg',
      },
      pressAction: {
        id: 'default',
      },
      actions: [
        {
          title: 'Open App',
          pressAction: {
            id: 'open-app',
          },
        },
        {
          title: 'Dismiss',
          pressAction: {
            id: 'dismiss',
          },
        },
      ],
    },
  });
}
