import React, {useState, useCallback, useRef} from 'react';
import {
  View,
  StyleSheet,
  Pressable,
  Dimensions,
  Image,
  TouchableWithoutFeedback,
  GestureResponderEvent,
} from 'react-native';
import {useRoute, useNavigation, RouteProp} from '@react-navigation/native';
import {RootStack} from '../../utils';
import {
  Carousel,
  Image as ImageComp,
  Input,
  ProgressBar,
  Row,
  Spacer,
  Text,
} from '../../components';
import {getTimeAndMin} from '../../utils/helpers';
import {Back, Send} from '../../assets/icons';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useForm} from 'react-hook-form';
import {colors} from '../../design/colors';
import {MEDIA_BASE_URL_DEBUG, MEDIA_BASE_URL_RELEASE} from '@env';

type StatusScreenRouteProp = RouteProp<RootStack, 'statusScreen'>;
const STORY_DURATION = 5000;

export default function StatusViewScreen() {
  const {
    params: {allStories, index},
  } = useRoute<StatusScreenRouteProp>();
  const MEDIA_BASE_URL = __DEV__
    ? MEDIA_BASE_URL_DEBUG
    : MEDIA_BASE_URL_RELEASE;
  const [currentUserIndex, setCurrentUserIndex] = useState(index);
  const [currentStoryIndex, setCurrentStoryIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [skippedStories, setSkippedStories] = useState<Set<string>>(new Set());
  const [progressKey, setProgressKey] = useState(0);
  const {control, handleSubmit} = useForm();
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const screenWidth = Dimensions.get('window').width;

  const isNavigating = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const resetProgress = useCallback(() => {
    setProgressKey(prev => prev + 1);
  }, []);

  const navigateToGoBack = () => {
    navigation.replace('tab', {screen: 'chatsList'});
  };

  const handleSendMessage = () => {
    console.log('Sending message...');
  };

  const completeCurrentStory = useCallback(() => {
    if (!allStories || isNavigating.current) return;

    const currentUser = allStories[currentUserIndex];
    const nextStoryIndex = currentStoryIndex + 1;

    if (nextStoryIndex < currentUser.stories.length) {
      setCurrentStoryIndex(nextStoryIndex);
      resetProgress();
    } else {
      if (currentUserIndex < allStories.length - 1) {
        setCurrentUserIndex(currentUserIndex + 1);
        setCurrentStoryIndex(0);
        setSkippedStories(new Set());
        resetProgress();
        navigateToGoBack();
      } else {
        navigateToGoBack();
      }
    }
  }, [allStories, currentUserIndex, currentStoryIndex, resetProgress]);

  const handleNextStory = useCallback(() => {
    if (!allStories || isNavigating.current) return;

    isNavigating.current = true;
    const currentUser = allStories[currentUserIndex];

    if (currentStoryIndex < currentUser.stories.length - 1) {
      setSkippedStories(
        prev => new Set([...prev, currentUser.stories[currentStoryIndex].id]),
      );
      setCurrentStoryIndex(prev => prev + 1);
      resetProgress();
    } else if (currentUserIndex < allStories.length - 1) {
      setCurrentUserIndex(prev => prev + 1);
      setCurrentStoryIndex(0);
      setSkippedStories(new Set());
      resetProgress();
      navigateToGoBack();
    } else {
      navigateToGoBack();
    }

    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      isNavigating.current = false;
    }, 200);
  }, [allStories, currentUserIndex, currentStoryIndex, resetProgress]);

  const handlePreviousStory = useCallback(() => {
    if (!allStories || isNavigating.current) return;

    isNavigating.current = true;

    if (currentStoryIndex > 0) {
      const currentUser = allStories[currentUserIndex];
      const previousStoryId = currentUser.stories[currentStoryIndex - 1].id;

      setSkippedStories(prev => {
        const newSkipped = new Set(prev);
        newSkipped.delete(previousStoryId);
        return newSkipped;
      });
      setCurrentStoryIndex(prev => prev - 1);
      resetProgress();
    } else if (currentUserIndex > 0) {
      const previousUser = allStories[currentUserIndex - 1];
      setCurrentUserIndex(prev => prev - 1);
      setCurrentStoryIndex(previousUser.stories.length - 1);
      setSkippedStories(new Set());
      resetProgress();
      navigateToGoBack();
    } else {
      navigateToGoBack();
    }

    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      isNavigating.current = false;
    }, 200);
  }, [allStories, currentUserIndex, currentStoryIndex, resetProgress]);

  const handleLongPress = useCallback(() => {
    setIsPaused(true);
  }, []);

  const handlePressOut = useCallback(() => {
    setIsPaused(false);
  }, []);

  const handleTap = useCallback(
    (e: GestureResponderEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (isNavigating.current) return;

      const touchX = e.nativeEvent.locationX;
      const threshold = screenWidth * 0.4;

      if (touchX < threshold) {
        handlePreviousStory();
      } else {
        handleNextStory();
      }
    },
    [handlePreviousStory, handleNextStory, screenWidth],
  );

  const handleUserChange = useCallback(
    (index: number) => {
      if (!allStories || isNavigating.current) return;

      isNavigating.current = true;
      const isMovingForward = index > currentUserIndex;
      setCurrentUserIndex(index);
      setCurrentStoryIndex(isMovingForward ? 0 : 0);
      setSkippedStories(new Set());
      resetProgress();

      if (timeoutRef.current) clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => {
        isNavigating.current = false;
      }, 200);
    },
    [allStories, currentUserIndex, resetProgress],
  );

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      {allStories && (
        <Carousel
          width={screenWidth}
          loop={false}
          defaultIndex={currentUserIndex}
          modeConfig={{
            snapDirection: 'left',
            stackInterval: 8,
          }}
          autoPlay={false}
          data={allStories}
          snapEnabled={true}
          onSnapToItem={handleUserChange}
          renderItem={({item, index}) => {
            const currentUser = allStories[index];
            const currentStory =
              currentUser.stories[
                currentUserIndex === index ? currentStoryIndex : 0
              ];

            return (
              <>
                <TouchableWithoutFeedback
                  onLongPress={handleLongPress}
                  onPressOut={handlePressOut}
                  onPress={handleTap}>
                  <View style={styles.container}>
                    <View style={styles.headerContainer}>
                      <Row spread style={{gap: 2}}>
                        {item.stories.map((story, idx) => (
                          <ProgressBar
                            key={`${story.id}-${progressKey}`}
                            storyDurations={STORY_DURATION}
                            isPaused={isPaused}
                            active={
                              currentUserIndex === index
                                ? idx === currentStoryIndex
                                : false
                            }
                            onComplete={completeCurrentStory}
                            currentIndex={
                              currentUserIndex === index ? currentStoryIndex : 0
                            }
                            storyIndex={idx}
                            isSkipped={skippedStories.has(story.id)}
                          />
                        ))}
                      </Row>
                      <Spacer height={4} />
                      <Row center>
                        <Pressable onPress={navigateToGoBack}>
                          <Back fill={colors.icon.black} />
                        </Pressable>
                        <ImageComp
                          uri={`https://avatar.iran.liara.run/public/${Math.floor(
                            Math.random() * 90 + 10,
                          )}`}
                          alt={'User Avatar'}
                        />
                        <Spacer width={8} />
                        <View>
                          <Text color={colors.text.white} variant="H2_500">
                            {currentUser?.stories[0].posted_role}
                          </Text>
                          <Text
                            color={colors.text.white}
                            variant="subText2_500">
                            {getTimeAndMin(currentStory?.createdAt)}
                          </Text>
                        </View>
                      </Row>
                    </View>

                    <View style={styles.gestureContainer}>
                      {currentStory?.image_url ? (
                        <Image
                          source={{
                            uri: `${MEDIA_BASE_URL}${currentStory.image_url}`,
                          }}
                          style={styles.statusImage}
                        />
                      ) : (
                        <View
                          style={[
                            styles.statusContainer,
                            {
                              backgroundColor:
                                currentStory?.background_color || '#fff',
                            },
                          ]}>
                          <Text center variant="H2_500">
                            {currentStory?.caption}
                          </Text>
                        </View>
                      )}
                    </View>
                    {/* <View style={styles.inputContainer}>
                    <Row center>
                      <Input
                        style={{flex: 1}}
                        control={control}
                        name="reply"
                        multiline
                        placeholder="Reply"
                      />
                      <Spacer width={4} />
                      <Pressable onPress={handleSubmit(handleSendMessage)}>
                        <Send />
                      </Pressable>
                    </Row>
                  </View> */}
                  </View>
                </TouchableWithoutFeedback>
              </>
            );
          }}
        />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    position: 'absolute',
    top: 0,
    zIndex: 2,
    width: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  gestureContainer: {
    flex: 1,
  },
  statusImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  statusContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inputContainer: {
    position: 'absolute',
    bottom: 0,
    width: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 8,
  },
});
