import * as React from 'react';
import {SharedValue, useSharedValue} from 'react-native-reanimated';
import RNCarousel, {TCarouselProps} from 'react-native-reanimated-carousel';

type Props<T> = TCarouselProps<T> & {
  renderPagination?: (progress: SharedValue<number>) => React.ReactNode;
};

export default function Carousel<T>({renderPagination, ...props}: Props<T>) {
  const progressValue = useSharedValue<number>(0);

  const updateProgress = React.useCallback(
    (_: number, absoluteProgress: number) =>
      (progressValue.value = absoluteProgress),
    [progressValue],
  );

  return (
    <>
      <RNCarousel {...props} onProgressChange={updateProgress} />
      {renderPagination?.(progressValue)}
    </>
  );
}
