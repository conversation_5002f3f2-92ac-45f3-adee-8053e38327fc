import {View, Pressable} from 'react-native';
import React from 'react';
import Modal from './Modal';
import {colors} from '../design/colors';
import {Back, Massage} from '../assets/icons';
import Image from './Image';
import Text from './Text';
import Spacer from './Spacer';
import Row from './Row';

interface ChatProfileProps {
  profileInfo: {
    id: string;
    name: string;
    img: string | null;
    bio?: string;
  } | null;
  onPressBack: () => void;
  onPressStartChat: () => void;
}

export default function ChatProfileModal({
  profileInfo,
  onPressBack,
  onPressStartChat,
}: ChatProfileProps) {
  return (
    <Modal visible={!!profileInfo} onClose={onPressBack}>
      <View
        style={{
          flex: 1,
          alignSelf: 'center',
          justifyContent: 'center',
        }}>
        <View
          style={{
            backgroundColor: colors.white,
            padding: 24,
            borderRadius: 14,
          }}>
          <Pressable onPress={onPressBack}>
            <Back />
          </Pressable>
          <View style={{paddingHorizontal: 24}}>
            <Image
              alt=""
              uri={profileInfo?.img ?? null}
              style={{height: 180, width: 180, borderRadius: 100}}
            />
            <Text center variant="H2_500">
              {profileInfo?.name}
            </Text>
            <Text center variant="subText2_500">
              {profileInfo?.bio}
            </Text>
            <Spacer height={12} />

            <Pressable onPress={onPressStartChat}>
              <Row
                style={{
                  backgroundColor: colors.primaryB50,
                  borderRadius: 20,
                  paddingVertical: 8,
                }}
                middle>
                <Massage fill={colors.primaryB200} height={16} width={16} />
                <Spacer width={8} />
                <Text center variant="subText2_500" color={colors.primaryB200}>
                  Start chat
                </Text>
              </Row>
            </Pressable>
            {/* //TODO:- view more backend is not ready  */}
            {/* <Spacer height={20} />
          <Text center variant="subText2_500" color={colors.primaryB200}>
            View More Info
          </Text> */}
          </View>
        </View>
      </View>
    </Modal>
  );
}
