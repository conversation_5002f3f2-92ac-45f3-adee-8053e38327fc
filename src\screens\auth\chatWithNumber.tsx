import {View, Alert} from 'react-native';
import React from 'react';
import {
  BodyCard,
  Cta,
  Input,
  KeyboardAvoidingView,
  Row,
  Spacer,
  Text,
} from '../../components';
import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {logInSchema} from '../../utils/validationSchema';
import {z} from 'zod';
import {chatWithNumberBySearch, createRoomForChat} from '../../utils/apis';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../../utils';
import {IndiaFlag, Phone} from '../../assets/icons';
import {useStore} from '../../utils/hooks';
import {showTostMessage} from '../../utils/helpers';

type FormData = z.infer<typeof logInSchema>;
const startView = (
  <Row>
    <IndiaFlag />
    <Spacer width={8} />
    <Text variant="subText1_500">+91</Text>
  </Row>
);
export default function ChatWithNumber() {
  const {
    control,
    handleSubmit,
    formState: {isValid},
    setError,
    // setValue,
  } = useForm<FormData>({
    resolver: zodResolver(logInSchema),
  });

  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const {myNumber} = useStore(state => ({myNumber: state.user?.phone}));
  async function onSubmit({phone}: FormData) {
    try {
      if (myNumber === phone) {
        return showTostMessage('self chat is not allowed');
      }
      const data = await chatWithNumberBySearch(phone);
      if (data.status === 200 && data.ok) {
        const res = await data.json();
        const chatData = await createRoomForChat(res.id);
        const resChatData = await chatData.json();
        if (chatData.status === 200 && chatData.ok) {
          navigation.navigate('chats', {
            id: resChatData.id,
            img: res?.avatar?.url,
            name: res.saved_name ? res.saved_name : res.phone,
            lastSeen: res.last_active,
            lastMsg: res.last_active,
            chatType: 'user',
          });
        } else {
          showTostMessage('not working');
        }
      } else if ((data.status === 404 || data.status === 403) && !data.ok) {
        setError('phone', {message: "Phone number doesn't exit"});
        // setValue('phone', '');
      }
    } catch (error) {
      Alert.alert('Error', 'something went wrong!');
    }
  }
  return (
    <BodyCard padTop padBottom>
      <KeyboardAvoidingView>
        <View>
          <Input
            labelIcon={<Phone />}
            control={control}
            name="phone"
            label="Phone number"
            placeholder="9999 999 999"
            maxLength={10}
            keyboardType="number-pad"
            startView={startView}
          />
        </View>
      </KeyboardAvoidingView>
      <Cta
        title="Send a message"
        onPress={handleSubmit(onSubmit)}
        disabled={!isValid}
      />
    </BodyCard>
  );
}
