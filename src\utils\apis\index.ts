export {globalSocket} from './webSocket';

export {
  postUserOtp,
  userLogin,
  userCreateAccount,
  userLogOut,
  userResetPassword,
  userResetOtp,
  userRoomChat,
  saveNewContact,
  createRoomForChat,
  createGroup,
  sendFilAndPhotos,
  sendCameraPhoto,
  adminLoginOtp,
  adminLogin,
  adminLogOut,
  agentResendOtp,
  agentResetPassword,
  agentLogin,
  addGroupMembers,
  createStoryWithCaption,
  createStoryWithGallery,
  createStoryWithCamera,
  uploadAudio,
  updateFcmToken,
  saveNewAdmin,
  saveNewAgent,
} from './postApis';

export {
  getUserProfile,
  getUserByPhoneOrUserName,
  getUserById,
  getUserByPhone,
  getUserType,
  chatWithNumberBySearch,
  getContactBySearch,
  getContactList,
  getChatMessageSearch,
  getAgentList,
  getAgentChatList,
  getGroupInfo,
  getStory,
  getStoryById,
  getLastActiveById,
  getAdminList,
} from './getApis';

export {
  updateUserProfile,
  updateUserAvatar,
  adminAgentLogOut,
  removeUserFromGroup,
  updateUserGroupInfo,
  updateGroupAvatar,
  exitGroup,
} from './putApis';

export {deleteStoryById} from './deleteApis';
