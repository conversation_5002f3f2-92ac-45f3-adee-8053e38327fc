import {z} from 'zod';

export const logInSchema = z.object({
  phone: z
    .string()
    .min(10, 'Phone number must be exactly 10 digits')
    .max(10, 'Phone number must be exactly 10 digits')
    .regex(
      /^[6-9][0-9]{9}$/,
      'Phone number must start with 6-9 and contain only digits (no spaces or special characters)',
    ),
});

export const otpSchema = z.object({
  otp1: z.string().max(1, 'Otp is required').min(1, 'Otp is required'),
  otp2: z.string().max(1, 'Otp is required').min(1, 'Otp is required'),
  otp3: z.string().max(1, 'Otp is required').min(1, 'Otp is required'),
  otp4: z.string().max(1, 'Otp is required').min(1, 'Otp is required'),
});

export const PasswordSchema = z.object({
  password: z
    .string()
    .max(20, 'Password must be at most 20 characters')
    .min(8, 'Password is most 8 characters')
    .refine(val => val === val.trim(), {
      message: 'Password cannot contain leading or trailing spaces',
    }),
});

export const NewContactSchema = z.object({
  name: z.string().min(3, 'Name is required'),
  phone: z
    .string()
    .min(10, 'Phone number must be at least 10 digits')
    .max(10, 'Phone number must be at most 10 digits')
    .regex(
      /^[6-9]\d{9}$/,
      'Phone number must only contain digits and start with a digit from 6 to 9',
    ),
});

export const NewAdminSchema = z.object({
  name: z.string().min(3, 'Name is required'),
  business_name: z.string().min(3, 'Buisness name is required'),
  phone: z
    .string()
    .min(10, 'Phone number must be at least 10 digits')
    .max(10, 'Phone number must be at most 10 digits')
    .regex(
      /^[6-9]\d{9}$/,
      'Phone number must only contain digits and start with a digit from 6 to 9',
    ),
  password: z
    .string()
    .max(20, 'Password must be at most 20 characters')
    .min(8, 'Password is most 8 characters')
    .refine(val => val === val.trim(), {
      message: 'Password cannot contain leading or trailing spaces',
    }),
});

export const NewAgentSchema = z.object({
  name: z.string().min(3, 'Name is required'),
  phone: z
    .string()
    .min(10, 'Phone number must be at least 10 digits')
    .max(10, 'Phone number must be at most 10 digits')
    .regex(
      /^[6-9]\d{9}$/,
      'Phone number must only contain digits and start with a digit from 6 to 9',
    ),
  password: z
    .string()
    .max(20, 'Password must be at most 20 characters')
    .min(8, 'Password is most 8 characters')
    .refine(val => val === val.trim(), {
      message: 'Password cannot contain leading or trailing spaces',
    }),
});

export const profileSettingSchema = z.object({
  name: z
    .string()
    // .min(3, 'Name is required')
    // .refine(val => val === val.trim(), {
    //   message: 'Name cannot contain leading or trailing spaces',
    // })
    .optional(),
  bio: z
    .string()
    .min(3, 'Bio is required')
    // .refine(val => val === val.trim(), {
    //   message: 'Bio cannot contain leading or trailing spaces',
    // })
    .optional(),
});

export const ConfirmPasswordSchema = (password: string) =>
  z
    .object({
      confirmPassword: z.string({message: 'Confirm Password is required'}),
    })
    .refine(data => data.confirmPassword === password, {
      message: 'Passwords do not match',
      path: ['confirmPassword'],
    });

export const ChatSearchSchema = z.object({
  search: z.string({required_error: '', coerce: true}).min(0, {message: ''}),
  number_id: z.string({required_error: '', coerce: true}).min(0, {message: ''}),
});

export const GroupSearchSchema = z.object({
  search: z.string({required_error: '', coerce: true}).min(0, {message: ''}),
});

export const ChatSchema = z.object({
  textMessage: z.string({required_error: ''}).min(1, {message: ''}).trim(),
});

export const NewCreateGroupSchema = z.object({
  name: z.string().min(3, 'Name is required'),
  description: z.string().optional(),
});

export const StoryTextSchema = z.object({
  caption: z.string(),
});
export const ProfileSetupSchema = z.object({
  name: z
    .string()
    .min(3, 'Name is required')
    .refine(val => val === val.trim(), {
      message: 'Name cannot start or end with space',
    }),
});
