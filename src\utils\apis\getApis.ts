import {API_URL_DEBUG, API_URL_RELEASE} from '@env';

console.log({API_URL_DEBUG, API_URL_RELEASE}, 'get api');

const API_URL = __DEV__ ? API_URL_DEBUG : API_URL_RELEASE;
export async function getUserProfile() {
  return await fetch(`${API_URL}/user/profile`);
}

export async function userLastActive(id: string) {
  return await fetch(`${API_URL}/user/last-active/${id}`);
}

export async function getUserByPhoneOrUserName(id: string) {
  return await fetch(`${API_URL}/user/search/${id}`);
}

export async function getUserById(id: string) {
  return await fetch(`${API_URL}/user/${id}`);
}

export async function getUserByPhone(phone: string) {
  return await fetch(`${API_URL}/user/phone/${phone}`);
}

export async function getUserType(phone: string) {
  return await fetch(`${API_URL}/user/knowuser?phone=${phone}`);
}

export async function connectToWS(id: string) {
  return await fetch(`${API_URL}/chat/connect/${id}`);
}

export async function chatWithNumberBySearch(search: string) {
  return await fetch(`${API_URL}/user/search/${search}`);
}

export async function getContactBySearch(search: string) {
  return await fetch(`${API_URL}/contact/search?search=${search}`);
}

//! NOTE:- Page and limit are not handled from Backend.
export async function getContactList(page: string = '1', limit: string = '0') {
  return await fetch(`${API_URL}/contact?page=${page}&limit?=${limit}`);
}

export async function getChatMessageSearch(search: string, number_id?: string) {
  return await fetch(
    `${API_URL}/chat/message?number_id=${number_id}&search=${search}`,
  );
}

export async function getAgentList() {
  return await fetch(`${API_URL}/number`);
}

export async function getAgentChatList(phone: string) {
  return await fetch(`${API_URL}/chat/number?phone=${phone}`);
}
export async function getGroupInfo(chat_id: string) {
  return await fetch(`${API_URL}/group/info/${chat_id}`);
}

export async function getStory() {
  return await fetch(`${API_URL}/story`);
}

export async function getStoryById(id: string) {
  return await fetch(`${API_URL}/story/${id}`);
}

export async function getLastActiveById(id: string) {
  return await fetch(`${API_URL}/user/last-active/${id}`);
}

export async function getAdminList() {
  return await fetch(`${API_URL}/admin`);
}
