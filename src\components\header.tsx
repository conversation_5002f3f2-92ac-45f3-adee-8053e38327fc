import {Pressable, StyleSheet, View} from 'react-native';
import React from 'react';
import Row from './Row';
import {Back, Dots, Logo, Massage} from '../assets/icons';
import Spacer from './Spacer';
import Text from './Text';
import {colors} from '../design/colors';

interface Props {
  title?: string;
  badgesTitle?: string;
  showLogo?: boolean;
  showBadgesIcon?: boolean;
  onPressThreeDots?: () => void;
  onPressBadges?: () => void;
  onPressBack?: () => void;
}

export default function Header({
  title,
  badgesTitle,
  showLogo = false,
  onPressThreeDots,
  onPressBadges,
  showBadgesIcon = false,
  onPressBack,
}: Props) {
  return (
    <>
      <View style={styles.headerContainer}>
        <Row center>
          {showLogo && (
            <>
              <Logo width={42} height={42} />
              <Spacer width={8} />
            </>
          )}
          {onPressBack && (
            <Pressable onPress={onPressBack}>
              <Back />
            </Pressable>
          )}
          <Text variant="H2_500" middle>
            {title}
          </Text>
        </Row>
        <Row center>
          {showBadgesIcon && (
            <Pressable
              style={({pressed}) => [styles.button, pressed && styles.pressed]}
              onPress={onPressBadges}>
              {({pressed}) => (
                <Row middle style={styles.row}>
                  <Massage
                    fill={pressed ? colors.icon.white : colors.icon.B200}
                  />
                  <Spacer width={2} />
                  <Text
                    color={pressed ? colors.text.white : colors.text.primary}
                    variant="subText2_500">
                    {badgesTitle}
                  </Text>
                </Row>
              )}
            </Pressable>
          )}
          {onPressThreeDots && (
            <Pressable onPress={onPressThreeDots}>
              <Dots />
            </Pressable>
          )}
        </Row>
      </View>
      <Spacer height={8} />
    </>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  row: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },

  button: {
    backgroundColor: colors.primaryB50,
    borderRadius: 24,
  },
  pressed: {
    backgroundColor: colors.primaryB200,
  },
});
