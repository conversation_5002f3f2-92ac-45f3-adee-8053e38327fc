import {Animated, Easing, View} from 'react-native';
import React from 'react';
import {BodyCard, Spacer, Text} from '../../components';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import LottieView from 'lottie-react-native';
import {RootStack} from '../../utils';

const AnimatedLottieView = Animated.createAnimatedComponent(LottieView);

export default function PasswordResetSuccessfully() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  React.useEffect(() => {
    while (navigation.canGoBack()) {
      navigation.pop();
    }
    const timeOut = setTimeout(() => {
      navigation.replace('login');
    }, 2000);

    return () => clearInterval(timeOut);
  }, []);

  const animationProgress = React.useRef(new Animated.Value(0));
  React.useEffect(() => {
    Animated.timing(animationProgress.current, {
      toValue: 1,
      duration: 2000,
      easing: Easing.linear,
      useNativeDriver: false,
    }).start();
  }, []);
  return (
    <BodyCard>
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          flex: 1,
        }}>
        <AnimatedLottieView
          source={require('../../assets/animations/password.json')}
          speed={1}
          style={{height: 196, width: 196}}
          autoPlay
          loop
          progress={animationProgress.current}
        />
        <Text variant="subText1_500" center>
          Password Updated Successfully
        </Text>
        <Spacer height={12} />
        <Text variant="subText2" center>
          Awesome 😃 Your password has been changed. You can login with your new
          password.
        </Text>
      </View>
    </BodyCard>
  );
}
