import React from 'react';
import {<PERSON><PERSON>ard, Header, Image, Row, Spacer, Text} from '../../components';
import {Pressable, StyleSheet, View} from 'react-native';
import {Block, Chevron, Massage, Phone, Video} from '../../assets/icons';
import {colors} from '../../design/colors';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {RootStack} from '../../utils';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

type ChatInfoProps = RouteProp<RootStack, 'chatInfo'>;

export default function ChatInfo() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const {
    params: {lastSeen},
  } = useRoute<ChatInfoProps>();

  function navigateToBack() {
    navigation.goBack();
  }

  return (
    <BodyCard padTop>
      <Header
        onPressBack={navigateToBack}
        // onPressThreeDots={() => console.log('first')}
      />
      <Image
        alt={''}
        uri={null}
        style={{height: 180, width: 180, borderRadius: 90}}
      />
      <Spacer height={12} />
      <Row middle style={{gap: 12}}>
        <Pressable
          style={styles.pressable}
          accessibilityLabel="send message"
          accessibilityRole="button"
          onPress={navigateToBack}>
          <Massage height={24} width={24} fill={colors.icon.B200} />
        </Pressable>
        <Pressable
          style={styles.pressable}
          accessibilityLabel="make a voice call"
          accessibilityRole="button">
          <Phone height={24} width={24} fill={colors.icon.B200} />
        </Pressable>
        <Pressable
          style={styles.pressable}
          accessibilityLabel="make a video call"
          accessibilityRole="button">
          <Video height={24} width={24} fill={colors.icon.B200} />
        </Pressable>
      </Row>
      <Spacer height={12} />
      <View style={styles.view}>
        <Text variant="subText2_500">Busy</Text>
        <Text>{lastSeen}</Text>
      </View>
      <Spacer height={12} />
      <View style={styles.view}>
        <Row spread>
          <Text variant="subText2_500">Media, links and docs</Text>
          <Row center>
            <Text variant="subText2_500">234</Text>
            <Chevron />
          </Row>
        </Row>
      </View>
      <Spacer height={12} />
      <Row>
        <Block />
        <Spacer width={8} />
        <Text variant="subText1_500">Block Harsh</Text>
      </Row>
    </BodyCard>
  );
}

const styles = StyleSheet.create({
  pressable: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderColor: colors.icon.B20,
    borderWidth: 1,
  },
  view: {
    paddingVertical: 14,
    paddingHorizontal: 14,
    borderRadius: 12,
    borderColor: colors.icon.B20,
    borderWidth: 1,
  },
});
