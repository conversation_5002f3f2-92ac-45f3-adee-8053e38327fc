import * as React from 'react';
import {Modal, ModalProps, Pressable, StyleSheet, View} from 'react-native';

interface Props extends ModalProps {
  onClose: () => void;
}

export default function BottomSheet({children, onClose, ...modalProps}: Props) {
  return (
    <Modal
      animationType="fade"
      transparent
      {...modalProps}
      onRequestClose={onClose}>
      <Pressable onPress={onClose} style={styles.backdrop} />
      <View style={styles.bottomSheet}>{children}</View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  backdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  bottomSheet: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
  },
});
