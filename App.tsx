import 'react-native-reanimated';
import 'react-native-gesture-handler';
import React, {useState} from 'react';
import {
  Al<PERSON>,
  Linking,
  SafeAreaView,
  StatusBar,
  StyleSheet,
  View,
} from 'react-native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {NavigationContainer} from '@react-navigation/native';
import AuthNavigators from './src/navigation/authNavigators';
import BootSplash from 'react-native-bootsplash';
import {getUserProfile} from './src/utils/apis/getApis';
import {useStore} from './src/utils/hooks';
import NetInfo from '@react-native-community/netinfo';
import {colors} from './src/design/colors';
import {Text} from './src/components';
import {listenForMessage} from './src/utils/pushNotification/firebase';
import {getPermissions} from './src/utils/pushNotification/pushNotification';
import inCallManager from 'react-native-incall-manager';

const deepLinkConfig = {
  screens: {
    chats: 'chats/:id/:name/:chatType',
    ProfileSetting: 'profileSetting',
    voiceCall: 'voiceCall/:isIncomingCall/:userName/:avatar',
  },
};

const linking = {
  prefixes: ['n2chat://app', 'https://n2chat.com', 'https://dev.n2chat.com'],
  config: deepLinkConfig,
};
function App() {
  const [loading, setLoading] = useState(true);
  // const [isConnected, setIsConnected] = useState<boolean | null>(null);

  const {setUser, setInitialRouteName, cleanLocalCache} = useStore(state => ({
    setUser: state.setUser,
    setInitialRouteName: state.setInitialRouteName,
    cleanLocalCache: state.cleanLocalCache,
  }));

  async function onReady() {
    try {
      getUserProfile()
        .then(res => {
          if (res.ok && res.status === 200) {
            return res.json();
          } else if (!res.ok) {
            setInitialRouteName('welcome');
            setLoading(false);
            cleanLocalCache();
          }
          return false;
        })
        .then(data => {
          if (data.isSuperAdmin && data.isSuperUser) {
            setInitialRouteName('adminList');
          } else {
            setInitialRouteName('tab');
          }
          if (data) {
            setUser({
              phone: data.phone,
              id: data.id,
              role: data.role,
              username: data.username,
              name: data.name,
              superUser: data.isSuperUser,
              superAdmin: data.isSuperAdmin,
              url: data?.avatar?.url,
              bio: data?.bio,
            });
          }
          setLoading(false);
        })
        .finally(async () => {
          await BootSplash.hide({
            fade: true,
          });
        });
    } catch (e) {
      Alert.alert('Error');
    }
  }

  function handleDeepLinking(url: string, appState: string) {
    console.log(url, 'url', appState, 'appState');
  }

  React.useEffect(() => {
    if (__DEV__) {
      inCallManager.setKeepScreenOn(__DEV__);
    }

    const fetchNetworkState = async () => {
      await NetInfo.fetch();
    };
    fetchNetworkState();
    listenForMessage();
    getPermissions();
    // const unsubscribe = NetInfo.addEventListener(state => {
    //   setIsConnected(state.isConnected && (state.isInternetReachable ?? true));
    // });

    Linking.getInitialURL().then(url => {
      if (url) {
        handleDeepLinking(url, 'CLOSED');
      }
    });

    Linking.addEventListener('url', ({url}) => {
      handleDeepLinking(url, 'BACKGROUND');
    });

    // return () => unsubscribe();
  }, []);

  return (
    <GestureHandlerRootView style={styles.flex}>
      <NavigationContainer
        onReady={onReady}
        linking={linking}
        fallback={<Text>Loading...</Text>}>
        <SafeAreaView style={styles.flex}>
          <StatusBar
            animated
            barStyle="light-content"
            backgroundColor="#0000009e"
          />
          {!loading && <AuthNavigators />}
          {/* {!isConnected && (
            <View
              style={{
                height: 24,
                width: '100%',
                backgroundColor: colors.error,
              }}>
              <Text center variant="subText1_500" color={colors.text.white}>
                No internet
              </Text>
            </View>
          )} */}
        </SafeAreaView>
      </NavigationContainer>
    </GestureHandlerRootView>
  );
}

export default App;

const styles = StyleSheet.create({
  flex: {
    flex: 1,
  },
});
