import React, {useCallback, useState} from 'react';
import {
  View,
  Pressable,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import {BodyCard, Fab, Header, Input, Spacer, Text} from '../../components';
import {AddContact, SearchIcon} from '../../assets/icons';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {AdminLists, RootStack} from '../../utils';
import {getAdminList} from '../../utils/apis';
import {heightPercentageToDP} from 'react-native-responsive-screen';
import {colors} from '../../design/colors';
import MassageCard from '../../components/massageCard';
import {useForm} from 'react-hook-form';

export default function AdminList() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {control} = useForm();

  const [adminList, setAdminList] = useState<AdminLists[] | undefined>(
    undefined,
  );
  const [adminSearchList, setAdminSearchList] = useState<
    AdminLists[] | undefined
  >(undefined);

  const [loading, setLoading] = useState(true);

  function handleThreeDots() {
    navigation.navigate('settings');
  }

  function navigateToNewAdmin() {
    navigation.navigate('newAdmin');
  }

  const getAdminLists = useCallback(async () => {
    try {
      setLoading(true);
      const response = await getAdminList();
      const data = await response.json();
      setAdminList(data);
      setAdminSearchList(data);
    } catch (err) {
      Alert.alert('Error', 'something went wrong try after some time!!!');
    } finally {
      setLoading(false);
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      getAdminLists();
    }, []),
  );

  const onChangeSearch = (search: string) => {
    const filterAdminList = adminSearchList?.filter(item => {
      return (
        item.name.toLowerCase().includes(search.toLowerCase()) ||
        item.phone.toLowerCase().includes(search.toLowerCase()) ||
        item.business_name.toLowerCase().includes(search.toLowerCase())
      );
    });

    setAdminList(filterAdminList);
  };

  return (
    <BodyCard padTop>
      <Header title="N2 Chat" showLogo onPressThreeDots={handleThreeDots} />
      <View style={{flex: 1}}>
        <Input
          control={control}
          name="search"
          placeholder="Search admins"
          border={false}
          placeholderTextColor={colors.primaryB100}
          onChangeText={onChangeSearch}
          endView={
            <Pressable hitSlop={{top: 12, bottom: 12, right: 16}}>
              <SearchIcon />
            </Pressable>
          }
          inputStyle={{backgroundColor: colors.primaryB50}}
        />

        <FlatList
          refreshControl={
            <RefreshControl refreshing={loading} onRefresh={getAdminLists} />
          }
          data={adminList}
          showsVerticalScrollIndicator={false}
          renderItem={({item}) => (
            <MassageCard
              loading={loading}
              id={item?.id}
              img={null}
              name={item?.name}
              phone={item.phone}
              username={item.business_name}
              massageCount={0}
              bio={'bio'}
            />
          )}
          keyExtractor={item => item.id.toString()}
          ItemSeparatorComponent={() => <Spacer height={12} />}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text variant="subText1" center color={colors.text.B100}>
                No Admin found!{'\n'}Please add a Admin to start
              </Text>
            </View>
          }
        />
      </View>
      <Fab onPress={navigateToNewAdmin} icon={<AddContact />} />
    </BodyCard>
  );
}

const styles = StyleSheet.create({
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: heightPercentageToDP(20),
  },
});
