import {Pressable, View} from 'react-native';
import React from 'react';
import {
  BodyCard,
  Text,
  BackBtn,
  Spacer,
  Input,
  Cta,
  KeyboardAvoidingView,
} from '../../components';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../../utils';
import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {CloseEye, OpenEye} from '../../assets/icons';
import {ConfirmPasswordSchema} from '../../utils/validationSchema';
import {colors} from '../../design/colors';
import {agentResetPassword, userResetPassword} from '../../utils/apis/postApis';

type FormData = {
  confirmPassword: string;
};

type confirmPasswordRouteProp = RouteProp<RootStack, 'confirmPassword'>;

export default function ConfirmPassword() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const [showPassword, setShowPassword] = React.useState(true);

  const {
    params: {phone, otp, newPassword, userRole},
  } = useRoute<confirmPasswordRouteProp>();

  const passwordSchema = ConfirmPasswordSchema(newPassword);
  const {
    control,
    handleSubmit,
    formState: {isValid},
    setError,
  } = useForm<FormData>({
    resolver: zodResolver(passwordSchema),
  });

  function togglePassword() {
    setShowPassword(!showPassword);
  }
  async function onSubmit({confirmPassword}: FormData) {
    try {
      if (userRole === 'admin') {
        const response = await agentResetPassword(phone, confirmPassword, otp);
        if (response.ok && response.status === 200) {
          navigation.replace('passwordResetSuccessfully');
        }
        return;
      }
      const response = await userResetPassword(phone, confirmPassword, otp);

      if (response.ok && response.status === 200) {
        navigation.replace('passwordResetSuccessfully');
      } else {
        const {error} = await response.json();

        if (error) {
          setError('confirmPassword', {
            message: 'Incorrect otp or phone number',
            type: 'validate',
          });
        }
      }
    } catch (error) {
      console.error(error);
    }
  }

  return (
    <BodyCard padBottom padTop>
      <KeyboardAvoidingView>
        <BackBtn />
        <View>
          <Text variant="H2_500">Confirm password</Text>
          <Spacer height={4} />
          <Text variant="subText2" color={colors.text.B40}>
            Please re-enter the password to confirm the change.
          </Text>
        </View>
        <Spacer height={16} />
        <Input
          control={control}
          maxLength={20}
          name="confirmPassword"
          secureTextEntry={showPassword}
          endView={
            <Pressable onPress={togglePassword}>
              {showPassword ? <OpenEye /> : <CloseEye />}
            </Pressable>
          }
        />
      </KeyboardAvoidingView>
      <Cta
        disabled={!isValid}
        appearance="primary"
        title="Continue"
        onPress={handleSubmit(onSubmit)}
      />
    </BodyCard>
  );
}
