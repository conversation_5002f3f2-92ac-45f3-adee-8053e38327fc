import React, {useContext} from 'react';
import {Animated, Easing, View} from 'react-native';
import {BodyCard, Cta, Spacer, Text} from '../../components';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../../utils';
import LottieView from 'lottie-react-native';
import {WebSocketContext} from '../../utils/providers/WebSocket';
const AnimatedLottieView = Animated.createAnimatedComponent(LottieView);

export default function GroupCongrats() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {reconnect} = useContext(WebSocketContext);

  function viewGroup() {
    navigation.navigate('groups');
  }
  const animationProgress = React.useRef(new Animated.Value(0));
  React.useEffect(() => {
    reconnect();
    Animated.timing(animationProgress.current, {
      toValue: 1,
      duration: 2000,
      easing: Easing.linear,
      useNativeDriver: false,
    }).start();
  }, []);
  return (
    <BodyCard>
      <View
        style={{
          justifyContent: 'center',
          alignItems: 'center',
          flex: 1,
        }}>
        <AnimatedLottieView
          source={require('../../assets/animations/highFive.json')}
          speed={1}
          style={{width: 300, height: 300}}
          autoPlay
          loop
          progress={animationProgress.current}
        />

        <Text variant="H2_500" center>
          Congrats 🙌 {'\n'} Your group has been created
        </Text>
        <Spacer height={25} />
        <Cta title="View Group" onPress={viewGroup} style={{width: '100%'}} />
      </View>
    </BodyCard>
  );
}
