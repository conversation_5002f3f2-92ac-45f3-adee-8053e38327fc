import {
  Pressable,
  StyleSheet,
  PressableProps,
  GestureResponderEvent,
  StyleProp,
  ViewStyle,
} from 'react-native';
import React from 'react';
import {colors} from '../design/colors';
import {
  heightPercentageToDP as Hp,
  widthPercentageToDP as Wp,
} from 'react-native-responsive-screen';
import Text from './Text';
import Row from './Row';
import {SvgProps} from 'react-native-svg';
import Spacer from './Spacer';

interface Props extends PressableProps {
  appearance?: 'primary' | 'secondary' | 'danger' | 'outline' | 'dangerOutline';
  title: string;
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  onPress?: (event: GestureResponderEvent) => void;
  icon?: React.ReactElement<SvgProps>;
}

export default function Cta({
  appearance = 'primary',
  title,
  disabled,
  style,
  icon,
  onPress,
  ...PressableProps
}: Props) {
  const styles = StyleSheet.create({
    base: {
      paddingVertical: Hp(2),
      paddingHorizontal: 12,
      backgroundColor: (() => {
        if (disabled) {
          return colors.Cta.disabled;
        }
        switch (appearance) {
          case 'primary':
            return colors.Cta.active;
          //?Note:- secondary color is not in use
          case 'secondary':
            return colors.Cta.success;
          case 'danger':
            return colors.Cta.danger;
          case 'outline':
            return colors.Cta.white;
          case 'dangerOutline':
            return colors.Cta.white;
          default:
            undefined;
        }
      })(),
      justifyContent: 'center',
      alignItems: 'center',
      borderRadius: Wp(7.5),
      borderWidth:
        appearance === 'outline' || appearance === 'dangerOutline' ? 1 : 0,
      borderColor: (() => {
        switch (appearance) {
          case 'dangerOutline':
            return colors.error;
          case 'outline':
            return colors.borders.B50;
          default:
            return '';
        }
      })(),
      opacity: disabled ? 0.5 : 1,
    },
    pressed: {
      backgroundColor:
        appearance === 'dangerOutline' ? colors.Cta.danger : colors.Cta.pressed,
    },
    text: {
      color: (() => {
        switch (appearance) {
          case 'danger':
            return colors.Cta.danger;
          case 'dangerOutline':
            return colors.text.error;
          case 'outline':
            return colors.text.B50;
          default:
            return colors.Cta.white;
        }
      })(),
    },
    textError: {
      color: colors.text.white,
    },
  });
  return (
    <>
      <Pressable
        {...PressableProps}
        style={({pressed}) => [styles.base, pressed && styles.pressed, style]}
        onPress={onPress}
        disabled={disabled}>
        {({pressed}) => (
          <Row center>
            {icon && (
              <>
                {React.cloneElement(icon, {
                  fill: pressed ? colors.icon.white : colors.icon.B50,
                })}
                <Spacer width={8} />
              </>
            )}
            <Text
              variant="subText1_500"
              color={
                appearance === 'dangerOutline'
                  ? colors.text.error
                  : appearance === 'outline'
                  ? colors.text.B50
                  : colors.text.white
              }
              style={[
                pressed && appearance === 'dangerOutline'
                  ? styles.textError
                  : pressed && appearance === 'outline'
                  ? {color: colors.text.white}
                  : styles.text,
              ]}>
              {title}
            </Text>
          </Row>
        )}
      </Pressable>
    </>
  );
}
