import * as React from 'react';
import {View} from 'react-native';

interface Size {
  height?: undefined;
  width?: undefined;
  size: number;
}

interface Height {
  height: number;
  width?: undefined;
  size?: undefined;
}

interface Width {
  height?: undefined;
  width: number;
  size?: undefined;
}

type Props = Size | Height | Width;

export default function Spacer({size, height, width}: Props) {
  const style = {
    height: height ?? size,
    width: width ?? size,
  };

  return <View style={style} />;
}
