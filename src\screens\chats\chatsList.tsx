import React, {useState, useEffect, useRef, useContext} from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  Pressable,
  RefreshControl,
  Keyboard,
  TextInput,
} from 'react-native';
import {useForm} from 'react-hook-form';
import {
  BodyCard,
  Input,
  Spacer,
  MassageCard,
  Header,
  Fab,
  Text,
  Status,
  ChatProfileModal,
} from '../../components';
import {colors} from '../../design/colors';
import {SearchIcon, Massage} from '../../assets/icons';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {ChatList, RootStack, StoriesType} from '../../utils';
import {useStore} from '../../utils/hooks';
import {getStory, updateFcmToken} from '../../utils/apis';
import {z} from 'zod';
import {ChatSearchSchema} from '../../utils/validationSchema';
import {zodResolver} from '@hookform/resolvers/zod';
import {
  convertIntoLocalTime,
  findRemoteUserId,
  findUserAvatar,
  findUserBio,
  findUserName,
  findUserRole,
} from '../../utils/helpers';
import {heightPercentageToDP as hp} from 'react-native-responsive-screen';
import {WebSocketContext} from '../../utils/providers/WebSocket';
import {FloatingCallBar} from '../auth/floatingCallBar';
import {WebRTCContext} from '../../utils/providers/WebRtcProvider';
import messaging from '@react-native-firebase/messaging';

type FormData = z.infer<typeof ChatSearchSchema>;

export default function ChatsList() {
  const {control, handleSubmit, setValue} = useForm<FormData>({
    resolver: zodResolver(ChatSearchSchema),
  });
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const {isCallActive} = React.useContext(WebRTCContext);

  const {setCurrentOpenChat, reconnect, setRemoteUserId} =
    useContext(WebSocketContext);
  const {chatLists, user, isFcmTokenUpdate, setIsFcmTokenUpate} = useStore(
    state => ({
      chatLists: state.chatLists,
      user: state.user,
      isFcmTokenUpdate: state.isFcmTokenUpdate,
      setIsFcmTokenUpate: state.setIsFcmTokenUpdate,
    }),
  );

  const [fullChatList, setFullChatList] = useState<ChatList[]>([]);
  const [refreshing] = useState(false);
  const [story, setStroy] = React.useState<StoriesType[]>();
  const [profileInfo, setProfileInfo] = useState<{
    id: string;
    name: string;
    img: string | null;
    bio: string;
  } | null>(null);
  const [loading] = useState(false);
  const inputRef = useRef<TextInput>(null);

  function handleThreeDots() {
    navigation.navigate('settings');
  }

  function navigateToContact() {
    navigation.navigate('tab', {screen: 'contact'});
  }

  function viewImageModal(
    id: string,
    name: string,
    img: string | null,
    bio: string,
  ) {
    setProfileInfo({id, name, img, bio});
  }

  function handleUserSearch({search}: FormData) {
    if (!Keyboard.isVisible()) {
      inputRef.current?.focus();
    }

    if (!search || search.trim() === '') {
      setFullChatList(chatLists);
      return;
    }

    const filteredChats = chatLists.filter(chat => {
      const userName = findUserName(chat.participants, user?.id).toLowerCase();
      const lastMessage = chat.messages[0]?.message?.toLowerCase() || '';

      return (
        userName.includes(search.toLowerCase()) ||
        lastMessage.includes(search.toLowerCase())
      );
    });
    setFullChatList(filteredChats);
  }

  const navigateToChat = React.useCallback(
    (
      id: string,
      img: string | null,
      name: string,
      lastSeen?: string,
      remoteUserId?: string,
    ) => {
      setRemoteUserId(remoteUserId);
      setCurrentOpenChat(prevState => ({
        ...prevState,
        id,
        chatType: 'user',
      }));

      navigation.navigate('chats', {
        id,
        img: img,
        name,
        chatType: 'user',
        lastSeen,
      });
      setValue('search', '');
    },
    [navigation, setValue],
  );

  function onPressStartChat() {
    profileInfo &&
      navigateToChat(profileInfo?.id, profileInfo?.img, profileInfo?.name),
      setProfileInfo(null);
  }

  useFocusEffect(
    React.useCallback(() => {
      getAllStory();
      setFullChatList(chatLists);
    }, [chatLists]),
  );

  async function getAllStory() {
    try {
      const res = await getStory();
      const jsonRes = await res.json();
      setStroy(jsonRes.data);
    } catch (error) {
      console.log('eeeeeeee', error);
    }
  }

  useEffect(() => {
    if (!isFcmTokenUpdate) {
      messaging()
        .getToken()
        .then(token => {
          updateFcmToken(token).then(res => {
            if (res.ok) {
              setIsFcmTokenUpate(true);
            }
          });
        });
    }
    return () => setValue('search', '');
  }, [setValue]);

  function onChangeText(search: string) {
    setValue('search', search);
    if (search.trim() === '') {
      setFullChatList(chatLists);
      return;
    }
    const filteredChats = fullChatList.filter(chat =>
      findUserName(chat.participants, user?.id)
        .toLowerCase()
        .includes(search.toLowerCase()),
    );

    setFullChatList(filteredChats);
  }

  function navigateToStatus(item: StoriesType, index: number) {
    navigation.navigate('statusScreen', {
      stories: item,
      allStories: story,
      index,
    });
  }

  function navigateToCreateStatus() {
    navigation.navigate('createStory');
  }

  return (
    <>
      <BodyCard padTop>
        <Header title="N2 Chat" showLogo onPressThreeDots={handleThreeDots} />
        <Input
          ref={inputRef}
          control={control}
          name="search"
          placeholder="Search chat..."
          placeholderTextColor={colors.primaryB100}
          renderToHardwareTextureAndroid
          border={false}
          maxLength={10}
          onChangeText={onChangeText}
          endView={
            <>
              <Pressable
                hitSlop={{top: 12, bottom: 12, right: 16}}
                onPress={handleSubmit(handleUserSearch)}>
                <SearchIcon />
              </Pressable>
            </>
          }
          inputStyle={{backgroundColor: colors.primaryB50}}
        />
        {isCallActive && (
          <View>
            <Spacer height={20} />
            <FloatingCallBar />
          </View>
        )}
        {user?.role && (
          <Status
            story={story}
            userType={user?.role}
            onPressStatus={navigateToStatus}
            onCreateStatus={navigateToCreateStatus}
          />
        )}
        <Spacer height={12} />
        <FlatList
          data={fullChatList}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={reconnect} />
          }
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Text variant="subText1" center color={colors.text.B100}>
                Start a new chat 💬
              </Text>
            </View>
          }
          showsVerticalScrollIndicator={false}
          renderItem={({item}) => {
            return (
              <MassageCard
                loading={loading}
                id={item?.id}
                img={findUserAvatar(item?.participants, user?.id) ?? null}
                name={findUserName(item?.participants, user?.id)}
                lastSeen={convertIntoLocalTime(item?.messages[0]?.createdAt)}
                lastMsg={item?.messages[0]?.message}
                massageCount={item?.unread_count}
                agent={findUserRole(item?.participants, user?.id)}
                massageStatus={
                  item?.messages[0]?.message_read ? 'seen' : 'delivered'
                }
                onPressImage={viewImageModal}
                onPress={navigateToChat}
                remoteUserId={findRemoteUserId(item?.participants, user?.id)}
                bio={findUserBio(item?.participants, user?.id)}
              />
            );
          }}
          keyExtractor={item => item.id.toString()}
          ItemSeparatorComponent={() => <Spacer height={12} />}
        />
        <Fab
          onPress={navigateToContact}
          icon={<Massage fill={colors.icon.white} />}
        />
      </BodyCard>

      <ChatProfileModal
        profileInfo={profileInfo}
        onPressBack={() => {
          setProfileInfo(null);
        }}
        onPressStartChat={onPressStartChat}
      />
    </>
  );
}

const styles = StyleSheet.create({
  listContainer: {
    justifyContent: 'center',
  },
  emptyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: hp(20),
  },
});
