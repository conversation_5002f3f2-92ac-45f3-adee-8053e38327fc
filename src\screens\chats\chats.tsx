import React, {useContext, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  Pressable,
  FlatList,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Keyboard,
  Alert,
  Platform,
} from 'react-native';
import {
  Input,
  Spacer,
  BottomSheet,
  Row,
  Text,
  BodyCard,
  ChatContainer,
  Camera as CameraComp,
} from '../../components';
import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {z} from 'zod';
import {
  Camera,
  Documents,
  Emoji,
  File,
  Gallery,
  Mic,
  Send,
} from '../../assets/icons';
import {colors} from '../../design/colors';
import DocumentPicker, {
  DocumentPickerResponse,
} from 'react-native-document-picker';
import {ChatRow, ChatSchema, MessageRead} from '../../utils';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {RootStack} from '../../utils';
import {useStore} from '../../utils/hooks';
import {
  massageStatus,
  showTostMessage,
  sortGroupsByLastActive,
  sortRecentMessage,
} from '../../utils/helpers';
import {CustomCameraRef} from '../../components/Camera';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {PhotoFile} from 'react-native-vision-camera';
import EmojiSelector, {Categories} from 'react-native-emoji-selector';

import {sendFilAndPhotos, sendCameraPhoto, uploadAudio} from '../../utils/apis';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import RNFS from 'react-native-fs';
import {WebSocketContext} from '../../utils/providers/WebSocket';

type ChatSchemaType = z.infer<typeof ChatSchema>;

type ChatProps = RouteProp<RootStack, 'chats'>;

const keyExtractor = (item: ChatRow, idx: number) => item.id + idx;
const audioRecorderPlayer = new AudioRecorderPlayer();
function Chats() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {socket, messages, setCurrentOpenChat, setMassages} =
    useContext(WebSocketContext);
  const messagesRef = useRef(messages);
  const {control, handleSubmit, setValue, watch, getValues} =
    useForm<ChatSchemaType>({
      resolver: zodResolver(ChatSchema),
    });
  const {
    params: {id, chatType, isSuperAdmin, agentPhone},
  } = useRoute<ChatProps>();

  const {userId, setChatLists, setGroupLists, userRole} = useStore(state => ({
    userId: state.user?.id,
    chatLists: state.chatLists,
    setChatLists: state.setChatLists,
    setGroupLists: state.setGroupLists,
    groupLists: state.groupLists,
    userRole: state.user?.role,
  }));

  const [showModal, setShowModal] = useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const [showEmoji, setShowEmoji] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);

  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState('00:00');
  const [toolKit, setIsToolkit] = useState(false);

  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(
    new Set(),
  );

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleSelectMessage = (id: string) => {
    setSelectedMessages(prev => {
      const newSelected = new Set(prev);

      if (newSelected.has(id)) {
        newSelected.delete(id);
      } else {
        newSelected.add(id);
      }
      return newSelected;
    });
  };

  const flatListRef = useRef<FlatList<ChatRow>>(null);

  const handleSendMassage = ({textMessage}: ChatSchemaType) => {
    if (textMessage.length === 0) return;
    try {
      const message = {
        type: 'message',
        message: textMessage,
        chat_id: id,
      };
      if (socket) {
        socket.send(JSON.stringify(message));
        socket.onmessage = msg => {
          const data = JSON.parse(msg.data);
          if (data.status === 200) {
            setValue('textMessage', '');
            flatListRef.current?.scrollToEnd();
          }
        };
      } else {
        showTostMessage('something went wrong');
      }
    } catch (error) {
      Alert.alert('Error', 'Something Went Wrong');
    }
  };

  const toggleManualModal = React.useCallback(() => {
    setShowModal(showing => !showing);
  }, []);

  const toggleEmoji = React.useCallback(() => {
    if (Keyboard.isVisible()) {
      Keyboard.dismiss();
    }

    setShowEmoji(!showEmoji);
  }, [showEmoji]);

  const onEmojiSelected = React.useCallback((emoji: string) => {
    setValue(
      'textMessage',
      (getValues('textMessage') === undefined ? '' : getValues('textMessage')) +
        emoji,
    );
  }, []);

  async function handleDocument() {
    try {
      const data = await DocumentPicker.pickSingle();
      const response = await sendFilAndPhotos(data);

      if (response.status !== 200 && !response.ok) {
        showTostMessage('try after some time');
      } else {
        const data = await response.json();
        socket?.send(
          JSON.stringify({
            type: 'media_message',
            message: data[0].type,
            media_id: data[0].id,
            chat_id: id,
          }),
        );
      }
    } catch (error) {
      setShowModal(false);
    } finally {
      setShowModal(false);
    }
  }

  async function sendFileFromGallery(data: DocumentPickerResponse[]) {
    const response = await sendFilAndPhotos(data[0]);

    if (response.status !== 200 && !response.ok) {
      showTostMessage('try after some time');
    } else {
      const data = await response.json();
      if (showCamera) {
        setShowCamera(false);
      }

      socket?.send(
        JSON.stringify({
          type: 'media_message',
          message: data[0].type,
          media_id: data[0].id,
          chat_id: id,
        }),
      );
    }
  }

  async function handleGallery() {
    try {
      const data = await DocumentPicker.pick({
        type: [DocumentPicker.types.images],
      });

      if (data[0]?.type === 'image/heic') {
        return showTostMessage(
          'We are currently support jpg, png, jpeg image formate ',
        );
      }
      sendFileFromGallery(data);
    } catch (error) {
      setShowModal(false);
    } finally {
      setShowModal(false);
    }
  }

  async function toggleCamera() {
    setShowCamera(showing => !showing);
    setShowModal(false);
  }

  function fetchOldMessages() {
    socket?.send(
      JSON.stringify({
        type: 'scrolling',
        skip: 20,
        limit: 20,
      }),
    );
  }

  function handleScrollToTop(event: NativeSyntheticEvent<NativeScrollEvent>) {
    const {contentOffset} = event.nativeEvent;
    if (contentOffset.y === 0) {
      fetchOldMessages();
    }
  }

  const handleStartRecording = async () => {
    setIsRecording(true);

    const cacheDir = RNFS.CachesDirectoryPath;
    const path = Platform.select({
      ios: 'audio.m4a',
      android: `${cacheDir}/audio.mp3`,
    });
    const result = await audioRecorderPlayer.startRecorder(path).then();
    console.log(result, 'result');

    if (result) {
      audioRecorderPlayer.addRecordBackListener(e => {
        const currentPosition = e.currentPosition;
        const minutes = Math.floor(currentPosition / 60000);
        const seconds = Math.floor((currentPosition % 60000) / 1000);
        const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
        setRecordingTime(formattedTime);
        return;
      });
    }
  };

  const handleStopRecording = async () => {
    if (!isRecording) {
      console.log('Recording is already stopped');
      return;
    }
    setIsRecording(false);
    setRecordingTime('00:00');
    try {
      const result = await audioRecorderPlayer.stopRecorder();
      const res = await uploadAudio(result);
      const data = await res.json();
      socket?.send(
        JSON.stringify({
          type: 'media_message',
          message: data[0].type,
          media_id: data[0].id,
          chat_id: id,
        }),
      );
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({animated: true});
      }, 300);
    } catch (error) {
      console.error('Error stopping recording:');
      showTostMessage('Something went wrong');
    } finally {
      audioRecorderPlayer.removeRecordBackListener();
    }
  };

  React.useEffect(() => {
    setCurrentOpenChat(prevState => ({
      ...prevState,
      id,
      chatType,
    }));
    setLoading(true);
    if (socket) {
      socket.send(
        JSON.stringify({
          chat_id: id,
          type: 'oldMessage',
        }),
      );
    }

    setTimeout(() => setLoading(false), 2000);

    return () => {
      if (chatType === 'user') {
        setChatLists((prevChatLists: any[]) => {
          const updatedChatLists = prevChatLists?.map(chat => {
            if (chat.id === id) {
              return {
                ...chat,
                unread_count: 0,
              };
            }
            return chat;
          });
          return sortRecentMessage(updatedChatLists);
        });
      } else if (chatType === 'group') {
        setGroupLists((prevGroupLists: any[]) => {
          const updatedGroupLists = prevGroupLists?.map(chat => {
            if (chat.id === id) {
              return {
                ...chat,
                unread_count: 0,
              };
            }
            return chat;
          });
          return sortGroupsByLastActive(updatedGroupLists);
        });
      }
      setMassages([]);
      setCurrentOpenChat(null);
    };
  }, []);

  const handleShowToolkit = () => {
    setIsToolkit(true);
    setTimeout(() => setIsToolkit(false), 2000);
  };

  const cameraRef = useRef<CustomCameraRef>(null);

  const handlePhotoTaken = async (photo: PhotoFile) => {
    try {
      const response = await sendCameraPhoto(photo);
      if (response.status == 200) {
        const data = await response.json();
        socket?.send(
          JSON.stringify({
            type: 'media_message',
            message: data[0].type,
            media_id: data[0].id,
            chat_id: id,
          }),
        );
        setShowCamera(false);
      } else {
        showTostMessage('try after some time');
      }
    } catch (error) {
      setShowModal(false);
    } finally {
      setShowModal(false);
    }
  };

  React.useEffect(() => {
    navigation.setParams({
      selectedMessagesCount: selectedMessages.size,
      showCamera,
    });
  }, [navigation, selectedMessages, showCamera]);

  React.useEffect(() => {
    function onKeyboardDidShow() {
      if (showEmoji) {
        setShowEmoji(!showEmoji);
      }
    }
    const showSubscription = Keyboard.addListener(
      'keyboardDidShow',
      onKeyboardDidShow,
    );
    return () => {
      showSubscription.remove();
    };
  }, [showEmoji]);

  React.useEffect(() => {
    messagesRef.current = messages;
    const lastmsg = messages[messages.length - 1];
    if (lastmsg) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({animated: true});
      }, 100);
      if (lastmsg.sender_id !== userId && lastmsg.chat_id === id) {
        const finduser =
          lastmsg.message_read && lastmsg.message_read.length > 0
            ? lastmsg.message_read.filter(
                (user: MessageRead) => user.user_id === userId,
              )
            : false;

        if (!finduser) {
          socket?.send(
            JSON.stringify({
              type: 'message_seen',
              chat_id: lastmsg?.chat_id,
              messageId: lastmsg?.id,
            }),
          );
        }
      }
    }
  }, [messages]);

  return (
    <>
      {showCamera && (
        <CameraComp
          ref={cameraRef}
          onPhotoTaken={handlePhotoTaken}
          toggleCamera={toggleCamera}
          sendFileFromGallery={sendFileFromGallery}
          cameraMode="chat"
        />
      )}

      <BodyCard>
        <>
          <View style={styles.chatContainer}>
            <FlatList
              onLayout={() => {
                setTimeout(() => {
                  flatListRef.current?.scrollToEnd();
                }, 500);
              }}
              onScroll={handleScrollToTop}
              ref={flatListRef}
              data={messages}
              showsVerticalScrollIndicator={false}
              keyExtractor={keyExtractor}
              ItemSeparatorComponent={() => <Spacer height={8} />}
              renderItem={({item}) => {
                const isSender =
                  userRole === 'user' ||
                  userRole === 'agent' ||
                  userRole === 'admin'
                    ? item?.sender_id?.toString() === userId?.toString()
                    : item?.sender.phone === agentPhone;

                const isSelected = selectedMessages.has(item.id);
                return (
                  <Pressable
                    // onLongPress={() => handleSelectMessage(item.id)}
                    style={[isSelected && {backgroundColor: 'lightgrey'}]}>
                    <ChatContainer
                      loading={loading}
                      chatRow={item}
                      massageStatus={massageStatus(
                        userId,
                        item?.sender,
                        item?.message_read,
                      )}
                      massageType={isSender ? 'sender' : 'receiver'}
                      chatType={chatType}
                    />
                  </Pressable>
                );
              }}
            />
          </View>
        </>
      </BodyCard>
      {!isSuperAdmin && (
        <>
          {toolKit && (
            <View
              style={{
                alignItems: 'flex-end',
                padding: 4,
              }}>
              <Text variant="subText2_500">Press and hold to record</Text>
            </View>
          )}
          <Row center style={styles.row}>
            {!isRecording ? (
              <Input
                style={{flex: 1}}
                control={control}
                name="textMessage"
                multiline
                placeholder="Write a message"
                startView={
                  <>
                    <Pressable onPress={toggleEmoji}>
                      <Emoji height={24} width={24} />
                    </Pressable>
                  </>
                }
                endView={
                  <Pressable onPress={toggleManualModal}>
                    <File
                      height={24}
                      width={24}
                      fill={showModal ? colors.icon.B200 : colors.icon.B50}
                      style={{
                        transform: [
                          {
                            rotate: showModal ? '-90deg' : '0deg',
                          },
                        ],
                      }}
                    />
                  </Pressable>
                }
              />
            ) : (
              <Input
                style={{flex: 1}}
                control={control}
                name="textMessage"
                startView={
                  <Text variant="subText1" color={colors.text.B20}>
                    {recordingTime}
                  </Text>
                }
                // endView={
                //   <Row center>
                //     <Chevron
                //       height={24}
                //       width={24}
                //       fill={colors.icon.B20}
                //       style={{
                //         transform: [{rotate: '180deg'}],
                //       }}
                //     />
                //     <Text variant="subText1" color={colors.text.B20}>
                //       Slide to cancel
                //     </Text>
                //   </Row>
                // }
              />
            )}

            {watch('textMessage') ? (
              <Pressable
                style={isRecording ? styles.recorder : styles.send}
                onPress={handleSubmit(handleSendMassage)}>
                <Send />
              </Pressable>
            ) : (
              <View>
                <Pressable
                  style={!isRecording ? styles.send : styles.recorder}
                  onLongPress={handleStartRecording}
                  onPressOut={handleStopRecording}
                  onPress={handleShowToolkit}>
                  <Mic />
                </Pressable>
              </View>
            )}
          </Row>
        </>
      )}

      {showEmoji && (
        <>
          <EmojiSelector
            category={Categories.all}
            onEmojiSelected={onEmojiSelected}
            showSearchBar={false}
            columns={9}
          />
        </>
      )}
      <BottomSheet visible={showModal} onClose={toggleManualModal}>
        <View style={styles.bottomSheet}>
          <Row between>
            <View>
              <Pressable style={styles.attachments} onPress={handleDocument}>
                <Documents fill={colors.icon.B100} />
              </Pressable>
              <Spacer height={8} />
              <Text center variant="subText2_500">
                Documents
              </Text>
            </View>
            <View>
              <Pressable style={styles.attachments} onPress={handleGallery}>
                <Gallery />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Gallery
              </Text>
            </View>
            <View>
              <Pressable style={styles.attachments} onPress={toggleCamera}>
                <Camera height={24} width={24} fill={colors.icon.B100} />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Camera
              </Text>
            </View>
          </Row>
        </View>
      </BottomSheet>
    </>
  );
}

const styles = StyleSheet.create({
  chatContainer: {
    paddingTop: 8,
    // marginBottom: 16,
  },
  attachments: {
    padding: 20,
    backgroundColor: colors.primaryB50,
    borderRadius: 100,
  },
  send: {
    backgroundColor: colors.primaryB200,
    borderRadius: 100,
    height: 48,
    width: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recorder: {
    backgroundColor: colors.error,
    borderRadius: 100,
    height: 48,
    width: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomSheet: {
    height: 120,
    width: '100%',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    shadowColor: 'black',
    elevation: 3,
    shadowRadius: 10,
    shadowOpacity: 1,
    backgroundColor: colors.white,
    paddingTop: 12,
  },
  row: {
    backgroundColor: colors.white,
    elevation: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowOffset: {
      width: 4,
      height: 1,
    },
    shadowOpacity: 0.5,
    columnGap: 8,
  },
});

export default React.memo(Chats);
