import React, {useRef, useState} from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Fab,
  Row,
  Camera as CameraComp,
  BottomSheet,
  Spacer,
  Text,
  Input,
} from '../../components';
import {
  CameraFab,
  Cross,
  Paint,
  // TextIcon,
  Emoji,
  Gallery,
  Camera,
  RightFab,
} from '../../assets/icons';
import {
  Image,
  Keyboard,
  Pressable,
  StyleSheet,
  ToastAndroid,
  View,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {StoryTextSchema, RootStack} from '../../utils';
import DocumentPicker, {
  DocumentPickerResponse,
  types,
} from 'react-native-document-picker';
import {CustomCameraRef} from '../../components/Camera';
import {colors} from '../../design/colors';
import {zodResolver} from '@hookform/resolvers/zod';
import {z} from 'zod';
import {useForm} from 'react-hook-form';
import {createStoryWithCaption, createStoryWithGallery} from '../../utils/apis';
import EmojiSelector, {Categories} from 'react-native-emoji-selector';
import {showTostMessage} from '../../utils/helpers';
import {
  heightPercentageToDP,
  widthPercentageToDP,
} from 'react-native-responsive-screen';

const backgroundColor = [
  '#96C0FF',
  '#FFE793',
  '#C3C9FF',
  '#FCC',
  '#CC9AFF',
  '#9AFFB6',
];
type FormData = z.infer<typeof StoryTextSchema>;

export default function CreateStory() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const [showCamera, setShowCamera] = useState(false);
  const [showModal, setShowModal] = React.useState(false);
  const cameraRef = useRef<CustomCameraRef>(null);
  const [idx, setIdx] = React.useState(0);
  const [showEmoji, setShowEmoji] = useState(false);
  const [galleryFile, setGalleryFile] = useState<
    DocumentPickerResponse[] | null
  >(null);

  const {control, handleSubmit, setValue, getValues} = useForm<FormData>({
    resolver: zodResolver(StoryTextSchema),
  });

  const toggleManualModal = React.useCallback(() => {
    setShowModal(showing => !showing);
  }, [showModal]);

  const toggleEmoji = React.useCallback(() => {
    if (Keyboard.isVisible()) {
      Keyboard.dismiss();
    } else if (showEmoji) {
      setShowEmoji(!showEmoji);
    }
    setShowEmoji(!showEmoji);
  }, [showEmoji]);

  const onEmojiSelected = React.useCallback((emoji: string) => {
    setValue(
      'caption',
      (getValues('caption') === undefined ? '' : getValues('caption')) + emoji,
    );
  }, []);

  async function handleGallery() {
    try {
      const result = await DocumentPicker.pick({
        type: [types.images],
      });

      setGalleryFile(result);
    } catch (error) {
      if (!DocumentPicker.isCancel(error)) {
        showTostMessage('Failed to pick image');
      }
    } finally {
      setShowModal(false);
    }
  }

  async function handelUpdateStory() {
    try {
      if (galleryFile) {
        const response = await createStoryWithGallery(galleryFile[0]);

        if (response.ok && response.status == 200) {
          showTostMessage('Story created');
          navigation.navigate('tab', {screen: 'chatList'});
        } else {
          showTostMessage('Something Went Wrong');
        }
      }
    } catch (error) {
      showTostMessage('Something Went Wrong');
    }
  }

  function navigateToBack() {
    navigation.goBack();
  }

  const handleChangeBackgroundColor = React.useCallback(() => {
    if (backgroundColor.length - 1 === idx) {
      setIdx(0);
    } else {
      setIdx(idx + 1);
    }
  }, [idx]);

  const handlePhotoTaken = async () => {
    toggleCamera();
  };

  async function toggleCamera() {
    setShowCamera(showing => !showing);
    setShowModal(false);
  }

  async function storyCreteByCaption({caption}: FormData) {
    try {
      const response = await createStoryWithCaption(
        caption,
        backgroundColor[idx],
      );

      if (response.ok && response.status == 200) {
        ToastAndroid.showWithGravityAndOffset(
          'Story Created',
          ToastAndroid.TOP,
          ToastAndroid.TOP,
          25,
          50,
        );
        navigation.goBack();
      } else {
        ToastAndroid.showWithGravityAndOffset(
          'Something Went Wrong',
          ToastAndroid.TOP,
          ToastAndroid.TOP,
          25,
          50,
        );
      }
    } catch (error) {
      ToastAndroid.showWithGravityAndOffset(
        'Something Went Wrong',
        ToastAndroid.TOP,
        ToastAndroid.TOP,
        25,
        50,
      );
    }
  }

  return (
    <>
      {showCamera && (
        <CameraComp
          ref={cameraRef}
          onPhotoTaken={handlePhotoTaken}
          toggleCamera={toggleCamera}
          cameraMode="story"
        />
      )}
      {galleryFile ? (
        <>
          <View style={{width: '100%', height: '100%'}}>
            <Image
              resizeMode="cover"
              style={{
                width: widthPercentageToDP(100),
                height: heightPercentageToDP(100),
              }}
              source={{uri: galleryFile[0].uri}}
            />
          </View>
          <Pressable
            style={{
              position: 'absolute',
              top: 36,
              left: 20,
            }}>
            <Cross onPress={() => setGalleryFile(null)} />
          </Pressable>
          <Pressable>
            <Fab icon={<RightFab />} onPress={handelUpdateStory} />
          </Pressable>
        </>
      ) : (
        <BodyCard
          padTop
          style={{backgroundColor: backgroundColor[idx]}}
          padBottom>
          <Row spread>
            <Cross onPress={navigateToBack} />
            <Row style={{gap: 8}}>
              {/* <Pressable>
            <TextIcon />
          </Pressable> */}
              <Pressable onPress={toggleEmoji}>
                <Emoji height={36} width={36} />
              </Pressable>
              <Pressable onPress={handleChangeBackgroundColor}>
                <Paint height={36} width={36} />
              </Pressable>
            </Row>
          </Row>

          <View style={{flex: 1, justifyContent: 'center'}}>
            <Input
              textAlign="center"
              control={control}
              multiline
              placeholder="Type something to share"
              placeholderTextColor={colors.text.white}
              name="caption"
              border={false}
              onFocus={() => setShowEmoji(false)}
            />
          </View>
          {!showCamera && (
            <Fab
              icon={<CameraFab />}
              onPress={toggleManualModal}
              style={{
                position: 'absolute',
                bottom: 64,
                left: 32,
                backgroundColor: colors.primaryB200,
                padding: 14,
                borderRadius: 20,
              }}
            />
          )}
          {!showCamera && (
            <Pressable>
              <Fab
                icon={<RightFab />}
                onPress={handleSubmit(storyCreteByCaption)}
              />
            </Pressable>
          )}
        </BodyCard>
      )}

      {showEmoji && (
        <>
          <EmojiSelector
            category={Categories.all}
            onEmojiSelected={onEmojiSelected}
            showSearchBar={false}
            columns={9}
          />
        </>
      )}
      <BottomSheet visible={showModal} onClose={toggleManualModal}>
        <View style={styles.bottomSheet}>
          <Spacer height={4} />
          <Row between>
            <View>
              <Pressable style={styles.attachments} onPress={handleGallery}>
                <Gallery />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Gallery
              </Text>
            </View>
            <View>
              <Pressable style={styles.attachments} onPress={toggleCamera}>
                <Camera height={24} width={24} fill={colors.icon.B100} />
              </Pressable>
              <Spacer height={8} />

              <Text center variant="subText2_500">
                Camera
              </Text>
            </View>
          </Row>
        </View>
      </BottomSheet>
    </>
  );
}
const styles = StyleSheet.create({
  bottomSheet: {
    height: 100,
    width: '100%',
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
    shadowColor: 'black',
    elevation: 3,
    shadowRadius: 10,
    shadowOpacity: 1,
    backgroundColor: colors.white,
  },
  attachments: {
    padding: 20,
    backgroundColor: colors.primaryB50,
    borderRadius: 100,
  },
});
