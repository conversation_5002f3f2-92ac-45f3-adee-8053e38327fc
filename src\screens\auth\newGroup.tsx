import {<PERSON><PERSON>, FlatList} from 'react-native';
import React, {useCallback, useState} from 'react';
import {
  BackBtn,
  BodyCard,
  Fab,
  Header,
  Input,
  MassageCard,
  Spacer,
} from '../../components';
import {CheckIcon, SearchIcon, RightFab, CheckBox} from '../../assets/icons';
import {colors} from '../../design/colors';
import {useForm} from 'react-hook-form';
import {useFocusEffect, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack, ContactList} from '../../utils';
import {getContactList, getUserType} from '../../utils/apis';
import {showTostMessage} from '../../utils/helpers';

export default function NewGroups() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const [selectedPhoneNumbers, setSelectedPhoneNumbers] = useState<string[]>(
    [],
  );
  const [contactList, setContactList] = useState<ContactList[]>([]);
  const [fullContactList, setFullContactList] = useState<ContactList[]>([]);

  const {control} = useForm<FormData>({});

  const handleFabPress = () => {
    const selectedContacts = contactList.filter(contact =>
      selectedPhoneNumbers.includes(contact.phone),
    );

    if (selectedContacts.length > 0) {
      navigation.navigate('createGroup', {
        selectContact: selectedContacts,
      });
    } else {
      showTostMessage('Please Select any Contact');
    }
  };

  const handlePress = async (phone: string) => {
    try {
      const response = await getUserType(phone);
      const {userExisted} = await response.json();

      if (userExisted) {
        setSelectedPhoneNumbers(prevSelected =>
          prevSelected.includes(phone)
            ? prevSelected.filter(p => p !== phone)
            : [...prevSelected, phone],
        );
      } else {
        showTostMessage('not an n2chat user');
      }
    } catch (error) {
      console.error('Error checking user type:', error);
      showTostMessage('Error checking user status');
    }
  };

  const renderItem = ({item}: {item: ContactList}) => (
    <MassageCard
      bio="bio"
      loading={false}
      id={item?.contact_id}
      img={null}
      name={item?.name}
      phone={item.phone}
      username={item.username}
      massageStatus="delivered"
      massageCount={0}
      onPress={() => handlePress(item.phone)}
      endView={
        selectedPhoneNumbers.includes(item.phone) ? <CheckBox /> : <CheckIcon />
      }
    />
  );

  useFocusEffect(
    useCallback(() => {
      getContactList()
        .then(res => {
          if (res.status === 500) {
            Alert.alert('Error', 'Something went wrong, try again later!');
            return null;
          }
          return res.json();
        })
        .then(response => {
          if (response) {
            setContactList(response);
            setFullContactList(response);
          }
        })
        .catch(error => {
          console.error('Error fetching contact list:', error);
          Alert.alert('Error', 'Failed to fetch contacts');
        });
    }, []),
  );

  function onChangeText(search: string) {
    const filteredContacts = fullContactList.filter(
      contact =>
        contact.name.toLowerCase().includes(search.toLowerCase()) ||
        contact.phone.toLowerCase().includes(search.toLowerCase()),
    );
    setContactList(filteredContacts);
  }

  return (
    <BodyCard padTop>
      <BackBtn />
      <Header title="Add members" />
      <Input
        control={control}
        name="Search contacts"
        placeholder="Search"
        placeholderTextColor={colors.primaryB100}
        endView={<SearchIcon />}
        inputStyle={{backgroundColor: colors.primaryB50}}
        onChangeText={onChangeText}
      />
      <FlatList
        data={contactList}
        showsVerticalScrollIndicator={false}
        renderItem={renderItem}
        keyExtractor={item =>
          item.contact_id?.toString() || Math.random().toString()
        }
        ItemSeparatorComponent={() => <Spacer height={4} />}
      />
      <Fab icon={<RightFab />} onPress={handleFabPress} />
    </BodyCard>
  );
}
