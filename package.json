{"name": "n2chat", "version": "0.0.1", "private": true, "author": "<PERSON><PERSON><PERSON>", "scripts": {"adb": "adb connect **************:${PORT}", "android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint 'src/**/*.{js,jsx,ts,tsx}'", "lint:fix": "eslint . --fix", "start": "react-native start", "clean:start": "react-native start --reset-cache", "test": "jest", "clean": "cd android && ./gradlew clean && cd ..", "release": "cd android && ./gradlew clean && ./gradlew assembleRelease && cd ..", "prepare": "husky", "prettier": "prettier -c .", "prettier:fix": "prettier -w ."}, "dependencies": {"@hookform/resolvers": "^3.6.0", "@notifee/react-native": "^9.1.8", "@react-native-community/blur": "^4.4.0", "@react-native-community/netinfo": "6.0.6", "@react-native-firebase/app": "^20.3.0", "@react-native-firebase/messaging": "^20.3.0", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.9.26", "jwt-decode": "^4.0.0", "lottie-react-native": "^6.7.2", "moti": "^0.29.0", "react": "18.2.0", "react-hook-form": "^7.51.5", "react-native": "0.74.1", "react-native-audio-recorder-player": "^3.6.11", "react-native-biometrics": "^3.0.1", "react-native-bootsplash": "^5.5.3", "react-native-compressor": "^1.8.25", "react-native-document-picker": "^9.3.0", "react-native-dotenv": "^3.4.11", "react-native-emoji-selector": "^0.2.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.16.2", "react-native-incall-manager": "^4.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-mmkv": "^2.12.2", "react-native-permissions": "^4.1.5", "react-native-reanimated": "^3.11.0", "react-native-reanimated-carousel": "^3.5.1", "react-native-responsive-screen": "^1.4.2", "react-native-safe-area-context": "^4.10.1", "react-native-screens": "^3.31.1", "react-native-sound": "^0.11.2", "react-native-svg": "^15.3.0", "react-native-video": "^6.4.3", "react-native-vision-camera": "^4.5.0", "react-native-web": "^0.19.12", "react-native-webrtc": "^124.0.4", "uri-scheme": "^1.3.1", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@eslint/compat": "^1.1.0", "@eslint/js": "^9.5.0", "@react-native-community/eslint-config": "^3.2.0", "@react-native/babel-preset": "0.74.83", "@react-native/eslint-config": "0.74.83", "@react-native/metro-config": "0.74.83", "@react-native/typescript-config": "0.74.83", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "9.x", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-native": "^4.1.0", "globals": "^15.6.0", "husky": "^9.1.7", "jest": "^29.6.3", "prettier": "2.8.8", "react-native-svg-transformer": "^1.4.0", "react-test-renderer": "18.2.0", "typescript": "5.0.4", "typescript-eslint": "^7.13.1"}, "engines": {"node": ">=18"}, "packageManager": "yarn@3.6.4"}