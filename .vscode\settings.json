{"workbench.colorCustomizations": {"titleBar.activeBackground": "#96C0FF", "activityBar.background": "#96C0FF", "titleBar.activeForeground": "#0047B3", "statusBar.background": "#96C0FF", "statusBar.foreground": "#0047B3", "activityBar.inactiveForeground": "#0047B3", "activityBar.activeBackground": "#6BA6FF", "activityBarBadge.background": "#10C600", "titleBar.inactiveBackground": "#96C0FF", "terminal.foreground": "#96C0FF"}, "git.ignoreLimitWarning": true, "typescript.tsdk": "node_modules/typescript/lib", "eslint.useFlatConfig": true, "java.compile.nullAnalysis.mode": "automatic"}