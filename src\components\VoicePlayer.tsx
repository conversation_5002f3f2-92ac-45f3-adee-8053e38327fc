import React, {useEffect, useState, useRef} from 'react';
import Row from './Row';
import Image from './Image';
import {Pause, Play} from '../assets/icons';
import {colors} from '../design/colors';
import {Pressable, StyleSheet, View} from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import Text from './Text';
import {Animated} from 'react-native';

// Global reference to track currently playing audio
let currentlyPlayingAudio: {stop: () => void} | null = null;

interface Props {
  url: string;
}

const audioRecorderPlayer = new AudioRecorderPlayer();

function VoicePlayer({url}: Props) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingTime, setRecordingTime] = useState('00:00');
  const [animatedValues, setAnimatedValues] = useState<Animated.Value[]>([]);
  const animationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Generate initial waveform data with smaller default height
    const initialWaveform = Array.from({length: 40}, () => Math.random() * 15);

    // Initialize animated values for each bar
    const initialAnimatedValues = initialWaveform.map(
      value => new Animated.Value(value),
    );
    setAnimatedValues(initialAnimatedValues);

    // Cleanup on unmount
    return () => {
      stopWaveformAnimation();
      onStopPlay();
    };
  }, []);

  const startWaveformAnimation = () => {
    // Clear any existing animation interval
    stopWaveformAnimation();

    // Start new animation interval
    animationIntervalRef.current = setInterval(() => {
      animatedValues.forEach(animatedValue => {
        Animated.timing(animatedValue, {
          toValue: Math.random() * 30,
          duration: 400,
          useNativeDriver: false,
        }).start();
      });
    }, 500);
  };

  const stopWaveformAnimation = () => {
    if (animationIntervalRef.current) {
      clearInterval(animationIntervalRef.current);
      animationIntervalRef.current = null;

      // Reset bars to smaller height
      animatedValues.forEach(animatedValue => {
        Animated.timing(animatedValue, {
          toValue: Math.random() * 15,
          duration: 300,
          useNativeDriver: false,
        }).start();
      });
    }
  };

  async function togglePlay() {
    if (isPlaying) {
      await onStopPlay();
      return;
    }

    // Stop any currently playing audio before starting a new one
    if (currentlyPlayingAudio) {
      currentlyPlayingAudio.stop();
    }

    try {
      await audioRecorderPlayer.startPlayer(url);
      setIsPlaying(true);

      // Set this instance as the currently playing audio
      currentlyPlayingAudio = {
        stop: async () => {
          await onStopPlay();
        },
      };

      // Start waveform animation
      startWaveformAnimation();

      // Add playback listener with proper completion check
      audioRecorderPlayer.addPlayBackListener(e => {
        // Check if the audio has finished playing
        if (e.currentPosition >= e.duration - 50) {
          onStopPlay();
          return;
        }

        const minutes = Math.floor(e.currentPosition / 60000);
        const seconds = Math.floor((e.currentPosition % 60000) / 1000);
        const formattedTime = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
        setRecordingTime(formattedTime);
      });
    } catch (error) {
      console.error('Error playing audio:', error);
      setIsPlaying(false);
      stopWaveformAnimation();
    }
  }

  const onStopPlay = async () => {
    try {
      await audioRecorderPlayer.stopPlayer();
      audioRecorderPlayer.removePlayBackListener();
      setIsPlaying(false);
      setRecordingTime('00:00');
      stopWaveformAnimation();
    } catch (error) {
      console.error('Error stopping audio:', error);
    }
  };

  return (
    <>
      <Row
        center
        style={{
          backgroundColor: 'rgba(150, 192, 255, 0.4)',
          borderRadius: 8,
          paddingVertical: 6,
          paddingHorizontal: 4,
        }}>
        <Image alt={''} uri={null} />
        <Pressable onPress={togglePlay}>
          {isPlaying ? (
            <Pause height={40} width={40} fill={colors.icon.B200} />
          ) : (
            <Play height={48} width={48} fill={colors.icon.B200} />
          )}
        </Pressable>

        <View style={styles.waveformContainer}>
          {animatedValues.map((animatedValue, index) => (
            <Animated.View
              key={index}
              style={[
                styles.waveformBar,
                {
                  height: animatedValue,
                },
              ]}
            />
          ))}
        </View>
      </Row>
      <Text color={colors.text.B100}>{recordingTime}</Text>
    </>
  );
}

const styles = StyleSheet.create({
  waveformContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  waveformBar: {
    width: 1,
    marginHorizontal: 1,
    backgroundColor: colors.primaryB200,
  },
});

export default React.memo(VoicePlayer);
