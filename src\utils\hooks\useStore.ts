import {MMKV} from 'react-native-mmkv';
import {create} from 'zustand';
import {createJSONStorage, persist, StateStorage} from 'zustand/middleware';
import {
  AppCacheType,
  ChatList,
  ContactList,
  GroupList,
  UserType,
} from '../types';

export const storage = new MMKV();

const zustandStorage: StateStorage = {
  setItem: (name, value) => {
    storage.set(name, value);
  },
  getItem: name => {
    const value = storage.getString(name);
    return Promise.resolve(value ?? null);
  },
  removeItem: name => {
    storage.delete(name);
    // return Promise.resolve();
  },
};

interface StoreType extends AppCacheType {
  setAuthToken: (authToken?: string) => void;
  setUser: (user: Partial<UserType>) => void;
  cleanLocalCache: () => void;
  setInitialRouteName: (
    initialRouteName?: AppCacheType['initialRouteName'],
  ) => void;
  setChatLists: (chatList: ChatList[]) => void;
  setGroupLists: (chatList: GroupList[]) => void;
  setContactLists: (contactLists: ContactList[]) => void;
  setIsFcmTokenUpdate: (isFcmTokenUpdate: boolean) => void;
}

export const useStore = create<StoreType, [['zustand/persist', StoreType]]>(
  persist(
    set => ({
      initialRouteName: 'welcome',
      authToken: undefined,
      user: undefined,
      isFcmTokenUpdate: false,
      chatLists: [],
      groupLists: [],
      contactLists: [],
      setAuthToken: authToken => set({authToken}),
      setUser: user => set({user}),
      setInitialRouteName: initialRouteName => set({initialRouteName}),
      setChatLists: chatLists => set({chatLists}),
      setGroupLists: groupLists => set({groupLists}),
      setContactLists: contactLists => set({contactLists}),
      setIsFcmTokenUpdate: isFcmTokenUpdate => set({isFcmTokenUpdate}),
      //?INFO:-  clean localCache
      cleanLocalCache: () => {
        set({
          authToken: undefined,
          user: undefined,
          initialRouteName: undefined,
          isFcmTokenUpdate: false,
          chatLists: [],
          groupLists: [],
          contactLists: [],
        });
      },
    }),
    {
      name: 'localCacheN2Chat', // Unique name for the storage
      storage: createJSONStorage(() => zustandStorage),
    },
  ),
);
