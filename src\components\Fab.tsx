import {
  StyleSheet,
  TouchableOpacityProps,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import {colors} from '../design/colors';

interface Props extends TouchableOpacityProps {
  icon: React.ReactNode;
}

export default function Fab({icon, ...rest}: Props) {
  return (
    <TouchableOpacity
      style={styles.addContactButton}
      activeOpacity={0.9}
      {...rest}>
      {icon}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  addContactButton: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    backgroundColor: colors.primaryB200,
    padding: 14,
    borderRadius: 20,
  },
});
