import React, {useState, useCallback, useRef} from 'react';
import {TouchableOpacity, Animated, StyleSheet} from 'react-native';
import {colors} from '../design/colors';

interface Props {
  thumbSize: number;
  disabled: false;
}

export default function Toggle({thumbSize = 20, disabled = false}: Props) {
  const [isEnabled, setIsEnabled] = useState(false);
  const thumbPosition = useRef(new Animated.Value(0)).current;

  const toggleSwitch = useCallback(() => {
    if (!disabled) {
      setIsEnabled(previousState => !previousState);
      Animated.timing(thumbPosition, {
        toValue: isEnabled ? 0 : 1,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  }, [isEnabled, thumbPosition, disabled]);

  const thumbStyle = {
    width: thumbSize,
    height: thumbSize,
    borderRadius: thumbSize / 2,
    backgroundColor: 'white',
    position: 'absolute' as const,
    transform: [
      {
        translateX: thumbPosition.interpolate({
          inputRange: [0, 1],
          outputRange: [2, 28],
        }),
      },
    ],
  };

  const trackStyle = {
    backgroundColor: disabled
      ? '#D9D9D9'
      : isEnabled
      ? colors.primaryB300
      : '#D9D9D9',
  };

  return (
    <TouchableOpacity
      style={[styles.track, trackStyle]}
      onPress={toggleSwitch}
      activeOpacity={disabled ? 1 : 0.7}
      disabled={disabled}>
      <Animated.View style={thumbStyle} />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  track: {
    width: 50,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    padding: 2,
  },
});
