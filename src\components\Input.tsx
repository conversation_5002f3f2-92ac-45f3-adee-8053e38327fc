import * as React from 'react';
import {
  TextInput,
  StyleSheet,
  TextInputProps,
  StyleProp,
  TextStyle,
  ViewStyle,
  ColorValue,
} from 'react-native';
import {Control, Controller} from 'react-hook-form';
import {colors} from '../design/colors';
import InputField from './inputField';
import {getFontSize} from '../utils';

interface Props extends TextInputProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  control: Control<any>;
  name: string;
  rules?: object;
  startView?: React.ReactNode;
  endView?: React.ReactNode;
  startViewStyle?: StyleProp<TextStyle>;
  endViewStyle?: StyleProp<TextStyle>;
  label?: string;
  secureTextEntry?: boolean;
  inputStyle?: StyleProp<ViewStyle>;
  style?: StyleProp<ViewStyle>;
  border?: boolean;
  placeholderTextColor?: ColorValue;
  labelIcon?: React.ReactNode;
}

// Use React.forwardRef to pass ref down to TextInput
const Input = React.forwardRef<TextInput, Props>(
  (
    {
      control,
      name,
      rules = {},
      placeholder,
      secureTextEntry,
      startView,
      endView,
      startViewStyle,
      endViewStyle,
      style,
      label,
      border = true,
      placeholderTextColor,
      inputStyle,
      labelIcon,
      ...textInputProps
    },
    ref,
  ) => {
    return (
      <>
        <Controller
          control={control}
          name={name}
          rules={rules}
          render={({field: {onChange, onBlur, value}, fieldState: {error}}) => (
            <>
              <InputField
                startView={startView}
                endView={endView}
                startViewStyle={startViewStyle}
                endViewStyle={endViewStyle}
                label={label}
                labelIcon={labelIcon}
                style={style}
                error={error}
                border={border}
                inputStyle={inputStyle}>
                <TextInput
                  ref={ref}
                  style={[styles.input]}
                  placeholder={placeholder}
                  placeholderTextColor={
                    placeholderTextColor
                      ? placeholderTextColor
                      : colors.text.B20
                  }
                  secureTextEntry={secureTextEntry}
                  onBlur={onBlur}
                  onChangeText={(text: string) => onChange(text)}
                  value={value}
                  {...textInputProps}
                />
              </InputField>
            </>
          )}
        />
      </>
    );
  },
);

Input.displayName = 'Input';

const styles = StyleSheet.create({
  input: {
    color: colors.text.B50,
    fontSize: getFontSize(16),
    paddingHorizontal: 16,
    textAlignVertical: 'center',
    flex: 1,
    borderRadius: 20,
    fontFamily: 'GoogleSans-Regular',
  },
});

export default Input;
