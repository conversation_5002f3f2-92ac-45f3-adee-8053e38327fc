import * as React from 'react';
import {StyleProp, StyleSheet, View, ViewStyle} from 'react-native';
import {widthPercentageToDP as wp} from 'react-native-responsive-screen';

interface Props {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  padTop?: boolean;
  padBottom?: boolean;
}

export default function BodyCard({
  children,
  style,
  padTop,
  padBottom,
  ...rest
}: Props) {
  return (
    <View
      style={[
        styles.base,
        padTop ? styles.padTop : {},
        padBottom ? styles.paddingBottom : {},
        style,
      ]}
      {...rest}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  base: {
    flex: 1,
    paddingHorizontal: wp(5),
    backgroundColor: '#ffffff',
  },
  padTop: {
    paddingTop: wp(9),
  },

  paddingBottom: {
    paddingBottom: wp(12),
  },
});
