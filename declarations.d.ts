import 'react-native-webrtc';

declare module '*.svg' {
  import React from 'react';
  import {SvgProps} from 'react-native-svg';
  const content: React.FC<SvgProps>;
  export default content;
}

declare module '@env' {
  export const API_URL_DEBUG: string;
  export const WEB_SOCKET_URL_DEBUG: string;
  export const MEDIA_BASE_URL_DEBUG: string;
  export const API_URL_RELEASE: string;
  export const WEB_SOCKET_URL_RELEASE: string;
  export const MEDIA_BASE_URL_RELEASE: string;
}

declare module '*.png' {
  const value: string;
  export default value;
}

declare module '*.gif';

declare module 'react-native-webrtc' {
  interface RTCPeerConnection {
    addEventListener(
      type: string,
      listener: EventListenerOrEventListenerObject,
      options?: boolean | AddEventListenerOptions,
    ): void;
  }
}
