import React, {
  createContext,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import {globalSocket} from '../apis';
import {useStore} from '../hooks';
import {
  showTostMessage,
  sortGroupsByLastActive,
  sortMessages,
  sortRecentMessage,
} from '../helpers';
import {<PERSON><PERSON><PERSON><PERSON>, Chat<PERSON>ow, GroupList, MessageRead, RootStack} from '../types';
import WebRTCProvider from './WebRtcProvider';
import {registerGlobals} from 'react-native-webrtc';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Alert} from '../../components';
import notifee, {EventType} from '@notifee/react-native';
import {AppState, AppStateStatus} from 'react-native';
import messaging from '@react-native-firebase/messaging';
registerGlobals();

type ChatType = {
  id: string;
  chatType: string;
};

type WebSocketContextType = {
  socket: WebSocket | null;
  isConnected: boolean;
  currentOpenChat: ChatType | null;
  messages: ChatRow[];
  setCurrentOpenChat: React.Dispatch<React.SetStateAction<ChatType | null>>;
  setMassages: React.Dispatch<React.SetStateAction<ChatRow[]>>;
  setRemoteUserId: React.Dispatch<React.SetStateAction<string | undefined>>;
  reconnect: () => void;
  remoteUserId: string | undefined;
  appState: AppStateStatus;
};

interface Props {
  children: React.ReactNode;
}

export const WebSocketContext = createContext<WebSocketContextType>({
  socket: null,
  isConnected: false,
  messages: [],
  setCurrentOpenChat: () => {},
  setMassages: () => {},
  setRemoteUserId: () => {},
  reconnect: () => {},
  currentOpenChat: null,
  remoteUserId: undefined,
  appState: 'unknown',
});

export default function WebSocketProvider({children}: Props) {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [showAlert, setShowAlert] = useState(false);

  const [firstTimeLoad, setFirstTimeLoad] = useState(true);
  const [messages, setMassages] = useState<ChatRow[]>([]);
  const [currentOpenChat, setCurrentOpenChat] = useState<ChatType | null>(null);
  const [remoteUserId, setRemoteUserId] = useState<string | undefined>(
    undefined,
  );
  // const [appState, setAppState] = useState<AppStateStatus>('unknown');
  const [appState, setAppState] = useState(AppState.currentState);
  const appStateRef = useRef(AppState.currentState);
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {setChatLists, setGroupLists, cleanLocalCache} = useStore(state => ({
    setChatLists: state.setChatLists,
    setGroupLists: state.setGroupLists,
    cleanLocalCache: state.cleanLocalCache,
  }));

  function handleNewConnection(response: {data: {rows: ChatRow[]}}) {
    try {
      const sortedMessages = sortMessages(response.data.rows);
      setMassages(sortedMessages);
    } catch (error) {
      showTostMessage('something went wrong');
    }
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async function showMessageNotifaction(data: any) {
    const channelId = await notifee.createChannel({
      id: 'default',
      name: 'sms',
    });

    // Display a notification
    await notifee.displayNotification({
      title: 'New Message',
      body: data?.data?.message?.message || 'ssss',
      android: {
        channelId,
      },
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleNewMessage = (data: any) => {
    if (appStateRef.current === 'background') {
      showMessageNotifaction(data);
    }
    if (
      currentOpenChat === null &&
      data?.data?.type === 'user' &&
      data?.data?.message?.chat_id
    ) {
      //!figure out how to fix this error
      setChatLists((prevChatLists: ChatList[]) => {
        const ind = prevChatLists.findIndex(
          chat => chat.id === data.data.message.chat_id,
        );

        if (ind > -1) {
          prevChatLists[ind].messages = [
            {
              id: data.data.message.id,
              message: data.data.message.message,
              createdAt: data.data.message.createdAt,
              message_read: data.data.message?.message_read,
            },
          ];
          prevChatLists[ind].unread_count = data.data.unreadCount;
        } else {
          webSocketConnection();
        }
        return sortRecentMessage(prevChatLists);
      });
    } else if (
      currentOpenChat === null &&
      data.data.type === 'group' &&
      data?.data?.message?.chat_id
    ) {
      //!figure out how to fix this error
      setGroupLists((prevGroupLists: GroupList[]) => {
        const ind = prevGroupLists.findIndex(
          Group => Group.id === data.data.message.chat_id,
        );

        if (ind > -1) {
          prevGroupLists[ind].messages = [
            {
              id: data.data.message.id,
              message: data.data.message.message,
              createdAt: data.data.message.createdAt,
              message_read: data.data.message?.message_read,
            },
          ];
          prevGroupLists[ind].unread_count = data.data.unreadCount;
        } else {
          webSocketConnection();
        }
        return sortGroupsByLastActive(prevGroupLists);
      });
    } else if (
      currentOpenChat !== null &&
      data?.data?.message?.chat_id === currentOpenChat?.id
    ) {
      setMassages(prevState => {
        if (prevState[0]?.chat_id === data?.data?.message?.chat_id) {
          const newMessage: ChatRow = {
            id: data?.data?.message?.id,
            chat_id: data?.data?.message?.chat_id,
            linked_id: data?.data?.message?.linked_id,
            media_id: data?.data?.message?.media_id,
            media: null,
            message_read: data?.data?.message?.message_read,
            message: data?.data?.message?.message,
            createdAt: data?.data?.message?.createdAt,
            updatedAt: data?.data?.message?.updatedAt,
            sender_id: data?.data?.message?.sender_id,
            message_delivered: data?.data?.query?.message_delivered,
            sender: {
              id: data?.data?.query?.sender?.id,
              username: data?.data?.query?.sender?.username,
              role: data?.data?.query?.sender?.role,
              is_banned: data?.data?.query?.sender?.is_banned,
              phone: data?.data?.query?.sender?.phone,
              avatar: data?.data?.query?.sender?.avatar || null,
            },
            linked_msg: data?.data?.message?.linked_msg || null,
          };
          const ind = prevState.findIndex(msg => msg.id === newMessage?.id);
          if (ind > -1) {
            return prevState;
          } else {
            return [...prevState, newMessage];
          }
        } else {
          const newMessage: ChatRow = {
            id: data?.data?.message?.id,
            chat_id: data?.data?.message?.chat_id,
            linked_id: data?.data?.message?.linked_id,
            media_id: data?.data?.message?.media_id,
            media: null,
            message_read: data?.data?.message?.message_read,
            message: data?.data?.message?.message,
            createdAt: data?.data?.message?.createdAt,
            updatedAt: data?.data?.message?.updatedAt,
            sender_id: data?.data?.message?.sender_id,
            message_delivered: data?.data?.query?.message_delivered,
            sender: {
              id: data?.data?.query?.sender?.id,
              username: data?.data?.query?.sender?.username,
              role: data?.data?.query?.sender?.role,
              is_banned: data?.data?.query?.sender?.is_banned,
              phone: data?.data?.query?.sender?.phone,
              avatar: data?.data?.query?.sender?.avatar || null,
            },
            linked_msg: data?.data?.message?.linked_msg || null,
          };
          return [...prevState, newMessage];
        }
      });
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function handleMediaMessage(data: any) {
    if (
      currentOpenChat !== null &&
      data?.data?.message?.chat_id === currentOpenChat.id
    ) {
      const newMessage: ChatRow = {
        id: data?.data?.message?.id,
        chat_id: data?.data?.message?.chat_id,
        linked_id: data?.data?.message?.linked_id,
        media_id: data?.data?.message?.media_id,
        media: {
          id: data?.data?.query?.media?.id,
          url: data?.data?.query?.media?.url,
          type: data?.data?.query?.media?.type,
        },
        message_read: data?.data?.message?.message_read,
        message: data?.data?.message?.message,
        createdAt: data?.data?.message?.createdAt,
        updatedAt: data?.data?.message?.updatedAt,
        sender_id: data?.data?.message?.sender_id,
        message_delivered: false,
        sender: {
          id: data?.data?.query?.sender?.id,
          username: data?.data?.query?.sender?.username,
          role: data?.data?.query?.sender?.role,
          is_banned: data?.data?.query?.sender?.is_banned,
          phone: data?.data?.query?.sender?.phone,
          avatar: data?.data?.query?.sender?.avatar || null,
        },
        linked_msg: data?.data?.message?.linked_msg || null,
      };
      setMassages(prevState => {
        const ind = prevState.findIndex(msg => msg.id === newMessage?.id);
        if (ind > -1) {
          return prevState;
        } else {
          return [...prevState, newMessage];
        }
      });
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function handleMessageSeen(data: any) {
    setMassages(prevMessages => {
      return prevMessages.map(message => {
        if (
          message.chat_id === data?.data?.chatId &&
          message.sender_id !== data?.data?.userSeenDetails.user_id
          //&& message.id === data?.data?.messageId
        ) {
          const msg_read = message.message_read ? message.message_read : [];
          if (
            msg_read.filter(
              (m: MessageRead) =>
                m.user_id === data?.data?.userSeenDetails.user_id,
            ).length === 0
          ) {
            msg_read.push(data?.data?.userSeenDetails);
            return {...message, message_read: msg_read};
          }
          return message;
        }
        return message;
      });
    });
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function handleScrolling(data: any) {
    console.log(data);
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const socketEventHandler = (event: WebSocketMessageEvent) => {
    try {
      const data = JSON.parse(event.data);

      switch (data.type) {
        case 'new_connection': {
          setChatLists(sortRecentMessage(data.data.chatList));
          const sortedGroups = sortGroupsByLastActive(data.data.groupList);
          setGroupLists(sortedGroups);
          break;
        }

        case 'oldMessage':
          {
            handleNewConnection(data);
          }
          break;
        case 'new_message':
          handleNewMessage(data);
          break;

        case 'media_message':
          handleMediaMessage(data);
          break;

        case 'message_seen':
          handleMessageSeen(data);
          break;

        case 'scrolling':
          handleScrolling(data);
          break;

        default: {
          // console.warn(
          //   'Unhandled message type in side the web socket provider:::---',
          //   data.type,
          // );
        }
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  };

  const webSocketConnection = useCallback(() => {
    const ws = globalSocket();
    const handleOpen = () => {
      console.log('WebSocket connection opened');
      setSocket(ws);
      setIsConnected(true);
    };

    const handleClose = () => {
      console.log('WebSocket connection closed');
      setSocket(null);
      setIsConnected(true);
    };

    const handleError = (error: WebSocketCloseEvent) => {
      setIsConnected(false);
      if (
        error.message ===
        "Expected HTTP 101 response but was '401 Unauthorized'"
      ) {
        setShowAlert(true);
      }
    };

    ws.addEventListener('open', handleOpen);
    ws.addEventListener('message', socketEventHandler);
    ws.addEventListener('close', handleClose);
    ws.addEventListener('error', handleError);

    return () => {
      ws.removeEventListener('open', handleOpen);
      ws.removeEventListener('message', socketEventHandler);
      ws.removeEventListener('close', handleClose);
      ws.removeEventListener('error', handleError);
      ws.close();
    };
  }, [socketEventHandler]);

  useEffect(() => {
    if (currentOpenChat !== null || firstTimeLoad) {
      if (firstTimeLoad) {
        setFirstTimeLoad(false);
      }
      webSocketConnection();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentOpenChat]);

  function logoutUser() {
    while (navigation.canGoBack()) {
      navigation.goBack();
    }
    navigation.navigate('welcome');
    cleanLocalCache();
    setShowAlert(false);
  }
  const cancelCallNotification = async (notificationId: string) => {
    try {
      await notifee.cancelNotification(notificationId);
    } catch (error) {
      console.error('Error canceling call notification:', error);
    }
  };

  useEffect(() => {
    notifee.onBackgroundEvent(async ({type, detail}) => {
      if (type === EventType.PRESS) {
        const {notification} = detail;
        if (notification?.id) {
          navigation.navigate('voiceCall', {
            id: notification.id,
            userName: notification.title,
            isIncomingCall: true,
          });
          cancelCallNotification(notification.id);
        }
      }
    });
  }, []);

  useEffect(() => {
    const handleAppStateChange = nextAppState => {
      setAppState(nextAppState);
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription.remove();
    };
  }, []);

  messaging().setBackgroundMessageHandler(async remoteMessage => {
    console.log(
      JSON.stringify(remoteMessage),
      'background eventt t t t t 1111111111111111111',
    );
  });

  return (
    <WebSocketContext.Provider
      value={{
        socket,
        isConnected,
        messages,
        setCurrentOpenChat,
        setRemoteUserId,
        remoteUserId,
        setMassages,
        reconnect: webSocketConnection,
        currentOpenChat,
        appState,
      }}>
      <WebRTCProvider socket={socket}>
        {children}
        {showAlert && (
          <Alert
            visible={showAlert}
            title="Logged Out"
            message={`You have been logged out because your account was logged in on another device.`}
            onClose={logoutUser}
            onConfirm={logoutUser}
            closeIcon={false}
            rightText="Login again"
            showLeftText={false}
          />
        )}
      </WebRTCProvider>
    </WebSocketContext.Provider>
  );
}
