import * as React from 'react';
import {StyleSheet, View, ViewProps} from 'react-native';

export interface RowProps extends ViewProps {
  spread?: boolean;
  between?: boolean;
  center?: boolean;
  middle?: boolean;
  end?: boolean;
  reverse?: boolean;
}

export default function Row({
  center,
  middle,
  spread,
  between,
  end,
  style,
  children,
  reverse,
  ...viewProps
}: RowProps) {
  return (
    <View
      {...viewProps}
      style={[
        styles.container,
        spread ? styles.spread : {},
        between ? styles.between : {},
        center ? styles.center : {},
        middle ? styles.middle : {},
        end ? styles.end : {},
        reverse ? styles.reverse : {},
        style,
      ]}>
      {children}
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  spread: {
    justifyContent: 'space-between',
  },
  between: {
    justifyContent: 'space-around',
  },
  center: {
    alignItems: 'center',
  },
  middle: {
    justifyContent: 'center',
  },
  end: {
    justifyContent: 'flex-end',
  },
  reverse: {
    flexDirection: 'row-reverse',
  },
});
