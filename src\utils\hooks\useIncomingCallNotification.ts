import {Notification} from '@notifee/react-native';
import notifee, {
  AndroidImportance,
  AndroidCategory,
  EventType,
  AndroidVisibility,
} from '@notifee/react-native';
import {useEffect, useCallback} from 'react';

interface CallNotificationProps {
  name: string;
  avatarUrl?: string;
}

interface UseCallNotificationsProps {
  onAnswer?: (notification: Notification) => void;
  onDecline?: (notification: Notification) => void;
}

const CALL_CHANNEL_ID = 'incoming_calls';

const initializeCallChannel = async () => {
  try {
    await notifee.createChannel({
      id: CALL_CHANNEL_ID,
      name: 'Incoming Calls',
      importance: AndroidImportance.HIGH,
      vibration: true,
      vibrationPattern: [300, 500, 300, 100, 200, 400, 200, 100],
      lights: true,
      visibility: AndroidVisibility.PUBLIC,
    });
  } catch (error) {
    console.error('Error initializing call channel:', error);
  }
};

const showIncomingCall = async ({name, avatarUrl}: CallNotificationProps) => {
  try {
    return await notifee.displayNotification({
      id: `call-${Date.now()}`,
      title: 'Incoming Call',
      body: `${name} is calling...`,
      android: {
        channelId: CALL_CHANNEL_ID,
        category: AndroidCategory.CALL,
        importance: AndroidImportance.HIGH,
        lightUpScreen: true,
        autoCancel: false,
        showTimestamp: true,
        ongoing: true,
        fullScreenAction: {
          id: 'full_screen',
          launchActivity: 'default',
        },
        pressAction: {
          id: 'default',
        },
      },
      ios: {
        categoryId: 'incoming_call',
        critical: true,
        criticalVolume: 1.0,
        sound: 'ringtone',
        attachments: avatarUrl
          ? [
              {
                url: avatarUrl,
                thumbnailClippingRect: {x: 0, y: 0, width: 1, height: 1},
              },
            ]
          : undefined,
      },
    });
  } catch (error) {
    console.error('Error showing incoming call notification:', error);
  }
};

const cancelCallNotification = async (notificationId: string) => {
  try {
    await notifee.stopForegroundService();
    await notifee.cancelNotification(notificationId);
  } catch (error) {
    console.error('Error canceling call notification:', error);
  }
};

export const useCallIncomingNotifications = ({
  onAnswer,
  onDecline,
}: UseCallNotificationsProps) => {
  const handleAnswer = useCallback(
    (notification: Notification) => {
      onAnswer?.(notification);

      if (notification?.id) {
        cancelCallNotification(notification.id);
      }
    },
    [onAnswer],
  );

  const handleDecline = useCallback(
    (notification: Notification) => {
      onDecline?.(notification);
      if (notification?.id) {
        cancelCallNotification(notification.id);
      }
    },
    [onDecline],
  );

  useEffect(() => {
    initializeCallChannel();
  }, []);

  useEffect(() => {
    const unsubscribe = notifee.onForegroundEvent(({type, detail}) => {
      if (type === EventType.ACTION_PRESS) {
        const {pressAction, notification} = detail;

        switch (pressAction?.id) {
          case 'answer':
            if (notification) {
              handleAnswer(notification);
            }
            break;
          case 'decline':
            if (notification) {
              handleDecline(notification);
            }
            break;
        }
      }
    });

    return () => unsubscribe();
  }, [handleAnswer, handleDecline]);

  return {
    showIncomingCall,
    cancelCallNotification,
  };
};
