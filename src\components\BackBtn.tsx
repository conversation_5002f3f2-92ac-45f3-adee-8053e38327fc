import * as React from 'react';
import {Pressable, StyleSheet} from 'react-native';
import {colors} from '../design/colors';
import {useNavigation} from '@react-navigation/native';
import {Back} from '../assets/icons';

export default function BackBtn() {
  const navigator = useNavigation();

  function navigateToGoBack() {
    navigator.goBack();
  }
  return (
    <Pressable style={style.back} onPress={navigateToGoBack}>
      <Back fill={colors.icon.black} />
    </Pressable>
  );
}

const style = StyleSheet.create({
  back: {
    height: 28,
    width: 28,
    // backgroundColor: colors.borders.default,
    alignItems: 'center',
    borderRadius: 100,
    justifyContent: 'center',
  },
});
