import {View, FlatList, Pressable, StyleSheet} from 'react-native';
import React from 'react';
import Spacer from './Spacer';
import Image from './Image';
import Row from './Row';
import {colors} from '../design/colors';
import {Cross} from '../assets/icons';
import {StoriesType} from '../utils';
import {MEDIA_BASE_URL_DEBUG, MEDIA_BASE_URL_RELEASE} from '@env';

interface Props {
  userType: string;
  onPressStatus: (item: StoriesType, index: number) => void;
  onCreateStatus: () => void;
  story?: StoriesType[];
}

const keyExtractor = (item: StoriesType, iex: number) => item?.user?.id + iex;
const MEDIA_BASE_URL = __DEV__ ? MEDIA_BASE_URL_DEBUG : MEDIA_BASE_URL_RELEASE;

function Status({userType, onPressStatus, story, onCreateStatus}: Props) {
  const renderItem = React.useCallback(
    ({item, index}: {item: StoriesType; index: number}) => {
      return (
        <Pressable
          key={item?.user?.id + index}
          style={styles.flatListView}
          onPress={() => onPressStatus(item, index)}>
          <Image
            uri={`${MEDIA_BASE_URL}/${item?.user?.name}`}
            alt={item?.user?.name}
            style={{height: 48, width: 48}}
          />
        </Pressable>
      );
    },
    [story],
  );

  return (
    <Row center>
      {userType !== 'user' && (
        <>
          <Pressable onPress={onCreateStatus}>
            <Image uri={'https://avatar.iran.liara.run/public'} alt="abd" />
            <View style={styles.view}>
              <Cross
                height={12}
                width={12}
                fill={'#fff'}
                style={styles.crossIcon}
              />
            </View>
          </Pressable>
          <Spacer width={12} />
        </>
      )}
      <FlatList
        data={story}
        horizontal
        keyExtractor={keyExtractor}
        showsHorizontalScrollIndicator={false}
        ItemSeparatorComponent={() => <Spacer width={12} />}
        renderItem={renderItem}
      />
    </Row>
  );
}
const styles = StyleSheet.create({
  crossIcon: {
    transform: [
      {
        rotate: '45deg',
      },
    ],
  },
  view: {
    backgroundColor: colors.primaryB200,
    alignSelf: 'center',
    borderRadius: 100,
    padding: 4,
    position: 'absolute',
    right: -4,
    bottom: -4,
  },
  flatListView: {
    borderColor: colors.primaryB300,
    borderWidth: 2,
    borderRadius: 100,
    padding: 4,
  },
});

export default React.memo(Status);
