import * as React from 'react';
import {ScrollView, ScrollViewProps, StyleSheet} from 'react-native';

export default function KeyboardAvoidingView({
  children,
  style,
  contentContainerStyle,
  ...restProps
}: ScrollViewProps) {
  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="always"
      {...restProps}
      style={[styles.scroll, style]}
      contentContainerStyle={[styles.scrollContent, contentContainerStyle]}>
      {children}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scroll: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 100,
  },
});
