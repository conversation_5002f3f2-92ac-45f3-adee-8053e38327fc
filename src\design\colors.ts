const themeColors = {
  primaryB500: '#003E9C',
  primaryB400: '#0047B3',
  primaryB300: '#0065FF',
  primaryB200: '#2B7FFF',
  primaryB100: '#6BA6FF',
  primaryB75: '#96C0FF',
  primaryB50: '#E6f0FF',

  textB0: '#000000',
  textB10: '#D5D5D5',
  textB20: '#B8B8B8',
  textB30: '#959595',
  textB40: '#727272',
  textB50: '#4E4E4E',
  textB60: '#242424',
  textB70: '#1D1D1D',
  textB80: '#161616',
  textB90: '#0E0E0E',
  textB100: '#090909',

  success: '#10C600',
  error: '#FF5F5F',
  warning: '#FFC700',

  white: '#FFFFFF',
  black: '#000000',

  disable: '#454545',
};

export const colors = {
  text: {
    primary: themeColors.primaryB300,
    warning: themeColors.warning,
    success: themeColors.success,
    error: themeColors.error,
    white: themeColors.white,
    black: themeColors.black,
    B50: themeColors.textB50,
    B20: themeColors.textB20,
    B100: themeColors.primaryB100,
    B40: themeColors.textB40,
  },

  borders: {
    B20: themeColors.textB20,
    B50: themeColors.textB50,
  },

  primaryB500: themeColors.primaryB500,
  primaryB400: themeColors.primaryB400,
  primaryB300: themeColors.primaryB300,
  primaryB200: themeColors.primaryB200,
  primaryB100: themeColors.primaryB100,
  primaryB75: themeColors.primaryB75,
  primaryB50: themeColors.primaryB50,

  textB0: themeColors.textB0,
  textB10: themeColors.textB10,
  textB20: themeColors.textB20,
  textB30: themeColors.textB30,
  textB40: themeColors.textB40,
  textB50: themeColors.textB50,
  textB60: themeColors.textB60,
  textB70: themeColors.textB70,
  textB80: themeColors.textB80,
  textB90: themeColors.textB90,
  textB100: themeColors.textB100,

  white: themeColors.white,
  black: themeColors.black,

  Cta: {
    active: themeColors.primaryB200,
    pressed: themeColors.primaryB400,
    primaryB400: themeColors.primaryB400,
    disabled: themeColors.disable,
    success: themeColors.success,
    danger: themeColors.error,
    white: themeColors.white,
    primaryB300: themeColors.primaryB300,
  },

  icon: {
    black: themeColors.black,
    B100: themeColors.primaryB100,
    B30: themeColors.textB30,
    B200: themeColors.primaryB200,
    white: themeColors.white,
    danger: themeColors.error,
    B50: themeColors.textB50,
    B20: themeColors.textB20,
    B10: themeColors.textB10,
    PB50: themeColors.primaryB50,
  },

  success: themeColors.success,
  error: themeColors.error,
  warning: themeColors.warning,
};
