import React from 'react';
import {DoubleTick, Sent} from '../assets/icons';
import {colors} from '../design/colors';

interface Props {
  massageType: 'sender' | 'receiver';
  massageStatus: 'sent' | 'delivered' | 'seen' | undefined;
}
export default function ReadReceipt({massageType, massageStatus}: Props) {
  if (massageType !== 'sender') return <></>;

  switch (massageStatus) {
    case 'sent':
      return <Sent height={16} width={16} fill={colors.icon.B100} />;
    case 'delivered':
    case 'seen':
      return (
        <DoubleTick
          height={16}
          width={16}
          fill={massageStatus === 'seen' ? colors.icon.B100 : colors.icon.B30}
        />
      );
    default:
      return <></>;
  }
}
