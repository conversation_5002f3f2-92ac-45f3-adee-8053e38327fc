import {BlurView} from '@react-native-community/blur';
import * as React from 'react';
import {
  Modal as RNModal,
  ModalProps as RNModalProps,
  StyleSheet,
} from 'react-native';

export interface ModalProps extends Omit<RNModalProps, 'onDismiss'> {
  onClose: () => void;
  animationType?: 'none' | 'slide' | 'fade';
  visible: boolean;
}

export default function Modal({
  onClose,
  style,
  animationType = 'slide',
  visible,
  ...props
}: ModalProps) {
  return (
    <RNModal
      visible={visible}
      animationType={animationType}
      transparent={false}
      onRequestClose={onClose}
      {...props}
      style={[styles.container, style]}>
      <BlurView style={styles.blurView} blurType="dark" blurAmount={1} />
      {props.children}
    </RNModal>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: 'pink',
    borderRadius: 20,
  },
  backdrop: {
    backgroundColor: 'yellow',
  },
  content: {
    marginTop: 78,
  },
  blurView: {
    ...StyleSheet.absoluteFillObject,
  },
});
