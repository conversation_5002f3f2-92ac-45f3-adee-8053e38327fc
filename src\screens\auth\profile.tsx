import {Image, View, StyleSheet} from 'react-native';
import React from 'react';
import {
  BodyCard,
  Text,
  BackBtn,
  Spacer,
  Cta,
  KeyboardAvoidingView,
  Row,
} from '../../components';
import {colors} from '../../design/colors';
import {useStore} from '../../utils/hooks';
import {ContactTabIcon, Phone} from '../../assets/icons';
import {RootStack} from '../../utils';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';

export default function Profile() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {user} = useStore(state => ({user: state.user}));
  function navigateToProfileUpdate() {
    navigation.navigate('ProfileSetting');
  }
  return (
    <BodyCard padBottom padTop>
      <KeyboardAvoidingView>
        <Row>
          <BackBtn />
          <Text variant="H2_500">Profile</Text>
        </Row>
        <Spacer height={2} />
        <Text variant="subText2" color={colors.text.B50}>
          You can view your user profile and related data here.
        </Text>
        <Spacer height={112} />
        <View style={[styles.profileCard]}>
          <View style={styles.centeredContainer}>
            <Row style={styles.imageContainer}>
              <Image
                source={
                  user?.url
                    ? {uri: user.url}
                    : require('../../assets/images/avatar.png')
                }
                alt="hero123"
                style={styles.profileImage}
              />
            </Row>
          </View>
          <View style={styles.formContainer}>
            <Text variant="subText1">
              Name{'\n'}
              {user?.name}
            </Text>
            <Spacer height={24} />
            <View>
              <Row>
                <Phone />
                <Text variant="subText1">Phone number</Text>
              </Row>
              <Text variant="subText1">+91 {user?.phone}</Text>
              <Text variant="subText2" color={colors.text.B40}>
                You can change your phone number only once.{'\n'}You won’t be
                able to make changes later.
              </Text>
            </View>
            <Spacer height={24} />
            <View>
              <Row>
                <ContactTabIcon height={20} width={20} />
                <Text variant="subText1">Username</Text>
              </Row>
              <Text variant="subText1">@{user?.username}</Text>
              <Text variant="subText2" color={colors.text.B40}>
                You can change your username only once.{'\n'}You won’t be able
                to make changes later.
              </Text>
            </View>
            <Spacer height={24} />
            <View>
              <Row>
                <Text variant="subText1">Bio</Text>
              </Row>
              <Text variant="subText1">{user?.bio}</Text>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
      <Spacer height={56} />
      <Cta title="Edit Profile" onPress={navigateToProfileUpdate} />
    </BodyCard>
  );
}

const styles = StyleSheet.create({
  profileCard: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.borders.B20,
    padding: 18,
    alignItems: 'center',
    backgroundColor: 'white',
    elevation: 4,
    shadowOffset: {
      width: 4,
      height: 1,
    },
    shadowRadius: 3.84,
    shadowOpacity: 0.5,
    height: '60%',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    bottom: 75,
  },
  formContainer: {
    width: '100%',
    bottom: 90,
  },
  centeredContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: 'auto',
  },
  imageContainer: {
    width: 86,
    height: 86,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 16,
  },
  cameraIconWrapper: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    borderRadius: 20,
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
