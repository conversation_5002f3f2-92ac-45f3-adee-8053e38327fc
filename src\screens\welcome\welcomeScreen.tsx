import {View, StyleSheet} from 'react-native';
import React, {useEffect} from 'react';
import {Text, Cta, Spacer, BodyCard} from '../../components';
import {useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../../utils/types';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import {Logo} from '../../assets/icons';

export default function WelcomeScreen() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();
  const handleRegister = () => {
    navigation.navigate('login');
  };

  const rotationAnimation = useSharedValue(0);

  useEffect(() => {
    rotationAnimation.value = withRepeat(
      withSequence(
        withTiming(25, {duration: 150}),
        withTiming(0, {duration: 150}),
      ),
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{rotate: `${rotationAnimation.value}deg`}],
  }));
  return (
    <BodyCard padTop padBottom>
      <View style={styles.container}>
        <Logo style={{alignSelf: 'center'}} />
        <Spacer height={48} />
        <View style={{flex: 1}}>
          <Text
            style={styles.heading}
            numberOfLines={2}
            variant="H1_500"
            center>
            Hey
            <Animated.View style={animatedStyle}>
              <Text variant="H1_500">👋 </Text>
            </Animated.View>
            {'\n'}Welcome to N2chat
          </Text>
          <Spacer height={14} />
          <Text
            style={styles.paragraph}
            numberOfLines={2}
            variant="subText1"
            center>
            Lorem ipsum dolor sit amet consectetur.{'\n'}
            Pharetra ac nunc quisque
          </Text>
        </View>
      </View>
      <Cta title="Get Started" onPress={handleRegister} />
    </BodyCard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  heading: {
    fontFamily: 'GoogleSans-Medium',
  },
  paragraph: {
    fontFamily: 'GoogleSans-Regular',
  },
});
