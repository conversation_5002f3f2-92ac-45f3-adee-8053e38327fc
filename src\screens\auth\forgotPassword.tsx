import * as React from 'react';
import {BackBtn, BodyCard, Text, Input, Spacer, Cta} from '../../components';
import {useForm} from 'react-hook-form';
import {z} from 'zod';
import {zodResolver} from '@hookform/resolvers/zod';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {RootStack} from '../../utils/types';
import {postUserOtp} from '../../utils/apis/postApis';
import {Alert} from 'react-native';

const schema = z.object({
  phone: z
    .string()
    .min(10, 'Phone number must be at most 10 digits')
    .max(10, 'Phone number must be at most 10 digits')
    .regex(/^\d+$/, 'Phone number must only contain digits'),
});

type FormData = z.infer<typeof schema>;
type PasswordScreenRouteProp = RouteProp<RootStack, 'password'>;

export default function ForgotPassword() {
  const navigation = useNavigation<NativeStackNavigationProp<RootStack>>();

  const {control, handleSubmit} = useForm<FormData>({
    resolver: zodResolver(schema),
  });

  const {
    params: {userRole, isSuperUser, isSuperAdmin},
  } = useRoute<PasswordScreenRouteProp>();

  async function handleForgotPassword({phone}: FormData) {
    try {
      const response = await postUserOtp(phone);
      const data = await response.json();
      if (data.message) {
        if (__DEV__) {
          Alert.alert('Message', `Your otp is ${data.message}`);
        }
        if (data.message) {
          navigation.navigate('otp', {
            phone: phone,
            userRole: userRole,
            isSuperAdmin: isSuperAdmin,
            isSuperUser: isSuperUser,
          });
        }
      }
    } catch (e) {
      Alert.alert('Error', 'Something went wrong!');
    }
  }

  return (
    <BodyCard>
      <BackBtn />
      <Text center variant="H1">
        Forgot Password
      </Text>
      <Spacer height={20} />
      <Input
        control={control}
        name="phone"
        label="Phone number"
        placeholder="Enter your phone number"
        maxLength={10}
        keyboardType="number-pad"
      />
      <Spacer height={20} />
      <Cta
        appearance="primary"
        title="Send OTP"
        onPress={handleSubmit(handleForgotPassword)}
      />
    </BodyCard>
  );
}
